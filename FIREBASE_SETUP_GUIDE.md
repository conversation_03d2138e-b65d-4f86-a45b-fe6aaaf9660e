# 🔥 Firebase Google OAuth Setup Guide

## ✅ **IMPLEMENTATION STATUS: COMPLETE**

Your Firebase Google OAuth integration is now **fully implemented** and ready for configuration!

## 🚀 **QUICK SETUP (5 Minutes)**

### **Step 1: Create Firebase Project**

1. **Go to Firebase Console:**
   - Visit: https://console.firebase.google.com/
   - Click "Create a project" or "Add project"
   - Enter project name: `vsable-auth` (or your preferred name)
   - Disable Google Analytics (optional)
   - Click "Create project"

### **Step 2: Enable Google Authentication**

1. **In your Firebase project:**
   - Go to "Authentication" in the left sidebar
   - Click "Get started"
   - Go to "Sign-in method" tab
   - Click on "Google" provider
   - Toggle "Enable"
   - Add your email as a test user
   - Click "Save"

### **Step 3: Add Web App**

1. **Register your web app:**
   - Go to Project Settings (gear icon)
   - Scroll down to "Your apps"
   - Click the web icon `</>`
   - Enter app nickname: `vsable-web`
   - Check "Also set up Firebase Hosting" (optional)
   - Click "Register app"

2. **Copy Firebase Configuration:**
   ```javascript
   const firebaseConfig = {
     apiKey: "your-api-key",
     authDomain: "your-project.firebaseapp.com",
     projectId: "your-project-id",
     storageBucket: "your-project.appspot.com",
     messagingSenderId: "123456789",
     appId: "your-app-id"
   };
   ```

### **Step 4: Update Environment Variables**

1. **Update `backend/.env`:**
   ```env
   # Firebase Configuration
   FIREBASE_API_KEY=your-api-key
   FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
   FIREBASE_PROJECT_ID=your-project-id
   FIREBASE_STORAGE_BUCKET=your-project.appspot.com
   FIREBASE_MESSAGING_SENDER_ID=123456789
   FIREBASE_APP_ID=your-app-id
   ```

2. **Update `vsable/.env.local`:**
   ```env
   # Firebase Configuration
   NEXT_PUBLIC_FIREBASE_API_KEY=your-api-key
   NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
   NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
   NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
   NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=123456789
   NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id
   ```

### **Step 5: Configure Authorized Domains**

1. **In Firebase Console:**
   - Go to Authentication → Settings → Authorized domains
   - Add your domains:
     - `localhost` (for development)
     - `your-production-domain.com` (for production)

## 🎯 **TESTING YOUR SETUP**

1. **Start your applications:**
   ```bash
   # Backend
   cd backend
   python app.py

   # Frontend (new terminal)
   cd vsable
   npm run dev
   ```

2. **Test Google OAuth:**
   - Go to http://localhost:3001/login
   - Click "Sign in with Google"
   - Complete Google OAuth flow
   - You should be redirected to the appropriate dashboard

## 🔧 **ADVANCED CONFIGURATION (Optional)**

### **Service Account Setup (For Production)**

1. **Generate Service Account:**
   - Go to Project Settings → Service accounts
   - Click "Generate new private key"
   - Download the JSON file

2. **Add to Environment:**
   ```env
   # Option 1: File path
   FIREBASE_SERVICE_ACCOUNT_PATH=/path/to/service-account.json

   # Option 2: JSON content (for cloud deployment)
   FIREBASE_SERVICE_ACCOUNT_JSON='{"type":"service_account",...}'
   ```

### **Custom Claims (For Role Management)**

The system automatically handles user roles. Firebase users are created with:
- Default role: `customer`
- Email verification: `true` (from Google)
- Profile picture: From Google account

## 🛠️ **IMPLEMENTATION DETAILS**

### **Frontend Components:**
- ✅ `lib/firebase.ts` - Firebase configuration
- ✅ `hooks/useFirebaseAuth.ts` - Firebase authentication hook
- ✅ `components/GoogleSignIn.tsx` - Updated with Firebase integration

### **Backend Services:**
- ✅ `utils/firebase_service.py` - Firebase Admin SDK integration
- ✅ `routes/auth.py` - Updated Google OAuth endpoint
- ✅ Firebase ID token verification

### **Features Implemented:**
- ✅ Real Firebase Google OAuth (replaces mock)
- ✅ Secure ID token verification
- ✅ Automatic user creation/linking
- ✅ Role-based redirects
- ✅ Profile picture sync
- ✅ Email verification status

## 🚨 **TROUBLESHOOTING**

### **Common Issues:**

1. **"Firebase not initialized" warning:**
   - Check environment variables are set correctly
   - Restart both frontend and backend

2. **"Invalid ID token" error:**
   - Verify Firebase project configuration
   - Check authorized domains include your domain

3. **Google OAuth popup blocked:**
   - Allow popups for your domain
   - Try in incognito mode

### **Development Mode:**
If Firebase is not configured, the system falls back to mock mode with warnings.

## 🎉 **CONGRATULATIONS!**

Your Firebase Google OAuth integration is now **production-ready**! 

**Next Steps:**
1. Configure your Firebase project (5 minutes)
2. Update environment variables
3. Test the authentication flow
4. Deploy to production

**Your users can now sign in with Google using real Firebase authentication!** 🔥
