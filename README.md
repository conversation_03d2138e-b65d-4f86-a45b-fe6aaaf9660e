# Multi-Vendor Platform

A full-stack multi-vendor platform with Flask/PostgreSQL backend and Next.js/TypeScript frontend.

## Features

- User authentication (login, logout, register) with remember me functionality
- Role-based access control (customer, vendor, admin)
- Secure cookie handling for production
- Public storefront for customers
- Vendor dashboard for business owners
- Admin panel for platform management
- Responsive design with dark mode support

### Store Management
- Create and edit store profiles
- Customize store name, description, and logo
- Add contact methods (Telegram, WhatsApp, phone, Instagram)
- Preview store as customers would see it

### Product Catalog
- Add, edit, and delete products
- Set product name, description, price, and image
- Categorize products with tags
- Enable/disable products
- Automatic "Contact to Buy" links via Telegram

### Customer Experience
- Browse vendor stores
- Search and filter products
- View product details
- Contact vendors directly via Telegram

## Project Structure

```
multi-vendor-platform/
│
├── backend/                      # Flask + PostgreSQL backend
│   ├── models/                   # Database models
│   │   └── user.py               # User model
│   ├── routes/                   # API routes
│   │   ├── auth.py               # Authentication routes
│   │   ├── vendor.py             # Vendor routes
│   │   └── admin.py              # Admin routes
│   ├── app.py                    # Main Flask application
│   ├── extensions.py             # Flask extensions
│   └── requirements.txt          # Python dependencies
│
├── frontend/                     # Next.js + TypeScript frontend
│   ├── public/                   # Static assets
│   ├── src/
│   │   ├── app/                  # Next.js app router
│   │   │   ├── page.tsx          # Home page
│   │   │   ├── login/            # Login page
│   │   │   ├── register/         # Registration page
│   │   │   ├── vendor/           # Vendor pages
│   │   │   ├── admin/            # Admin pages
│   │   │   └── store/            # Store pages
│   │   ├── components/           # Reusable components
│   │   │   ├── Navbar.tsx        # Navigation bar
│   │   │   └── ProtectedRoute.tsx # Auth protection
│   │   ├── contexts/             # React contexts
│   │   │   └── AuthContext.tsx   # Authentication context
│   │   └── styles/               # Global styles
│   └── package.json              # Node.js dependencies
```

## Getting Started

### Prerequisites

- Python 3.8+
- Node.js 18+
- PostgreSQL

### Backend Setup

1. Navigate to the backend directory:
   ```
   cd backend
   ```

2. Create a virtual environment:
   ```
   python -m venv venv
   ```

3. Activate the virtual environment:
   - Windows: `venv\Scripts\activate`
   - macOS/Linux: `source venv/bin/activate`

4. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

5. Set up environment variables (create a `.env` file):
   ```
   SECRET_KEY=your_secret_key_here
   DB_HOST=localhost
   DB_PORT=5432
   DB_DATABASE=test_dev
   DB_USER=postgres
   DB_PASSWORD=Chettra2020
   FLASK_APP=app.py
   FLASK_ENV=development
   ```

6. Create the PostgreSQL database:
   ```
   # Connect to PostgreSQL and create the database
   psql -U postgres
   CREATE DATABASE test_dev;
   \q
   ```

7. Initialize the database:
   ```
   # Initialize the database schema and create sample users
   python backend/init_db.py
   ```

8. Run the Flask application:
   ```
   flask run
   ```

The backend will be available at http://localhost:5000/api.

### Frontend Setup

1. Navigate to the frontend directory:
   ```
   cd frontend/vsable
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Create a `.env.local` file:
   ```
   NEXT_PUBLIC_API_URL=http://localhost:5000/api
   ```

   This file has already been created for you.

4. Run the development server:
   ```
   npm run dev
   ```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Sample Users

The initialization script creates the following sample users that you can use to test the application:

1. **Admin User**
   - Email: <EMAIL>
   - Password: admin123
   - Role: admin

2. **Vendor User**
   - Email: <EMAIL>
   - Password: vendor123
   - Role: vendor

3. **Customer User**
   - Email: <EMAIL>
   - Password: customer123
   - Role: customer

## Deployment

### Backend Deployment

1. Set up a production PostgreSQL database
2. Configure environment variables for production:
   ```
   SECRET_KEY=your_secure_secret_key
   DATABASE_URL=your_production_database_url
   FLASK_ENV=production
   ```
3. Use Gunicorn as a WSGI server:
   ```
   gunicorn app:app
   ```

### Frontend Deployment

1. Build the Next.js application:
   ```
   npm run build
   ```
2. Deploy the built application to your hosting provider of choice (Vercel, Netlify, etc.)

## License

This project is licensed under the MIT License - see the LICENSE file for details.
