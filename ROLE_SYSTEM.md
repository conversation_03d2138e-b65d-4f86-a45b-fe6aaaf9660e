# 🎭 **Role-Based Authentication System**

## 📋 **Overview**

The Vsable platform supports three distinct user roles with different permissions and access levels:

### **🔵 Customer Role**
- **Purpose:** Browse and purchase products
- **Access:** 
  - Product catalog
  - Shopping cart
  - Order history
  - Profile management
- **Redirect:** `/` (Homepage)

### **🟢 Vendor Role**
- **Purpose:** Sell products and manage online store
- **Access:**
  - Vendor dashboard
  - Product management
  - Store settings
  - Order management
  - Analytics
- **Redirect:** `/vendor/dashboard`

### **🟣 Admin Role**
- **Purpose:** Platform administration and user management
- **Access:**
  - Admin dashboard
  - User management
  - Platform settings
  - System analytics
  - Content moderation
- **Redirect:** `/admin/dashboard`

## 🚀 **How Role Assignment Works**

### **1. Regular Registration**
- Users select their role during signup
- Role is validated on the backend
- Default role: `customer` if none specified

### **2. Google OAuth Registration**
- Users select role before clicking "Sign up with Google"
- Role is sent to backend with OAuth data
- Backend creates user with selected role

### **3. Role Validation**
- Backend validates all role assignments
- Only allows: `customer`, `vendor`, `admin`
- Invalid roles default to `customer`

## 🔧 **Implementation Details**

### **Frontend Components**

#### **RoleSelector Component**
```tsx
<RoleSelector
  selectedRole={role}
  onRoleChange={setRole}
  disabled={loading}
/>
```

#### **GoogleSignIn with Role**
```tsx
<GoogleSignIn 
  mode="signup" 
  role={role} 
  disabled={loading} 
/>
```

### **Backend API**

#### **Google OAuth Endpoint**
```
POST /api/auth/google
{
  "idToken": "firebase_id_token",
  "role": "customer|vendor|admin",
  "email": "<EMAIL>",
  "name": "User Name",
  "photoURL": "profile_picture_url"
}
```

#### **Regular Registration Endpoint**
```
POST /api/auth/register
{
  "name": "User Name",
  "email": "<EMAIL>",
  "password": "password",
  "role": "customer|vendor|admin"
}
```

## 🎯 **User Experience Flow**

### **New User Registration**
1. User visits `/register`
2. User fills in basic information
3. User selects role using RoleSelector
4. User can either:
   - Submit form for email/password registration
   - Click "Sign up with Google" (role is preserved)
5. User is redirected based on their role

### **Existing User Login**
1. User visits `/login`
2. User signs in (email/password or Google)
3. User is redirected based on their existing role

## 🔒 **Security Features**

### **Role Validation**
- All roles are validated on the backend
- Invalid roles are rejected or defaulted
- Role changes require admin privileges

### **Route Protection**
- Each dashboard is protected by role-based middleware
- Users can only access routes for their role
- Unauthorized access redirects to appropriate page

### **Token-Based Authentication**
- JWT tokens include user role
- Role is verified on each protected request
- Tokens expire and require renewal

## 📊 **Role Statistics**

The system tracks:
- User registration by role
- Role distribution across platform
- Role-based feature usage
- Authentication method preferences

## 🛠️ **Customization Options**

### **Adding New Roles**
1. Update role validation in backend
2. Add role option to RoleSelector component
3. Create appropriate dashboard/routes
4. Update redirect logic

### **Role Permissions**
- Roles can be extended with granular permissions
- Permission-based access control
- Feature flags per role

## 🎉 **Benefits**

✅ **Clear User Intent:** Users choose their purpose upfront
✅ **Personalized Experience:** Role-appropriate dashboards
✅ **Streamlined Onboarding:** Direct access to relevant features
✅ **Flexible Authentication:** Works with both email and Google OAuth
✅ **Scalable Architecture:** Easy to add new roles and permissions

---

**The role system ensures every user gets the right experience from day one!** 🚀
