# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Flask
instance/
.webassets-cache

# Database
*.sqlite
*.db

# Environment variables
.env
.env.*

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/
*.log

# Uploads directory (local file storage)
uploads/

# Alembic migrations (optional, you may want to keep these in version control)
# migrations/