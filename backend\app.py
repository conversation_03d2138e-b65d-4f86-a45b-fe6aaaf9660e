from flask import Flask, send_from_directory
import os
from datetime import <PERSON><PERSON><PERSON>
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

from extensions import db, migrate, cors
from models.user import User

def create_app(test_config=None):
    # Initialize Flask app
    app = Flask(__name__, instance_relative_config=True)

    @app.route("/")
    def home():
        return "<h1>Flask App is running!</h1>"
    # Configuration
    db_host = os.environ.get('DB_HOST')  # Google Cloud SQL public IP
    db_port = os.environ.get('DB_PORT')
    db_database = os.environ.get('DB_DATABASE')
    db_user = os.environ.get('DB_USER')
    db_password = os.environ.get('DB_PASSWORD')  # Use a secure password in production

    # For development, use SQLite if database environment variables are not set
    if not all([os.environ.get('DB_HOST'), os.environ.get('DB_PORT'), os.environ.get('DB_DATABASE')]):
        database_url = 'sqlite:///app.db'
        print("Using SQLite database for development")
    else:
        database_url = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_database}"
        print(f"Using PostgreSQL database: {database_url}")

    # Configure SQLAlchemy engine options based on database type
    engine_options = {}
    if database_url.startswith('postgresql://'):
        # PostgreSQL-specific options
        engine_options = {
            'pool_pre_ping': True,
            'pool_recycle': 300,
            'pool_timeout': 20,
            'max_overflow': 0
        }

    app.config.from_mapping(
        SECRET_KEY=os.environ.get('SECRET_KEY', 'dev_secret_key_change_in_production'),
        SQLALCHEMY_DATABASE_URI=database_url,
        SQLALCHEMY_TRACK_MODIFICATIONS=False,
        SQLALCHEMY_ENGINE_OPTIONS=engine_options,
        SESSION_COOKIE_SECURE=True,
        SESSION_COOKIE_HTTPONLY=True,
        SESSION_COOKIE_SAMESITE='Lax',
        PERMANENT_SESSION_LIFETIME=timedelta(days=30),
        # File upload settings
        MAX_CONTENT_LENGTH=50 * 1024 * 1024,  # 50MB max upload size
        UPLOADS_URL='/uploads/',  # URL prefix for uploads
        UPLOAD_FOLDER=os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads'),
        # MinIO configuration with defaults
        MINIO_ENDPOINT=os.environ.get('MINIO_ENDPOINT', '**************:9000'),
        MINIO_ACCESS_KEY=os.environ.get('MINIO_ACCESS_KEY', 'your-access-key'),
        # Email configuration with mock credentials
        SMTP_SERVER=os.environ.get('SMTP_SERVER', 'smtp.gmail.com'),
        SMTP_PORT=int(os.environ.get('SMTP_PORT', '587')),
        SMTP_USERNAME=os.environ.get('SMTP_USERNAME', '<EMAIL>'),
        SMTP_PASSWORD=os.environ.get('SMTP_PASSWORD', 'your-app-password'),
        FROM_EMAIL=os.environ.get('FROM_EMAIL', '<EMAIL>'),
        FROM_NAME=os.environ.get('FROM_NAME', 'Vsable'),
        # Google OAuth configuration with mock credentials
        GOOGLE_CLIENT_ID=os.environ.get('GOOGLE_CLIENT_ID', 'your-google-client-id.apps.googleusercontent.com'),
        GOOGLE_CLIENT_SECRET=os.environ.get('GOOGLE_CLIENT_SECRET', 'your-google-client-secret'),
        # Frontend URL for email links
        FRONTEND_URL=os.environ.get('FRONTEND_URL', 'http://localhost:3000'),
        MINIO_SECRET_KEY=os.environ.get('MINIO_SECRET_KEY', 'your-secret-key'),
        MINIO_SECURE=True,
        MINIO_BUCKET=os.environ.get('MINIO_BUCKET', 'uploads'),
        MINIO_URL=os.environ.get('MINIO_URL', 'https://**************:9000'),
        DEBUG=os.environ.get('FLASK_ENV') == 'development'
    )

    # Ensure MINIO_ENDPOINT_API is set
    minio_endpoint = app.config.get('MINIO_ENDPOINT')
    if minio_endpoint:
        if ':' in minio_endpoint:
            host = minio_endpoint.split(':')[0]
            app.config['MINIO_ENDPOINT_API'] = f"{host}:9000"
        else:
            app.config['MINIO_ENDPOINT_API'] = minio_endpoint
    else:
        app.config['MINIO_ENDPOINT_API'] = '**************:9000'

    print("\nMinIO Configuration:")
    print(f"  Endpoint API: {app.config.get('MINIO_ENDPOINT_API')}")
    print(f"  Access Key: {app.config.get('MINIO_ACCESS_KEY')}")
    print(f"  Secure: {app.config.get('MINIO_SECURE')}")
    print(f"  Bucket: {app.config.get('MINIO_BUCKET')}")
    print(f"  URL: {app.config.get('MINIO_URL')}")

    # Initialize extensions
    db.init_app(app)
    migrate.init_app(app, db)

    # Configure CORS
    cors.init_app(app,
                 supports_credentials=True,
                 origins=["https://vsable.vercel.app/", 'http://localhost:3000', 'http://127.0.0.1:3000', 'http://localhost:3001', 'http://127.0.0.1:3001', 'http://************:3000'],
                 expose_headers=['Content-Type', 'Authorization'],
                 allow_headers=['Content-Type', 'Authorization', 'Content-Disposition'],
                 methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])

    # Import routes
    from routes.auth import auth_bp
    from routes.vendor import vendor_bp
    from routes.admin import admin_bp
    from routes.store import store_bp
    from routes.product import product_bp
    from routes.upload import upload_bp

    # Register blueprints
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(vendor_bp, url_prefix='/api/vendor')
    app.register_blueprint(admin_bp, url_prefix='/api/admin')
    app.register_blueprint(store_bp, url_prefix='/api/store')
    app.register_blueprint(product_bp, url_prefix='/api/product')
    app.register_blueprint(upload_bp, url_prefix='/api/upload')

    # Create uploads directory if it doesn't exist
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'images'), exist_ok=True)
    os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'videos'), exist_ok=True)

    # Print storage configuration for debugging
    print("\nMinIO Configuration:")
    print(f"  Endpoint: {app.config.get('MINIO_ENDPOINT')}")
    print(f"  Access Key: {app.config.get('MINIO_ACCESS_KEY')}")
    # print(f"  Secret Key: {'*' * len(app.config.get('MINIO_SECRET_KEY', ''))}")
    print(f"  Secure: {app.config.get('MINIO_SECURE')}")
    print(f"  Bucket: {app.config.get('MINIO_BUCKET')}")
    print(f"  URL: {app.config.get('MINIO_URL')}")

    # Route to serve uploaded files
    @app.route('/uploads/<path:filename>')
    def uploaded_file(filename):
        return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

    # Alternative route with /api prefix for compatibility
    @app.route('/api/uploads/<path:filename>')
    def api_uploaded_file(filename):
        return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

    return app
app = create_app()

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True)
