from flask import Flask
from app import create_app
from extensions import db
from models.user import User
from models.store import Store
from models.product import Product
from werkzeug.security import generate_password_hash

def init_db():
    """Initialize the database with some sample data."""
    app = create_app()

    with app.app_context():
        # Create tables
        db.create_all()

        # Check if we already have users
        if User.query.count() == 0:
            print("Creating sample users...")

            # Create admin user
            admin = User(
                email="<EMAIL>",
                password="admin123",
                name="Admin User",
                role="admin"
            )

            # Create vendor user
            vendor = User(
                email="<EMAIL>",
                password="vendor123",
                name="Vendor User",
                role="vendor"
            )

            # Create customer user
            customer = User(
                email="<EMAIL>",
                password="customer123",
                name="Customer User",
                role="customer"
            )

            # Add users to session and commit
            db.session.add(admin)
            db.session.add(vendor)
            db.session.add(customer)
            db.session.commit()

            print("Sample users created successfully!")

            # Create sample store for the vendor
            store = Store(
                name="Gourmet Haven",
                user_id=vendor.id,
                description="Fine dining and gourmet meals delivered to your doorstep.",
                logo_url="https://images.unsplash.com/photo-1517248135467-4c7edcad34c4",
                telegram="@gourmethaven",
                phone="+1234567890"
            )

            db.session.add(store)
            db.session.commit()

            print("Sample store created successfully!")

            # Create sample products for the store
            products = [
                Product(
                    name="Classic Burger",
                    price=12.99,
                    store_id=store.id,
                    description="Juicy beef patty with fresh vegetables",
                    image_url="https://images.unsplash.com/photo-1568901346375-23c9450c58cd",
                    category="Main Course",
                    tags="beef,burger,lunch"
                ),
                Product(
                    name="Margherita Pizza",
                    price=14.99,
                    store_id=store.id,
                    description="Traditional Italian pizza with tomatoes and mozzarella",
                    image_url="https://images.unsplash.com/photo-1604068549290-dea0e4a305ca",
                    category="Main Course",
                    tags="vegetarian,italian,pizza"
                ),
                Product(
                    name="Chocolate Cake",
                    price=8.99,
                    store_id=store.id,
                    description="Rich chocolate cake with ganache frosting",
                    image_url="https://images.unsplash.com/photo-1578985545062-69928b1d9587",
                    category="Dessert",
                    tags="chocolate,cake,dessert"
                )
            ]

            for product in products:
                db.session.add(product)

            db.session.commit()

            print("Sample products created successfully!")
        else:
            print("Database already contains users, skipping sample data creation.")

if __name__ == "__main__":
    init_db()
