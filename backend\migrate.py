from app import app, db
from flask_migrate import Migrate, init, migrate, upgrade

# Initialize migration
migrate_instance = Migrate(app, db)

# Run in app context
with app.app_context():
    # Initialize migrations if not already done
    init()
    
    # Create a migration
    migrate(message='Add cover_image_url to Store model')
    
    # Apply the migration
    upgrade()

print("Migration completed successfully!")
