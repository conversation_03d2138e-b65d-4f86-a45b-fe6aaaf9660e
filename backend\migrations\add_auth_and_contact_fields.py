"""
Database migration script to add new authentication and contact fields
Run this script to update your database schema with the new fields
"""

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize Flask app for migration
app = Flask(__name__)

# Database configuration
database_url = os.environ.get('DATABASE_URL')
if database_url and database_url.startswith('postgres://'):
    database_url = database_url.replace('postgres://', 'postgresql://', 1)

app.config['SQLALCHEMY_DATABASE_URI'] = database_url or 'sqlite:///vsable.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)
migrate = Migrate(app, db)

def run_migration():
    """Run the database migration"""
    with app.app_context():
        # Add new fields to users table
        try:
            db.engine.execute("""
                ALTER TABLE users ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT FALSE;
                ALTER TABLE users ADD COLUMN IF NOT EXISTS email_verification_token VARCHAR(255);
                ALTER TABLE users ADD COLUMN IF NOT EXISTS email_verification_expires TIMESTAMP;
                ALTER TABLE users ADD COLUMN IF NOT EXISTS reset_token VARCHAR(255);
                ALTER TABLE users ADD COLUMN IF NOT EXISTS reset_token_expires TIMESTAMP;
                ALTER TABLE users ADD COLUMN IF NOT EXISTS otp_code VARCHAR(6);
                ALTER TABLE users ADD COLUMN IF NOT EXISTS otp_expires TIMESTAMP;
                ALTER TABLE users ADD COLUMN IF NOT EXISTS otp_attempts INTEGER DEFAULT 0;
                ALTER TABLE users ADD COLUMN IF NOT EXISTS google_id VARCHAR(100) UNIQUE;
                ALTER TABLE users ADD COLUMN IF NOT EXISTS profile_picture VARCHAR(255);
            """)
            print("✓ User table migration completed")
        except Exception as e:
            print(f"User table migration error: {e}")

        # Add new fields to stores table
        try:
            db.engine.execute("""
                ALTER TABLE stores ADD COLUMN IF NOT EXISTS facebook VARCHAR(100);
                ALTER TABLE stores ADD COLUMN IF NOT EXISTS tiktok VARCHAR(100);
                ALTER TABLE stores ADD COLUMN IF NOT EXISTS google_maps TEXT;
                ALTER TABLE stores ADD COLUMN IF NOT EXISTS website VARCHAR(255);
                ALTER TABLE stores ADD COLUMN IF NOT EXISTS email VARCHAR(120);
                ALTER TABLE stores ADD COLUMN IF NOT EXISTS contact_preferences JSON;
            """)
            print("✓ Store table migration completed")
        except Exception as e:
            print(f"Store table migration error: {e}")

        # Update existing stores with default contact preferences
        try:
            db.engine.execute("""
                UPDATE stores 
                SET contact_preferences = '{
                    "telegram": true,
                    "whatsapp": true,
                    "phone": true,
                    "instagram": true,
                    "facebook": true,
                    "tiktok": true,
                    "google_maps": true,
                    "website": true,
                    "email": true
                }'
                WHERE contact_preferences IS NULL;
            """)
            print("✓ Default contact preferences set for existing stores")
        except Exception as e:
            print(f"Contact preferences update error: {e}")

        print("\n🎉 Database migration completed successfully!")
        print("\nNew features available:")
        print("- Email verification and password reset")
        print("- OTP authentication")
        print("- Google OAuth support")
        print("- Extended contact information for stores")
        print("- Contact preferences management")

if __name__ == '__main__':
    print("Starting database migration...")
    print("Adding new authentication and contact fields...")
    run_migration()
