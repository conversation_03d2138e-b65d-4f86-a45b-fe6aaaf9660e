from datetime import datetime
from extensions import db

class Product(db.Model):
    __tablename__ = 'products'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    price = db.Column(db.Float, nullable=False)
    image_url = db.Column(db.String(255), nullable=True)
    
    # Category and tags
    category = db.Column(db.String(50), nullable=True)
    tags = db.Column(db.String(255), nullable=True)  # Comma-separated tags
    
    # Product status
    is_active = db.Column(db.<PERSON>, default=True)
    
    # Relationships
    store_id = db.Column(db.Integer, db.ForeignKey('stores.id'), nullable=False)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, name, price, store_id, description=None, image_url=None, 
                 category=None, tags=None, is_active=True):
        self.name = name
        self.price = price
        self.store_id = store_id
        self.description = description
        self.image_url = image_url
        self.category = category
        self.tags = tags
        self.is_active = is_active
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'price': self.price,
            'image_url': self.image_url,
            'category': self.category,
            'tags': self.tags.split(',') if self.tags else [],
            'is_active': self.is_active,
            'store_id': self.store_id,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'contact_link': self.generate_contact_link()
        }
    
    def generate_contact_link(self):
        """Generate a contact link for Telegram if available"""
        store = Store.query.get(self.store_id)
        if store and store.telegram:
            # If it's a username (starts with @)
            if store.telegram.startswith('@'):
                username = store.telegram[1:]  # Remove the @ symbol
                return f"https://t.me/{username}"
            # If it's already a link
            elif store.telegram.startswith('https://t.me/'):
                return store.telegram
            # Otherwise, assume it's a username without @
            else:
                return f"https://t.me/{store.telegram}"
        return None

# Import here to avoid circular imports
from models.store import Store
