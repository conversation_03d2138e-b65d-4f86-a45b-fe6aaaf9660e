from datetime import datetime
from slugify import slugify
from extensions import db
from sqlalchemy.orm import relationship

class Store(db.Model):
    __tablename__ = 'stores'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    slug = db.Column(db.String(100), unique=True, nullable=False)
    description = db.Column(db.Text, nullable=True)
    logo_url = db.Column(db.String(255), nullable=True)
    cover_image_url = db.Column(db.String(255), nullable=True)  # New field for cover page image
    telegram = db.Column(db.String(100), nullable=True)
    whatsapp = db.Column(db.String(100), nullable=True)
    phone = db.Column(db.String(50), nullable=True)
    instagram = db.Column(db.String(100), nullable=True)
    facebook = db.Column(db.String(100), nullable=True)
    tiktok = db.Column(db.String(100), nullable=True)
    google_maps = db.Column(db.Text, nullable=True)  # For Google Maps embed URL
    website = db.Column(db.String(255), nullable=True)
    email = db.Column(db.String(120), nullable=True)

    # Contact preferences - JSON field to store which contacts to show
    contact_preferences = db.Column(db.JSON, nullable=True, default=lambda: {
        'telegram': True,
        'whatsapp': True,
        'phone': True,
        'instagram': True,
        'facebook': True,
        'tiktok': True,
        'google_maps': True,
        'website': True,
        'email': True
    })
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())
    updated_at = db.Column(db.DateTime, default=db.func.current_timestamp(), onupdate=db.func.current_timestamp())

    selected_template_id = db.Column(db.String(50), nullable=True, default='default') # Changed default

    # Relationship with StoreView
    views = relationship("StoreView", backref="store", lazy=True)
    def __init__(self, name, user_id, description=None, logo_url=None, cover_image_url=None,
                 telegram=None, whatsapp=None, phone=None, instagram=None, facebook=None,
                 tiktok=None, google_maps=None, website=None, email=None, contact_preferences=None):
        self.name = name
        self.user_id = user_id
        self.description = description
        self.logo_url = logo_url
        self.cover_image_url = cover_image_url
        self.telegram = telegram
        self.whatsapp = whatsapp
        self.phone = phone
        self.instagram = instagram
        self.facebook = facebook
        self.tiktok = tiktok
        self.google_maps = google_maps
        self.website = website
        self.email = email
        self.contact_preferences = contact_preferences or {
            'telegram': True, 'whatsapp': True, 'phone': True, 'instagram': True,
            'facebook': True, 'tiktok': True, 'google_maps': True, 'website': True, 'email': True
        }
        self.generate_slug()

    def generate_slug(self):
        base_slug = slugify(self.name)
        slug = base_slug
        counter = 1

        # Check if slug exists and generate a unique one
        while Store.query.filter_by(slug=slug).first() is not None:
            slug = f"{base_slug}-{counter}"
            counter += 1

        self.slug = slug

    def to_dict(self):
        # Count total views
        view_count = len(self.views) if hasattr(self, 'views') else 0

        return {
            'id': self.id,
            'name': self.name,
            'slug': self.slug,
            'description': self.description,
            'logo_url': self.logo_url,
            'cover_image_url': self.cover_image_url,
            'telegram': self.telegram,
            'whatsapp': self.whatsapp,
            'phone': self.phone,
            'instagram': self.instagram,
            'facebook': self.facebook,
            'tiktok': self.tiktok,
            'google_maps': self.google_maps,
            'website': self.website,
            'email': self.email,
            'contact_preferences': self.contact_preferences or {},
            'user_id': self.user_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'selected_template_id': self.selected_template_id,
            'view_count': view_count
        }

    def __repr__(self):
        return f'<Store {self.name}>'