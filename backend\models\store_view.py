from datetime import datetime
from extensions import db

class StoreView(db.Model):
    __tablename__ = 'store_views'

    id = db.Column(db.Integer, primary_key=True)
    store_id = db.Column(db.Integer, db.<PERSON>ey('stores.id'), nullable=False)
    ip_address = db.Column(db.String(50), nullable=True)  # Store IP to prevent duplicate counts
    user_agent = db.Column(db.String(255), nullable=True)  # Store user agent for analytics
    timestamp = db.Column(db.DateTime, default=db.func.current_timestamp())

    def __init__(self, store_id, ip_address=None, user_agent=None):
        self.store_id = store_id
        self.ip_address = ip_address
        self.user_agent = user_agent

    def to_dict(self):
        return {
            'id': self.id,
            'store_id': self.store_id,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None
        }
