from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash, check_password_hash
from flask import current_app
import jwt
import secrets
import string

from extensions import db

class User(db.Model):
    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password = db.Column(db.String(255), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='customer')  # customer, vendor, admin
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime, nullable=True)

    # Email verification fields
    email_verified = db.Column(db.Boolean, default=False)
    email_verification_token = db.Column(db.String(255), nullable=True)
    email_verification_expires = db.Column(db.DateTime, nullable=True)

    # Password reset fields
    reset_token = db.Column(db.String(255), nullable=True)
    reset_token_expires = db.Column(db.DateTime, nullable=True)

    # OTP fields
    otp_code = db.Column(db.String(6), nullable=True)
    otp_expires = db.Column(db.DateTime, nullable=True)
    otp_attempts = db.Column(db.Integer, default=0)

    # Google OAuth fields
    google_id = db.Column(db.String(100), nullable=True, unique=True)
    profile_picture = db.Column(db.String(255), nullable=True)

    def __init__(self, email, password, name, role='customer', google_id=None, profile_picture=None, email_verified=False):
        self.email = email
        self.password = generate_password_hash(password) if password else ''
        self.name = name
        self.role = role
        self.google_id = google_id
        self.profile_picture = profile_picture
        self.email_verified = email_verified

    def check_password(self, password):
        return check_password_hash(self.password, password)

    def generate_token(self, remember_me=False):
        expiration = datetime.utcnow() + timedelta(days=30 if remember_me else 1)

        payload = {
            'user_id': self.id,
            'role': self.role,
            'exp': expiration
        }

        return jwt.encode(
            payload,
            current_app.config['SECRET_KEY'],
            algorithm="HS256"
        )

    @staticmethod
    def verify_token(token):
        try:
            data = jwt.decode(token, current_app.config['SECRET_KEY'], algorithms=["HS256"])
            return User.query.get(data['user_id'])
        except:
            return None

    def generate_otp(self):
        """Generate a 6-digit OTP code"""
        self.otp_code = ''.join(secrets.choice(string.digits) for _ in range(6))
        self.otp_expires = datetime.utcnow() + timedelta(minutes=10)  # OTP expires in 10 minutes
        self.otp_attempts = 0
        return self.otp_code

    def verify_otp(self, otp_code):
        """Verify OTP code"""
        if not self.otp_code or not self.otp_expires:
            return False

        if datetime.utcnow() > self.otp_expires:
            return False

        if self.otp_attempts >= 3:
            return False

        if self.otp_code == otp_code:
            self.clear_otp()
            return True
        else:
            self.otp_attempts += 1
            return False

    def clear_otp(self):
        """Clear OTP data"""
        self.otp_code = None
        self.otp_expires = None
        self.otp_attempts = 0

    def generate_reset_token(self):
        """Generate password reset token"""
        self.reset_token = secrets.token_urlsafe(32)
        self.reset_token_expires = datetime.utcnow() + timedelta(hours=1)  # Token expires in 1 hour
        return self.reset_token

    def verify_reset_token(self, token):
        """Verify password reset token"""
        if not self.reset_token or not self.reset_token_expires:
            return False

        if datetime.utcnow() > self.reset_token_expires:
            return False

        return self.reset_token == token

    def clear_reset_token(self):
        """Clear password reset token"""
        self.reset_token = None
        self.reset_token_expires = None

    def generate_email_verification_token(self):
        """Generate email verification token"""
        self.email_verification_token = secrets.token_urlsafe(32)
        self.email_verification_expires = datetime.utcnow() + timedelta(days=1)  # Token expires in 1 day
        return self.email_verification_token

    def verify_email_token(self, token):
        """Verify email verification token"""
        if not self.email_verification_token or not self.email_verification_expires:
            return False

        if datetime.utcnow() > self.email_verification_expires:
            return False

        if self.email_verification_token == token:
            self.email_verified = True
            self.email_verification_token = None
            self.email_verification_expires = None
            return True

        return False

    def to_dict(self):
        return {
            'id': self.id,
            'email': self.email,
            'name': self.name,
            'role': self.role,
            'created_at': self.created_at.isoformat(),
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'email_verified': self.email_verified,
            'profile_picture': self.profile_picture,
            'google_id': self.google_id is not None
        }
