from flask import Blueprint, jsonify, request
from functools import wraps

from models.user import User
from extensions import db

admin_bp = Blueprint('admin', __name__)

# Authentication decorator
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None

        # Check if token is in cookies
        if 'token' in request.cookies:
            token = request.cookies.get('token')

        # Check if token is in headers
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            if auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]

        if not token:
            return jsonify({'message': 'Token is missing!'}), 401

        user = User.verify_token(token)

        if not user:
            return jsonify({'message': 'Invalid or expired token!'}), 401

        return f(user, *args, **kwargs)

    return decorated

# Role-based access control decorator
def role_required(roles):
    def decorator(f):
        @wraps(f)
        def decorated_function(current_user, *args, **kwargs):
            if current_user.role not in roles:
                return jsonify({'message': 'Permission denied!'}), 403
            return f(current_user, *args, **kwargs)
        return decorated_function
    return decorator

@admin_bp.route('/dashboard', methods=['GET'])
@token_required
@role_required(['admin'])
def admin_dashboard(current_user):
    return jsonify({
        'message': 'Admin dashboard data',
        'user': current_user.to_dict()
    })

@admin_bp.route('/vendors', methods=['GET'])
@token_required
@role_required(['admin'])
def get_vendors(current_user):
    # In a real application, you would fetch vendors from the database
    vendors = User.query.filter_by(role='vendor').all()

    return jsonify({
        'vendors': [vendor.to_dict() for vendor in vendors]
    })

@admin_bp.route('/users', methods=['GET'])
@token_required
@role_required(['admin'])
def get_users(current_user):
    # Get all users
    users = User.query.all()

    return jsonify({
        'users': [user.to_dict() for user in users]
    })
