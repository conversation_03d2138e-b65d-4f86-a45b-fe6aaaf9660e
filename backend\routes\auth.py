from flask import Blueprint, request, jsonify, current_app
from werkzeug.security import check_password_hash, generate_password_hash
from datetime import datetime, timedelta
import re

from models.user import User
from extensions import db
from utils.email_service import email_service

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/register', methods=['POST'])
def register():
    data = request.get_json()

    # Validate input
    if not data or not data.get('email') or not data.get('password') or not data.get('name'):
        return jsonify({'message': 'Missing required fields!'}), 400

    # Check if user already exists
    if User.query.filter_by(email=data['email']).first():
        return jsonify({'message': 'User already exists!'}), 409

    # Create new user
    role = data.get('role', 'customer')

    # Validate role
    if role not in ['customer', 'vendor', 'admin']:
        return jsonify({'message': 'Invalid role!'}), 400

    try:
        new_user = User(
            email=data['email'],
            password=data['password'],
            name=data['name'],
            role=role
        )

        db.session.add(new_user)
        db.session.flush()  # Get the user ID before commit

        # Generate email verification token
        verification_token = new_user.generate_email_verification_token()
        db.session.commit()

        # Send welcome email with verification link
        try:
            email_service.send_welcome_email(new_user.email, new_user.name, verification_token)
        except Exception as e:
            current_app.logger.error(f"Failed to send welcome email: {str(e)}")
            # Don't fail registration if email fails

        return jsonify({
            'message': 'User registered successfully! Please check your email to verify your account.',
            'user': new_user.to_dict()
        }), 201

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Registration error: {str(e)}")
        return jsonify({'message': 'Registration failed. Please try again.'}), 500

@auth_bp.route('/login', methods=['POST'])
def login():
    data = request.get_json()

    # Validate input
    if not data or not data.get('email') or not data.get('password'):
        return jsonify({'message': 'Missing email or password!'}), 400

    # Find user
    user = User.query.filter_by(email=data['email']).first()

    # Check if user exists and password is correct
    if not user or not user.check_password(data['password']):
        return jsonify({'message': 'Invalid email or password!'}), 401

    # Update last login time
    user.last_login = datetime.utcnow()
    db.session.commit()

    # Generate token
    remember_me = data.get('remember_me', False)
    token = user.generate_token(remember_me)

    # Create response
    response = jsonify({
        'message': 'Login successful!',
        'user': user.to_dict()
    })

    # Set cookie
    response.set_cookie(
        'token',
        token,
        httponly=True,
        secure=current_app.config['SESSION_COOKIE_SECURE'],
        samesite=current_app.config['SESSION_COOKIE_SAMESITE'],
        max_age=30*24*60*60 if remember_me else 24*60*60  # 30 days or 1 day
    )

    return response

@auth_bp.route('/logout', methods=['POST'])
def logout():
    response = jsonify({'message': 'Logout successful!'})
    response.delete_cookie('token')
    return response

@auth_bp.route('/me', methods=['GET'])
def get_current_user():
    token = request.cookies.get('token')

    if not token:
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]

    if not token:
        return jsonify({'message': 'Token is missing!'}), 401

    user = User.verify_token(token)

    if not user:
        return jsonify({'message': 'Invalid or expired token!'}), 401

    return jsonify({'user': user.to_dict()})

@auth_bp.route('/forgot-password', methods=['POST'])
def forgot_password():
    """Send password reset email"""
    data = request.get_json()

    if not data or not data.get('email'):
        return jsonify({'message': 'Email is required!'}), 400

    email = data['email'].lower().strip()

    # Validate email format
    email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_regex, email):
        return jsonify({'message': 'Invalid email format!'}), 400

    user = User.query.filter_by(email=email).first()

    if not user:
        # Don't reveal if user exists or not for security
        return jsonify({'message': 'If an account with this email exists, you will receive a password reset link.'}), 200

    try:
        # Generate reset token
        reset_token = user.generate_reset_token()
        db.session.commit()

        # Send reset email
        email_sent = email_service.send_password_reset_email(user.email, reset_token, user.name)

        if email_sent:
            return jsonify({'message': 'If an account with this email exists, you will receive a password reset link.'}), 200
        else:
            return jsonify({'message': 'Failed to send reset email. Please try again later.'}), 500

    except Exception as e:
        current_app.logger.error(f"Password reset error: {str(e)}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

@auth_bp.route('/reset-password', methods=['POST'])
def reset_password():
    """Reset password with token"""
    data = request.get_json()

    if not data or not data.get('token') or not data.get('password'):
        return jsonify({'message': 'Token and new password are required!'}), 400

    token = data['token']
    new_password = data['password']

    # Validate password strength
    if len(new_password) < 8:
        return jsonify({'message': 'Password must be at least 8 characters long!'}), 400

    # Find user with valid reset token
    user = User.query.filter_by(reset_token=token).first()

    if not user or not user.verify_reset_token(token):
        return jsonify({'message': 'Invalid or expired reset token!'}), 400

    try:
        # Update password
        user.password = generate_password_hash(new_password)
        user.clear_reset_token()
        db.session.commit()

        return jsonify({'message': 'Password reset successfully!'}), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Password reset error: {str(e)}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

@auth_bp.route('/send-otp', methods=['POST'])
def send_otp():
    """Send OTP for verification"""
    data = request.get_json()

    if not data or not data.get('email'):
        return jsonify({'message': 'Email is required!'}), 400

    email = data['email'].lower().strip()

    user = User.query.filter_by(email=email).first()

    if not user:
        return jsonify({'message': 'User not found!'}), 404

    try:
        # Generate OTP
        otp_code = user.generate_otp()
        db.session.commit()

        # Send OTP email
        email_sent = email_service.send_otp_email(user.email, otp_code, user.name)

        if email_sent:
            return jsonify({'message': 'OTP sent successfully!'}), 200
        else:
            return jsonify({'message': 'Failed to send OTP. Please try again later.'}), 500

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"OTP send error: {str(e)}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

@auth_bp.route('/verify-otp', methods=['POST'])
def verify_otp():
    """Verify OTP code"""
    data = request.get_json()

    if not data or not data.get('email') or not data.get('otp'):
        return jsonify({'message': 'Email and OTP are required!'}), 400

    email = data['email'].lower().strip()
    otp_code = data['otp'].strip()

    user = User.query.filter_by(email=email).first()

    if not user:
        return jsonify({'message': 'User not found!'}), 404

    if user.verify_otp(otp_code):
        try:
            db.session.commit()
            return jsonify({'message': 'OTP verified successfully!', 'user': user.to_dict()}), 200
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"OTP verification error: {str(e)}")
            return jsonify({'message': 'An error occurred. Please try again later.'}), 500
    else:
        return jsonify({'message': 'Invalid or expired OTP!'}), 400

@auth_bp.route('/profile', methods=['PUT'])
def update_profile():
    """Update user profile"""
    token = request.cookies.get('token')

    if not token:
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]

    if not token:
        return jsonify({'message': 'Token is missing!'}), 401

    user = User.verify_token(token)

    if not user:
        return jsonify({'message': 'Invalid or expired token!'}), 401

    data = request.get_json()

    if not data:
        return jsonify({'message': 'No data provided!'}), 400

    try:
        # Update allowed fields
        if 'name' in data:
            user.name = data['name'].strip()

        if 'email' in data:
            new_email = data['email'].lower().strip()
            # Check if email is already taken by another user
            existing_user = User.query.filter_by(email=new_email).first()
            if existing_user and existing_user.id != user.id:
                return jsonify({'message': 'Email already in use!'}), 409

            # If email changed, mark as unverified
            if user.email != new_email:
                user.email = new_email
                user.email_verified = False

        db.session.commit()

        return jsonify({
            'message': 'Profile updated successfully!',
            'user': user.to_dict()
        }), 200

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Profile update error: {str(e)}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

@auth_bp.route('/send-verification-email', methods=['POST'])
def send_verification_email():
    """Send email verification"""
    data = request.get_json()

    if not data or not data.get('email'):
        return jsonify({'message': 'Email is required!'}), 400

    email = data['email'].lower().strip()

    user = User.query.filter_by(email=email).first()

    if not user:
        return jsonify({'message': 'User not found!'}), 404

    if user.email_verified:
        return jsonify({'message': 'Email is already verified!'}), 400

    try:
        # Generate verification token
        verification_token = user.generate_email_verification_token()
        db.session.commit()

        # Send verification email
        email_sent = email_service.send_welcome_email(user.email, user.name, verification_token)

        if email_sent:
            return jsonify({'message': 'Verification email sent successfully!'}), 200
        else:
            return jsonify({'message': 'Failed to send verification email. Please try again later.'}), 500

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Email verification send error: {str(e)}")
        return jsonify({'message': 'An error occurred. Please try again later.'}), 500

@auth_bp.route('/verify-email', methods=['POST'])
def verify_email():
    """Verify email with token"""
    data = request.get_json()

    if not data or not data.get('token'):
        return jsonify({'message': 'Verification token is required!'}), 400

    token = data['token']

    # Find user with this verification token
    user = User.query.filter_by(email_verification_token=token).first()

    if not user:
        return jsonify({'message': 'Invalid verification token!'}), 400

    if user.verify_email_token(token):
        try:
            db.session.commit()
            return jsonify({
                'message': 'Email verified successfully!',
                'user': user.to_dict()
            }), 200
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Email verification error: {str(e)}")
            return jsonify({'message': 'An error occurred. Please try again later.'}), 500
    else:
        return jsonify({'message': 'Invalid or expired verification token!'}), 400

@auth_bp.route('/google', methods=['POST'])
def google_auth():
    """Handle Firebase Google OAuth authentication"""
    data = request.get_json()

    if not data:
        return jsonify({'message': 'Request data is required!'}), 400

    # Check for Firebase ID token
    id_token = data.get('idToken')
    if not id_token:
        return jsonify({'message': 'Firebase ID token is required!'}), 400

    # Get role from request (optional)
    requested_role = data.get('role', 'customer')

    # Validate role
    valid_roles = ['customer', 'vendor', 'admin']
    if requested_role not in valid_roles:
        requested_role = 'customer'

    try:
        # Import Firebase service
        from utils.firebase_service import firebase_service

        # Debug logging
        print(f"Received Google OAuth request with data: {data}")
        print(f"ID Token length: {len(id_token) if id_token else 0}")

        # Verify Firebase ID token
        firebase_user_data = firebase_service.verify_id_token(id_token, data)
        print(f"Firebase verification result: {firebase_user_data}")

        if not firebase_user_data:
            print("Firebase verification failed - returning 401")
            return jsonify({'message': 'Invalid Firebase ID token!'}), 401

        # Extract user data from Firebase token or frontend data
        google_user_data = {
            'sub': firebase_user_data['uid'],
            'email': firebase_user_data.get('email') or data.get('email'),
            'name': firebase_user_data.get('name') or data.get('name'),
            'picture': firebase_user_data.get('picture') or data.get('photoURL'),
            'email_verified': firebase_user_data.get('email_verified', True)
        }

        # Check if user exists with this Google ID
        try:
            user = User.query.filter_by(google_id=google_user_data['sub']).first()
        except Exception as db_error:
            print(f"Database query error: {db_error}")
            # Try to reconnect to database
            try:
                db.session.rollback()
                db.session.close()
                user = User.query.filter_by(google_id=google_user_data['sub']).first()
            except Exception as retry_error:
                print(f"Database retry error: {retry_error}")
                return jsonify({'message': 'Database connection error. Please try again.'}), 500

        if not user:
            # Check if user exists with this email
            try:
                user = User.query.filter_by(email=google_user_data['email']).first()
            except Exception as db_error:
                print(f"Database email query error: {db_error}")
                db.session.rollback()
                return jsonify({'message': 'Database connection error. Please try again.'}), 500

            if user:
                # Link existing account with Google
                user.google_id = google_user_data['sub']
                user.profile_picture = google_user_data.get('picture')
                user.email_verified = True  # Google emails are pre-verified
            else:
                # Create new user
                user = User(
                    email=google_user_data['email'],
                    name=google_user_data['name'],
                    password='',  # No password for Google users
                    role=requested_role,
                    google_id=google_user_data['sub'],
                    profile_picture=google_user_data.get('picture'),
                    email_verified=True
                )
                db.session.add(user)

        # Update last login
        user.last_login = datetime.utcnow()

        try:
            db.session.commit()
        except Exception as commit_error:
            print(f"Database commit error: {commit_error}")
            db.session.rollback()
            return jsonify({'message': 'Failed to save user data. Please try again.'}), 500

        # Generate token
        try:
            token = user.generate_token(remember_me=True)

            # Create response
            response = jsonify({
                'message': 'Google authentication successful!',
                'user': user.to_dict()
            })

            # Set cookie
            response.set_cookie(
                'token',
                token,
                httponly=True,
                secure=current_app.config['SESSION_COOKIE_SECURE'],
                samesite=current_app.config['SESSION_COOKIE_SAMESITE'],
                max_age=30*24*60*60  # 30 days
            )

            print(f"Google OAuth successful for user: {user.email}")
            return response

        except Exception as token_error:
            print(f"Token generation error: {token_error}")
            db.session.rollback()
            return jsonify({'message': 'Authentication successful but token generation failed. Please try again.'}), 500

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Google auth error: {str(e)}")
        print(f"Google auth error: {str(e)}")
        return jsonify({'message': 'Google authentication failed. Please try again.'}), 500