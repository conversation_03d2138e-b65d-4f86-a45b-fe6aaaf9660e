from flask import Blueprint, jsonify, request, current_app
from functools import wraps

from models.user import User
from models.store import Store
from models.product import Product
from extensions import db
from utils.file_upload import save_file

product_bp = Blueprint('product', __name__)

# Authentication decorator
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None

        # Check if token is in cookies
        if 'token' in request.cookies:
            token = request.cookies.get('token')

        # Check if token is in headers
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            if auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]

        if not token:
            return jsonify({'message': 'Token is missing!'}), 401

        user = User.verify_token(token)

        if not user:
            return jsonify({'message': 'Invalid or expired token!'}), 401

        return f(user, *args, **kwargs)

    return decorated

# Role-based access control decorator
def role_required(roles):
    def decorator(f):
        @wraps(f)
        def decorated_function(current_user, *args, **kwargs):
            if current_user.role not in roles:
                return jsonify({'message': 'Permission denied!'}), 403
            return f(current_user, *args, **kwargs)
        return decorated_function
    return decorator

# Get all products for current user's store
@product_bp.route('/', methods=['GET'])
@token_required
@role_required(['vendor', 'admin'])
def get_my_products(current_user):
    store = Store.query.filter_by(user_id=current_user.id).first()

    if not store:
        return jsonify({'message': 'Store not found!'}), 404

    products = Product.query.filter_by(store_id=store.id).all()

    return jsonify({
        'products': [product.to_dict() for product in products]
    })

# Create a product
@product_bp.route('/create', methods=['POST'])
@token_required
@role_required(['vendor', 'admin'])
def create_product(current_user):
    store = Store.query.filter_by(user_id=current_user.id).first()

    if not store:
        return jsonify({'message': 'You need to create a store first!'}), 400

    data = request.get_json()

    # Validate input
    if not data or not data.get('name') or not data.get('price'):
        return jsonify({'message': 'Product name and price are required!'}), 400

    # Process tags if provided
    tags = None
    if data.get('tags'):
        if isinstance(data['tags'], list):
            tags = ','.join(data['tags'])
        else:
            tags = data['tags']

    # Create new product
    new_product = Product(
        name=data['name'],
        price=float(data['price']),
        store_id=store.id,
        description=data.get('description'),
        image_url=data.get('image_url'),
        category=data.get('category'),
        tags=tags,
        is_active=data.get('is_active', True)
    )

    db.session.add(new_product)
    db.session.commit()

    return jsonify({
        'message': 'Product created successfully!',
        'product': new_product.to_dict()
    }), 201

# Update a product
@product_bp.route('/<int:product_id>', methods=['PUT'])
@token_required
@role_required(['vendor', 'admin'])
def update_product(current_user, product_id):
    store = Store.query.filter_by(user_id=current_user.id).first()

    if not store:
        return jsonify({'message': 'Store not found!'}), 404

    product = Product.query.filter_by(id=product_id, store_id=store.id).first()

    if not product:
        return jsonify({'message': 'Product not found or you do not have permission!'}), 404

    data = request.get_json()

    # Update product fields
    if 'name' in data:
        product.name = data['name']

    if 'price' in data:
        product.price = float(data['price'])

    if 'description' in data:
        product.description = data['description']

    if 'image_url' in data:
        product.image_url = data['image_url']

    if 'category' in data:
        product.category = data['category']

    if 'tags' in data:
        if isinstance(data['tags'], list):
            product.tags = ','.join(data['tags'])
        else:
            product.tags = data['tags']

    if 'is_active' in data:
        product.is_active = data['is_active']

    db.session.commit()

    return jsonify({
        'message': 'Product updated successfully!',
        'product': product.to_dict()
    })

# Delete a product
@product_bp.route('/<int:product_id>', methods=['DELETE'])
@token_required
@role_required(['vendor', 'admin'])
def delete_product(current_user, product_id):
    store = Store.query.filter_by(user_id=current_user.id).first()

    if not store:
        return jsonify({'message': 'Store not found!'}), 404

    product = Product.query.filter_by(id=product_id, store_id=store.id).first()

    if not product:
        return jsonify({'message': 'Product not found or you do not have permission!'}), 404

    db.session.delete(product)
    db.session.commit()

    return jsonify({
        'message': 'Product deleted successfully!'
    })

# Toggle product status (active/inactive)
@product_bp.route('/<int:product_id>/toggle', methods=['PUT'])
@token_required
@role_required(['vendor', 'admin'])
def toggle_product(current_user, product_id):
    store = Store.query.filter_by(user_id=current_user.id).first()

    if not store:
        return jsonify({'message': 'Store not found!'}), 404

    product = Product.query.filter_by(id=product_id, store_id=store.id).first()

    if not product:
        return jsonify({'message': 'Product not found or you do not have permission!'}), 404

    # Toggle the is_active status
    product.is_active = not product.is_active
    db.session.commit()

    return jsonify({
        'message': f'Product {"activated" if product.is_active else "deactivated"} successfully!',
        'product': product.to_dict()
    })

# Upload product image
@product_bp.route('/<int:product_id>/upload-image', methods=['POST'])
@token_required
@role_required(['vendor', 'admin'])
def upload_product_image(current_user, product_id):
    store = Store.query.filter_by(user_id=current_user.id).first()

    if not store:
        return jsonify({'message': 'Store not found!'}), 404

    product = Product.query.filter_by(id=product_id, store_id=store.id).first()

    if not product:
        return jsonify({'message': 'Product not found or you do not have permission!'}), 404

    # Check if the post request has the file part
    if 'file' not in request.files:
        return jsonify({'message': 'No file part in the request'}), 400

    file = request.files['file']

    # If user does not select file, browser also
    # submit an empty part without filename
    if file.filename == '':
        return jsonify({'message': 'No selected file'}), 400

    try:
        # Determine file type based on content
        content_type = file.content_type
        file_type = 'image'

        if content_type.startswith('video/'):
            file_type = 'video'
        elif not content_type.startswith('image/'):
            return jsonify({'message': 'Invalid file type. Only images and videos are allowed.'}), 400

        # Save the file and get the relative path (using MinIO)
        file_path = save_file(file, file_type=file_type, use_minio=True)

        # Update product with the new image URL
        product.image_url = file_path
        db.session.commit()

        # Construct the URL
        minio_url = current_app.config.get('MINIO_URL', f"http://{current_app.config.get('MINIO_ENDPOINT')}")
        bucket_name = current_app.config.get('MINIO_BUCKET', 'uploads')
        object_name = file_path.split('/')[-1]  # Just use the filename without the folder path
        file_url = f"{minio_url}/{bucket_name}/{object_name}"

        return jsonify({
            'message': 'File uploaded successfully',
            'file_path': file_path,
            'file_url': file_url,
            'product': product.to_dict()
        }), 201
    except ValueError as e:
        return jsonify({'message': str(e)}), 400
    except Exception as e:
        return jsonify({'message': f'Error uploading file: {str(e)}'}), 500
