from flask import Blueprint, jsonify, request, current_app
from functools import wraps
from datetime import datetime, timedelta

from models.user import User
from models.store import Store
from models.product import Product
from models.store_view import StoreView
from extensions import db
from utils.file_upload import handle_upload_error, save_file

store_bp = Blueprint('store', __name__)

# Authentication decorator
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None

        # Check if token is in cookies
        if 'token' in request.cookies:
            token = request.cookies.get('token')

        # Check if token is in headers
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            if auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]

        if not token:
            return jsonify({'message': 'Token is missing!'}), 401

        user = User.verify_token(token)

        if not user:
            return jsonify({'message': 'Invalid or expired token!'}), 401

        return f(user, *args, **kwargs)

    return decorated

# Role-based access control decorator
def role_required(roles):
    def decorator(f):
        @wraps(f)
        def decorated_function(current_user, *args, **kwargs):
            if current_user.role not in roles:
                return jsonify({'message': 'Permission denied!'}), 403
            return f(current_user, *args, **kwargs)
        return decorated_function
    return decorator

# Get current user's store
@store_bp.route('/my-store', methods=['GET'])
@token_required
@role_required(['vendor', 'admin'])
def get_my_store(current_user):
    store = Store.query.filter_by(user_id=current_user.id).first()

    if not store:
        return jsonify({'message': 'Store not found!'}), 404

    return jsonify({'store': store.to_dict()})

# Create a store
@store_bp.route('/create', methods=['POST'])
@token_required
@role_required(['vendor', 'admin'])
def create_store(current_user):
    # Check if user already has a store
    existing_store = Store.query.filter_by(user_id=current_user.id).first()
    if existing_store:
        return jsonify({'message': 'You already have a store!'}), 400

    data = request.get_json()

    # Validate input
    if not data or not data.get('name'):
        return jsonify({'message': 'Store name is required!'}), 400

    # Create new store
    new_store = Store(
        name=data['name'],
        user_id=current_user.id,
        description=data.get('description'),
        logo_url=data.get('logo_url'),
        telegram=data.get('telegram'),
        whatsapp=data.get('whatsapp'),
        phone=data.get('phone'),
        instagram=data.get('instagram'),
        facebook=data.get('facebook'),
        tiktok=data.get('tiktok')
    )

    db.session.add(new_store)
    db.session.commit()

    return jsonify({
        'message': 'Store created successfully!',
        'store': new_store.to_dict()
    }), 201

# Update a store
@store_bp.route('/update', methods=['PUT'])
@token_required
@role_required(['vendor', 'admin'])
def update_store(current_user):
    store = Store.query.filter_by(user_id=current_user.id).first()

    if not store:
        return jsonify({'message': 'Store not found!'}), 404

    data = request.get_json()

    # Update store fields
    if 'name' in data:
        store.name = data['name']
        store.generate_slug()  # Regenerate slug if name changes

    if 'description' in data:
        store.description = data['description']

    if 'logo_url' in data:
        store.logo_url = data['logo_url']

    if 'cover_image_url' in data:
        store.cover_image_url = data['cover_image_url']

    if 'telegram' in data:
        store.telegram = data['telegram']

    if 'whatsapp' in data:
        store.whatsapp = data['whatsapp']

    if 'phone' in data:
        store.phone = data['phone']

    if 'instagram' in data:
        store.instagram = data['instagram']

    if 'facebook' in data:
        store.facebook = data['facebook']

    if 'tiktok' in data:
        store.tiktok = data['tiktok']

    db.session.commit()

    return jsonify({
        'message': 'Store updated successfully!',
        'store': store.to_dict()
    })

# Get store by slug (public)
@store_bp.route('/<slug>', methods=['GET'])
def get_store_by_slug(slug):
    store = Store.query.filter_by(slug=slug).first()

    if not store:
        return jsonify({'message': 'Store not found!'}), 404

    # Track store view
    ip_address = request.remote_addr
    user_agent = request.headers.get('User-Agent', '')

    print(f"DEBUG: Tracking view for store {store.id} from IP {ip_address}")

    # Check if this IP has viewed the store in the last 24 hours to prevent duplicate counts
    last_24h = datetime.now() - timedelta(hours=24)
    existing_view = StoreView.query.filter_by(
        store_id=store.id,
        ip_address=ip_address
    ).filter(StoreView.timestamp > last_24h).first()

    # Only count the view if it's a new visitor or returning after 24 hours
    if not existing_view:
        print(f"DEBUG: Adding new view for store {store.id}")
        new_view = StoreView(
            store_id=store.id,
            ip_address=ip_address,
            user_agent=user_agent
        )
        db.session.add(new_view)
        db.session.commit()
        print(f"DEBUG: View added successfully")
    else:
        print(f"DEBUG: Skipping duplicate view from IP {ip_address}")

    # Get active products for this store
    products = Product.query.filter_by(store_id=store.id, is_active=True).all()

    return jsonify({
        'store': store.to_dict(),
        'products': [product.to_dict() for product in products]
    })

# Get all stores (public)
@store_bp.route('/', methods=['GET'])
def get_all_stores():
    stores = Store.query.all()
    return jsonify({
        'stores': [store.to_dict() for store in stores]
    })

# Upload store logo
@store_bp.route('/upload-logo', methods=['POST'])
@token_required
@role_required(['vendor', 'admin'])
def upload_store_logo(current_user):
    store = Store.query.filter_by(user_id=current_user.id).first()

    if not store:
        return jsonify({'message': 'Store not found!'}), 404

    # Check if the post request has the file part
    if 'file' not in request.files:
        return jsonify({'message': 'No file part in the request'}), 400

    file = request.files['file']

    # If user does not select file, browser also
    # submit an empty part without filename
    if file.filename == '':
        return jsonify({'message': 'No selected file'}), 400

    try:
        # Determine file type based on content
        content_type = file.content_type
        file_type = 'image'

        if content_type.startswith('video/'):
            file_type = 'video'
        elif not content_type.startswith('image/'):
            return jsonify({'message': 'Invalid file type. Only images and videos are allowed.'}), 400

        # Save the file and get the relative path (using MinIO)
        file_path = save_file(file, file_type=file_type, use_minio=True)
        store.logo_url = file_path
        db.session.commit()

        # Construct URL
        minio_url = current_app.config.get('MINIO_URL')
        bucket_name = current_app.config.get('MINIO_BUCKET', 'uploads')
        object_name = file_path.split('/')[-1]
        file_url = f"{minio_url}/{bucket_name}/{object_name}"

        return jsonify({
            'message': 'File uploaded successfully',
            'file_path': file_path,
            'file_url': file_url,
            'store': store.to_dict()
        }), 201

    except Exception as e:
        message, status_code = handle_upload_error(e, 'store logo upload')
        return jsonify({'message': message}), status_code

# Upload store cover image
@store_bp.route('/upload-cover', methods=['POST'])
@token_required
@role_required(['vendor', 'admin'])
def upload_store_cover(current_user):
    store = Store.query.filter_by(user_id=current_user.id).first()

    if not store:
        return jsonify({'message': 'Store not found!'}), 404

    # Check if the post request has the file part
    if 'file' not in request.files:
        return jsonify({'message': 'No file part in the request'}), 400

    file = request.files['file']

    # If user does not select file, browser also
    # submit an empty part without filename
    if file.filename == '':
        return jsonify({'message': 'No selected file'}), 400

    try:
        # Determine file type based on content
        content_type = file.content_type
        file_type = 'image'

        if content_type.startswith('video/'):
            file_type = 'video'
        elif not content_type.startswith('image/'):
            return jsonify({'message': 'Invalid file type. Only images and videos are allowed.'}), 400

        # Save the file and get the relative path (using MinIO)
        file_path = save_file(file, file_type=file_type, use_minio=True)

        # Update store with the new cover image URL
        print(f"Updating store cover image URL: {file_path}")
        store.cover_image_url = file_path
        db.session.commit()

        # Verify the update was successful
        updated_store = Store.query.get(store.id)
        print(f"Updated store cover image URL: {updated_store.cover_image_url}")
        print(f"Updated store logo URL: {updated_store.logo_url}")

        # Construct the URL
        minio_url = current_app.config.get('MINIO_URL', f"http://{current_app.config.get('MINIO_ENDPOINT')}")
        bucket_name = current_app.config.get('MINIO_BUCKET', 'uploads')
        object_name = file_path.split('/')[-1]  # Just use the filename without the folder path
        file_url = f"{minio_url}/{bucket_name}/{object_name}"

        return jsonify({
            'message': 'Cover image uploaded successfully',
            'file_path': file_path,
            'file_url': file_url,
            'store': store.to_dict()
        }), 201
    except ValueError as e:
        return jsonify({'message': str(e)}), 400
    except Exception as e:
        return jsonify({'message': f'Error uploading file: {str(e)}'}), 500
