from flask import Blueprint, jsonify, request, current_app, Response
from functools import wraps
import os
from minio import Minio
from minio.error import S3Error

from utils.file_upload import save_file
from routes.vendor import token_required, role_required

upload_bp = Blueprint('upload', __name__)

def get_minio_client():
    """Get MinIO client with current application configuration"""
    import urllib3

    # Disable SSL warnings for self-signed certificates
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

    # Get configuration
    endpoint = current_app.config.get('MINIO_ENDPOINT_API', '35.240.129.146:9000')
    access_key = current_app.config.get('MINIO_ACCESS_KEY', 'admin')
    secret_key = current_app.config.get('MINIO_SECRET_KEY', 'password')
    secure = current_app.config.get('MINIO_SECURE', True)
    verify_ssl = current_app.config.get('MINIO_VERIFY_SSL', False)

    print(f"Connecting to MinIO: {endpoint} (secure={secure}, verify_ssl={verify_ssl})")

    # Create client with SSL verification disabled for self-signed certificates
    return Minio(
        endpoint=endpoint,
        access_key=access_key,
        secret_key=secret_key,
        secure=secure,
        http_client=None if verify_ssl else urllib3.PoolManager(
            timeout=urllib3.Timeout.DEFAULT_TIMEOUT,
            cert_reqs='CERT_NONE',
            maxsize=10
        )
    )

@upload_bp.route('/image', methods=['POST'])
@token_required
@role_required(['vendor', 'admin'])
def upload_image(current_user):
    """Upload an image file to MinIO storage"""
    if 'file' not in request.files:
        return jsonify({'message': 'No file part in the request'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'message': 'No selected file'}), 400

    try:
        # Update parameter name from use_cloud_storage to use_minio
        file_path = save_file(file, file_type='image', use_minio=True)
        object_name = file_path.split('/')[-1]

        # Construct MinIO URL
        bucket_name = current_app.config.get('MINIO_BUCKET', 'uploads')
        minio_url = current_app.config.get('MINIO_URL', 'https://35.240.129.146:9000')
        file_url = f"{minio_url}/{bucket_name}/{object_name}"

        print(f"File path: {file_path}")
        print(f"Generated MinIO URL: {file_url}")

        return jsonify({
            'message': 'File uploaded successfully',
            'file_path': file_path,
            'file_url': file_url,
            'file_type': 'image',
            'original_filename': file.filename
        }), 201
    except ValueError as e:
        return jsonify({'message': str(e)}), 400
    except S3Error as e:
        return jsonify({'message': f'Storage error: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'message': f'Error uploading file: {str(e)}'}), 500

@upload_bp.route('/video', methods=['POST'])
@token_required
@role_required(['vendor', 'admin'])
def upload_video(current_user):
    """Upload a video file to MinIO storage"""
    if 'file' not in request.files:
        return jsonify({'message': 'No file part in the request'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'message': 'No selected file'}), 400

    try:
        # Update parameter name from use_cloud_storage to use_minio
        file_path = save_file(file, file_type='video', use_minio=True)
        object_name = file_path.split('/')[-1]

        # Construct MinIO URL
        bucket_name = current_app.config.get('MINIO_BUCKET', 'uploads')
        minio_url = current_app.config.get('MINIO_URL', 'https://35.240.129.146:9000')
        file_url = f"{minio_url}/{bucket_name}/{object_name}"

        print(f"File path: {file_path}")
        print(f"Generated MinIO URL: {file_url}")

        return jsonify({
            'message': 'File uploaded successfully',
            'file_path': file_path,
            'file_url': file_url,
            'file_type': 'video',
            'original_filename': file.filename
        }), 201
    except ValueError as e:
        return jsonify({'message': str(e)}), 400
    except S3Error as e:
        return jsonify({'message': f'Storage error: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'message': f'Error uploading file: {str(e)}'}), 500

@upload_bp.route('/serve/<filename>', methods=['GET'])
def serve_file(filename):
    """Serve a file from MinIO storage"""
    try:
        minio_client = get_minio_client()
        bucket_name = current_app.config.get('MINIO_BUCKET', 'uploads')

        # Try different possible paths for the file
        possible_paths = [
            filename,                          # Direct filename
            f"uploads/images/{filename}",      # Images folder
            f"uploads/videos/{filename}"       # Videos folder
        ]

        # Try each path until we find the file
        response = None
        for path in possible_paths:
            try:
                print(f"Trying to get object: {path}")
                response = minio_client.get_object(bucket_name, path)
                print(f"Found file at path: {path}")
                break
            except Exception as e:
                print(f"File not found at path: {path}, error: {str(e)}")
                continue

        # If we didn't find the file, raise an exception
        if response is None:
            raise FileNotFoundError(f"File not found: {filename}")

        # Determine content type based on file extension
        content_type = 'application/octet-stream'  # Default
        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            content_type = 'image/jpeg'
        elif filename.lower().endswith('.gif'):
            content_type = 'image/gif'
        elif filename.lower().endswith('.webp'):
            content_type = 'image/webp'
        elif filename.lower().endswith(('.mp4', '.webm')):
            content_type = 'video/mp4'

        return Response(
            response.data,
            mimetype=content_type
        )
    except FileNotFoundError as e:
        return jsonify({'message': str(e)}), 404
    except Exception as e:
        print(f"Error serving file: {str(e)}")
        return jsonify({'message': f'Error serving file: {str(e)}'}), 500


