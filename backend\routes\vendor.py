# vendor/routes.py  (or wherever you register vendor_bp)

from flask import Blueprint, jsonify, request, current_app
from functools import wraps
from urllib.parse import urljoin

from extensions import db
from models.user import User
from models.store import Store
from models.product import Product

vendor_bp = Blueprint("vendor", __name__, url_prefix='/api/vendor')
    # ------------------------------------------------------------------
# Auth helpers (unchanged logic, but simplified string handling)
    # ------------------------------------------------------------------
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = (
            request.cookies.get("token")
            or request.headers.get("Authorization", "").replace("Bearer ", "").strip()
        )

        if not token:
            return jsonify({"message": "Token is missing!"}), 401

        try:
            user = User.verify_token(token)
            if not user:
                raise Exception("Invalid token")
        except Exception as e:
            current_app.logger.error(f"Token verification failed: {e}")
            return jsonify({"message": "Invalid or expired token!"}), 401

        return f(user, *args, **kwargs)

    return decorated


def role_required(roles):
    def decorator(f):
        @wraps(f)
        def decorated_function(current_user, *args, **kwargs):
            if current_user.role not in roles:
                return jsonify({"message": "Permission denied! Required roles: " + ", ".join(roles)}), 403
            return f(current_user, *args, **kwargs)

        return decorated_function

    return decorator
    # ------------------------------------------------------------------
# Routes
    # ------------------------------------------------------------------
@vendor_bp.route("/dashboard", methods=["GET"])
@token_required
@role_required(["vendor", "admin"])
def vendor_dashboard(current_user):
    """
    Returns user info and very light stats so the React dashboard has something
    to show while it waits for the heavy endpoints.
    """
    store = Store.query.filter_by(user_id=current_user.id).first()
    store_data = store.to_dict() if store else None
    return jsonify({"user": current_user.to_dict(), "store": store_data})


@vendor_bp.route("/menu", methods=["GET"])
@token_required
@role_required(["vendor", "admin"])
def get_menu(current_user):
    """
    → Vendors: returns ONLY the products that belong to *their* store.
    → Admins:  returns ALL products (across every store).

    Each product dict now carries an absolute imageUrl so the React
    dashboard can render thumbnails out‑of‑the‑box.
    """
    # ------------------------------------------------------------------
    # 1. pull the right product list
    # ------------------------------------------------------------------
    if current_user.role == "vendor":
        store = Store.query.filter_by(user_id=current_user.id).first()
        if store is None:
            return jsonify({"menu_items": []})

        products = (
            Product.query
                   .filter_by(store_id=store.id, is_active=True)
                   .order_by(Product.id.desc())
                   .all()
    )
    else:
        # admin
        products = Product.query.order_by(Product.id.desc()).all()

    # ------------------------------------------------------------------
    # 2. normalise image paths → absolute URLs
    # ------------------------------------------------------------------
    uploads_prefix = current_app.config.get("UPLOADS_URL", "/uploads/")  # e.g. "/uploads/"
    host_root      = request.host_url                                   # e.g. "https://api.example.com/"

    menu_items = []
    for p in products:
        d = p.to_dict()                             # your usual mapping
        img = getattr(p, "image_url", None)

        if img:
            # already absolute? → use as-is
            if img.startswith(("http://", "https://")):
                d["imageUrl"] = img
            else:
                # relative → make it absolute
                #   "/uploads/"     + "burger.jpg"    → https://api…/uploads/burger.jpg
                #   "images/bun.png"→ https://api…/uploads/images/bun.png
                d["imageUrl"] = urljoin(host_root,
                                        f"{uploads_prefix.lstrip('/')}/{img.lstrip('/')}")
        else:
            d["imageUrl"] = None                    # optional – keeps shape consistent

        menu_items.append(d)
    return jsonify({"menu_items": menu_items})


# ------------------------------------------------------------------
# (Optional) create / update / delete endpoints so the dashboard
# refreshes with real data right after a product is added.
# ------------------------------------------------------------------
@vendor_bp.route("/menu", methods=["POST"])
@token_required
@role_required(["vendor", "admin"])
def create_menu_item(current_user):
    data = request.get_json(force=True)

    # Vendors can create only inside *their* store; admins can create anywhere
    if current_user.role == "vendor":
        store = Store.query.filter_by(user_id=current_user.id).first()
        if store is None:
            return jsonify({"message": "Please create a store first."}), 400
        store_id = store.id
    else:
        store_id = data.get("store_id")
        if store_id is None:
            return jsonify({"message": "store_id is required for admins"}), 400

    item = Product(
        name=data["name"],
        price=float(data["price"]),
        store_id=store_id,
        description=data.get("description"),
        image_url=data.get("image_url"),
        category=data.get("category"),
        tags=",".join(data.get("tags", [])),
        is_active=data.get("is_active", True),
    )

    db.session.add(item)
    db.session.commit()
    return jsonify({"menu_item": item.to_dict()}), 201


# NEW: Endpoint to update store template
@vendor_bp.route("/store/template", methods=["PUT"])
@token_required
@role_required(["vendor"]) # Only vendors can change their own store template
def update_store_template(current_user):
    store = Store.query.filter_by(user_id=current_user.id).first()
    if not store:
        return jsonify({"message": "Store not found for this vendor"}), 404

    data = request.get_json()
    if not data or 'selected_template_id' not in data:
        return jsonify({"message": "Missing 'selected_template_id' in request body"}), 400

    new_template_id = data.get('selected_template_id')

    # Define available templates (consider moving this to config)
    AVAILABLE_TEMPLATES = ["template1", "template2", "template3", "template4", "template5"]
    if new_template_id not in AVAILABLE_TEMPLATES:
        return jsonify({"message": f"Invalid template ID. Must be one of: {', '.join(AVAILABLE_TEMPLATES)}"}), 400

    try:
        store.selected_template_id = new_template_id
        db.session.commit()
        return jsonify({"message": "Store template updated successfully", "store": store.to_dict()}), 200
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating store template: {e}")
        return jsonify({"message": "Failed to update store template"}), 500

# Get store statistics (views)
@vendor_bp.route("/stats", methods=["GET"])
@token_required
@role_required(["vendor", "admin"])
def get_store_stats(current_user):
    """
    Returns statistics for the vendor's store, including total views
    """
    print(f"DEBUG: Getting stats for user {current_user.id}")
    store = Store.query.filter_by(user_id=current_user.id).first()

    if not store:
        print(f"DEBUG: Store not found for user {current_user.id}")
        return jsonify({"message": "Store not found", "total_views": 0}), 404

    # Get total views
    from models.store_view import StoreView
    view_count = StoreView.query.filter_by(store_id=store.id).count()
    print(f"DEBUG: Store {store.id} has {view_count} views")

    return jsonify({
        "total_views": view_count
    })

# IMPORTANT: Ensure that any public API endpoint that fetches store details (e.g., for the frontend/vsable/src/app/store/[slug]/page.tsx)
# also includes the `selected_template_id`. This is handled if the `Store.to_dict()` method includes it and is used by that endpoint.