from app import app, db
from flask_migrate import Migrate, init, migrate, upgrade

# Run in app context
with app.app_context():
    # Create a migration instance
    migrate_instance = Migrate(app, db)
    
    # Initialize migrations directory if it doesn't exist
    try:
        init()
        print("Migrations directory initialized.")
    except:
        print("Migrations directory already exists or could not be created.")
    
    # Create a migration
    try:
        migrate(message='Add cover_image_url to Store model')
        print("Migration created.")
    except Exception as e:
        print(f"Error creating migration: {e}")
    
    # Apply the migration
    try:
        upgrade()
        print("Migration applied.")
    except Exception as e:
        print(f"Error applying migration: {e}")

print("Migration process completed!")
