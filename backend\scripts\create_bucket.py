from minio import Minio
import urllib3
import json
import os
from dotenv import load_dotenv

# Disable SSL warning for development
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def create_minio_bucket():
    """Create and configure MinIO bucket"""
    try:
        # Load environment variables
        load_dotenv()
        
        # Get MinIO configuration
        minio_endpoint = os.getenv('MINIO_ENDPOINT', '35.240.129.146:9000')
        minio_access_key = os.getenv('MINIO_ACCESS_KEY', 'admin')
        minio_secret_key = os.getenv('MINIO_SECRET_KEY', 'password')
        bucket_name = os.getenv('MINIO_BUCKET', 'uploads')

        print(f"Connecting to MinIO at {minio_endpoint}")
        print(f"Using access key: {minio_access_key}")
        
        # Create MinIO client
        client = Minio(
            minio_endpoint,
            access_key=minio_access_key,
            secret_key=minio_secret_key,
            secure=True,
            http_client=urllib3.PoolManager(
                timeout=urllib3.Timeout.DEFAULT_TIMEOUT,
                cert_reqs='CERT_NONE',  # Skip SSL verification in development
                maxsize=10
            )
        )

        # Check if bucket exists
        if client.bucket_exists(bucket_name):
            print(f"Bucket '{bucket_name}' already exists")
        else:
            print(f"Creating bucket '{bucket_name}'")
            client.make_bucket(bucket_name)
            print(f"Bucket '{bucket_name}' created successfully")

        # Set public read policy for the bucket
        policy = {
            "Version": "2012-10-17",
            "Statement": [
                {
                    "Effect": "Allow",
                    "Principal": {"AWS": "*"},
                    "Action": ["s3:GetObject"],
                    "Resource": [f"arn:aws:s3:::{bucket_name}/*"]
                }
            ]
        }
        
        # Set bucket policy
        client.set_bucket_policy(bucket_name, json.dumps(policy))
        print(f"Public read policy set for bucket '{bucket_name}'")

        # Test uploading a small file
        test_content = b"Test file content"
        try:
            client.put_object(
                bucket_name,
                "test.txt",
                data=test_content,
                length=len(test_content)
            )
            print("Test file upload successful")
        except Exception as e:
            print(f"Test file upload failed: {str(e)}")

        print("\nBucket setup completed successfully!")
        return True

    except Exception as e:
        print(f"Error creating bucket: {str(e)}")
        return False

if __name__ == "__main__":
    print("Starting MinIO bucket setup...")
    success = create_minio_bucket()
    print(f"\nSetup {'succeeded' if success else 'failed'}")