import requests
import os
from PIL import Image
import io
import tempfile
import jwt
import datetime

# API base URL
API_URL = 'http://localhost:5000/api'

def generate_test_token():
    """Generate a test token for testing"""
    # This is just for testing purposes
    # In a real application, you would get a token from the login endpoint
    payload = {
        'user_id': 1,
        'role': 'admin',
        'exp': int((datetime.datetime.now() + datetime.timedelta(days=1)).timestamp())
    }

    # Use the same secret key as in the Flask app
    secret_key = 'dev_secret_key_change_in_production'

    return jwt.encode(payload, secret_key, algorithm="HS256")

def create_test_image():
    """Create a simple test image"""
    img = Image.new('RGB', (100, 100), color='red')
    img_io = io.BytesIO()
    img.save(img_io, 'JPEG')
    img_io.seek(0)
    return img_io

def test_image_upload():
    """Test uploading an image to the API"""
    print("Testing image upload...")

    # Create a test image
    img_io = create_test_image()

    # Prepare the file for upload
    files = {'file': ('test_image.jpg', img_io, 'image/jpeg')}

    # Use a hardcoded token for testing
    # This is a token for a vendor user with ID 4
    token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjo0LCJyb2xlIjoidmVuZG9yIiwiZXhwIjoxNzUwMDQxNTI1fQ.nMadY923lv2gOBE2kwzYwhSU12uA7FFOiw6PryO-9ng"

    # Make the request
    headers = {'Authorization': f'Bearer {token}'}
    response = requests.post(f'{API_URL}/upload/image', files=files, headers=headers)

    # Print the response
    print(f"Status code: {response.status_code}")
    print(f"Response: {response.json() if response.status_code < 400 else response.text}")

    # Check if the file_url is correct (doesn't contain 'directly' folder)
    if response.status_code == 201:
        file_url = response.json().get('file_url', '')
        print(f"File URL: {file_url}")

        # Check if 'directly' is in the URL
        if 'directly' in file_url:
            print("ERROR: 'directly' folder is still in the URL")
        else:
            print("SUCCESS: 'directly' folder is not in the URL")

    return response.json() if response.status_code == 201 else None

if __name__ == "__main__":
    print("Testing Minio uploads without 'directly' folder")
    print("==============================================")

    image_result = test_image_upload()

    print("\nTest Summary:")
    print("=============")
    print(f"Image upload: {'SUCCESS' if image_result else 'FAILED'}")