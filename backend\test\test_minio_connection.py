from minio import Minio
import urllib3
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_minio_connection():
    try:
        # Get credentials from environment
        minio_endpoint = os.getenv('MINIO_ENDPOINT', '35.240.129.146:9000')
        minio_access_key = os.getenv('MINIO_ACCESS_KEY', 'minioadmin')
        minio_secret_key = os.getenv('MINIO_SECRET_KEY', 'minioadmin')
        
        print(f"Testing connection to MinIO at {minio_endpoint}")
        print(f"Using access key: {minio_access_key}")
        
        # Create MinIO client
        client = Minio(
            minio_endpoint,
            access_key=minio_access_key,
            secret_key=minio_secret_key,
            secure=True,
            http_client=urllib3.PoolManager(cert_reqs='CERT_NONE')
        )
        
        # Test bucket operations
        bucket_name = 'uploads'
        
        # Check if bucket exists
        if client.bucket_exists(bucket_name):
            print(f"Bucket '{bucket_name}' exists")
        else:
            print(f"Creating bucket '{bucket_name}'")
            client.make_bucket(bucket_name)
            
        print("MinIO connection test successful!")
        return True
        
    except Exception as e:
        print(f"MinIO connection test failed: {str(e)}")
        return False

if __name__ == "__main__":
    test_minio_connection()