from app import app
from models.store import Store

with app.app_context():
    # Print all column names
    print("Store table columns:", Store.__table__.columns.keys())
    
    # Check if cover_image_url is in the columns
    if 'cover_image_url' in Store.__table__.columns:
        print("cover_image_url column exists in the Store table!")
    else:
        print("cover_image_url column does NOT exist in the Store table!")
