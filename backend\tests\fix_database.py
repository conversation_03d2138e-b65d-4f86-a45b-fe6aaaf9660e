#!/usr/bin/env python3
"""
Database migration script to add new authentication and contact fields
"""

import os
import sys
from sqlalchemy import text

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from extensions import db

def run_migration():
    """Run the database migration"""
    app = create_app()
    
    with app.app_context():
        print("🚀 Starting database migration...")
        print(f"Database URL: {app.config['SQLALCHEMY_DATABASE_URI']}")
        
        # Check if we're using PostgreSQL
        if 'postgresql' in str(db.engine.url):
            print("📊 Using PostgreSQL - running ALTER TABLE commands...")
            
            # User table migrations
            user_migrations = [
                "ALTER TABLE users ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT FALSE",
                "ALTER TABLE users ADD COLUMN IF NOT EXISTS email_verification_token VARCHAR(255)",
                "ALTER TABLE users ADD COLUMN IF NOT EXISTS email_verification_expires TIMESTAMP",
                "ALTER TABLE users ADD COLUMN IF NOT EXISTS reset_token VARCHAR(255)",
                "ALTER TABLE users ADD COLUMN IF NOT EXISTS reset_token_expires TIMESTAMP",
                "ALTER TABLE users ADD COLUMN IF NOT EXISTS otp_code VARCHAR(6)",
                "ALTER TABLE users ADD COLUMN IF NOT EXISTS otp_expires TIMESTAMP",
                "ALTER TABLE users ADD COLUMN IF NOT EXISTS otp_attempts INTEGER DEFAULT 0",
                "ALTER TABLE users ADD COLUMN IF NOT EXISTS google_id VARCHAR(100)",
                "ALTER TABLE users ADD COLUMN IF NOT EXISTS profile_picture VARCHAR(255)"
            ]
            
            # Store table migrations
            store_migrations = [
                "ALTER TABLE stores ADD COLUMN IF NOT EXISTS facebook VARCHAR(100)",
                "ALTER TABLE stores ADD COLUMN IF NOT EXISTS tiktok VARCHAR(100)",
                "ALTER TABLE stores ADD COLUMN IF NOT EXISTS google_maps TEXT",
                "ALTER TABLE stores ADD COLUMN IF NOT EXISTS website VARCHAR(255)",
                "ALTER TABLE stores ADD COLUMN IF NOT EXISTS email VARCHAR(120)",
                "ALTER TABLE stores ADD COLUMN IF NOT EXISTS contact_preferences JSON"
            ]
            
            # Run user migrations
            print("\n👤 Migrating users table...")
            for migration in user_migrations:
                try:
                    db.session.execute(text(migration))
                    column_name = migration.split("ADD COLUMN IF NOT EXISTS ")[1].split(" ")[0]
                    print(f"  ✅ Added {column_name}")
                except Exception as e:
                    column_name = migration.split("ADD COLUMN IF NOT EXISTS ")[1].split(" ")[0]
                    print(f"  ⚠️  {column_name}: {e}")
            
            # Run store migrations
            print("\n🏪 Migrating stores table...")
            for migration in store_migrations:
                try:
                    db.session.execute(text(migration))
                    column_name = migration.split("ADD COLUMN IF NOT EXISTS ")[1].split(" ")[0]
                    print(f"  ✅ Added {column_name}")
                except Exception as e:
                    column_name = migration.split("ADD COLUMN IF NOT EXISTS ")[1].split(" ")[0]
                    print(f"  ⚠️  {column_name}: {e}")
            
            # Add unique constraint for google_id if it doesn't exist
            try:
                db.session.execute(text("ALTER TABLE users ADD CONSTRAINT users_google_id_unique UNIQUE (google_id)"))
                print("  ✅ Added unique constraint for google_id")
            except Exception as e:
                print(f"  ⚠️  google_id unique constraint: {e}")
            
            # Update existing stores with default contact preferences
            print("\n🔧 Updating existing stores with default contact preferences...")
            try:
                result = db.session.execute(text("""
                    UPDATE stores 
                    SET contact_preferences = '{"telegram": true, "whatsapp": true, "phone": true, "instagram": true, "facebook": true, "tiktok": true, "google_maps": true, "website": true, "email": true}'::json
                    WHERE contact_preferences IS NULL
                """))
                print(f"  ✅ Updated {result.rowcount} stores with default contact preferences")
            except Exception as e:
                print(f"  ⚠️  Contact preferences update: {e}")
            
            # Commit all changes
            db.session.commit()
            
        else:
            print("📊 Using SQLite - recreating tables...")
            db.create_all()
        
        print("\n🎉 Database migration completed successfully!")
        print("\n✨ New features now available:")
        print("  • Email verification and password reset")
        print("  • OTP authentication")
        print("  • Google OAuth support")
        print("  • Extended contact information for stores")
        print("  • Contact preferences management")
        print("\n🚀 Your authentication system is ready!")

if __name__ == '__main__':
    run_migration()
