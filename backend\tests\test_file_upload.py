import os
import unittest
import tempfile
from io import BytesIO

from app import create_app
from extensions import db
from models.user import User
from models.store import Store
from models.product import Product
from utils.file_upload import allowed_image_file, allowed_video_file, save_file

class FileUploadTestCase(unittest.TestCase):
    def setUp(self):
        self.app = create_app(test_config={
            'TESTING': True,
            'SQLALCHEMY_DATABASE_URI': 'sqlite:///:memory:',
            'UPLOAD_FOLDER': tempfile.mkdtemp()
        })
        self.client = self.app.test_client()
        
        with self.app.app_context():
            db.create_all()
            
            # Create test user
            test_user = User(
                email='<EMAIL>',
                password='password123',
                name='Test User',
                role='vendor'
            )
            db.session.add(test_user)
            
            # Create test store
            test_store = Store(
                name='Test Store',
                user_id=1,
                description='Test store description'
            )
            db.session.add(test_store)
            
            # Create test product
            test_product = Product(
                name='Test Product',
                price=9.99,
                store_id=1
            )
            db.session.add(test_product)
            
            db.session.commit()
    
    def tearDown(self):
        with self.app.app_context():
            db.session.remove()
            db.drop_all()
    
    def test_allowed_image_file(self):
        """Test the allowed_image_file function"""
        self.assertTrue(allowed_image_file('test.jpg'))
        self.assertTrue(allowed_image_file('test.jpeg'))
        self.assertTrue(allowed_image_file('test.png'))
        self.assertTrue(allowed_image_file('test.gif'))
        self.assertTrue(allowed_image_file('test.webp'))
        self.assertFalse(allowed_image_file('test.txt'))
        self.assertFalse(allowed_image_file('test.mp4'))
    
    def test_allowed_video_file(self):
        """Test the allowed_video_file function"""
        self.assertTrue(allowed_video_file('test.mp4'))
        self.assertTrue(allowed_video_file('test.webm'))
        self.assertTrue(allowed_video_file('test.mov'))
        self.assertTrue(allowed_video_file('test.avi'))
        self.assertFalse(allowed_video_file('test.txt'))
        self.assertFalse(allowed_video_file('test.jpg'))
    
    def test_upload_image_endpoint(self):
        """Test the upload image endpoint"""
        # Login as test user
        response = self.client.post('/api/auth/login', json={
            'email': '<EMAIL>',
            'password': 'password123'
        })
        token = response.json.get('token')
        
        # Create a test image
        image_data = BytesIO(b'fake image data')
        
        # Test uploading an image
        response = self.client.post(
            '/api/upload/image',
            data={'file': (image_data, 'test.jpg')},
            headers={'Authorization': f'Bearer {token}'},
            content_type='multipart/form-data'
        )
        
        self.assertEqual(response.status_code, 201)
        self.assertIn('file_url', response.json)
        self.assertIn('file_path', response.json)
    
    def test_upload_product_image(self):
        """Test uploading an image to a product"""
        # Login as test user
        response = self.client.post('/api/auth/login', json={
            'email': '<EMAIL>',
            'password': 'password123'
        })
        token = response.json.get('token')
        
        # Create a test image
        image_data = BytesIO(b'fake image data')
        
        # Test uploading an image to a product
        response = self.client.post(
            '/api/product/1/upload-image',
            data={'file': (image_data, 'test.jpg')},
            headers={'Authorization': f'Bearer {token}'},
            content_type='multipart/form-data'
        )
        
        self.assertEqual(response.status_code, 201)
        self.assertIn('file_url', response.json)
        self.assertIn('product', response.json)
        self.assertEqual(response.json['product']['id'], 1)
        self.assertIsNotNone(response.json['product']['image_url'])

if __name__ == '__main__':
    unittest.main()
