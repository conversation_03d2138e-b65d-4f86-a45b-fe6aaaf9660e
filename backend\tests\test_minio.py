import urllib3
urllib3.disable_warnings()
from minio import Minio

# Create MinIO client with SSL verification disabled
client = Minio(
    '35.240.129.146:9000',
    access_key='admin',
    secret_key='password',
    secure=True,
    http_client=urllib3.PoolManager(cert_reqs='CERT_NONE')
)

print('Connected to MinIO successfully')

# List buckets
buckets = client.list_buckets()
print(f"Buckets: {[b.name for b in buckets]}")

# List all objects in the uploads bucket
print("\nListing all objects in 'uploads' bucket:")
objects = client.list_objects('uploads', recursive=True)
for obj in objects:
    print(f"Object: {obj.object_name}, Size: {obj.size}, Last Modified: {obj.last_modified}")

# Try to get a specific file
try:
    filename = '67faf9544b8a4886a896b4b45a4186ee_ChatGPT_Image_May_17_2025_02_09_53_AM.png'
    obj = client.get_object('uploads', filename)
    print(f'File found: {filename}')
    print(f'Content type: {obj.headers.get("content-type")}')
    print(f'Size: {obj.headers.get("content-length")} bytes')
except Exception as e:
    print(f'Error getting file: {e}')

# Try to get another file
try:
    filename = '9602fd443c8b4832b7a98bbd439ccf74_ChatGPT_Image_May_17_2025_02_09_53_AM.png'
    obj = client.get_object('uploads', filename)
    print(f'File found: {filename}')
    print(f'Content type: {obj.headers.get("content-type")}')
    print(f'Size: {obj.headers.get("content-length")} bytes')
except Exception as e:
    print(f'Error getting file: {e}')
