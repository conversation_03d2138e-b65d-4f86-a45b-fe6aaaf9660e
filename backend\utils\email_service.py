import smtplib
import os
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from flask import current_app
import logging

logger = logging.getLogger(__name__)

class EmailService:
    def __init__(self):
        # Use Flask app config if available, otherwise fall back to environment variables
        if current_app:
            self.smtp_server = current_app.config.get('SMTP_SERVER', 'smtp.gmail.com')
            self.smtp_port = current_app.config.get('SMTP_PORT', 587)
            self.smtp_username = current_app.config.get('SMTP_USERNAME', '<EMAIL>')
            self.smtp_password = current_app.config.get('SMTP_PASSWORD', 'your-app-password')
            self.from_email = current_app.config.get('FROM_EMAIL', '<EMAIL>')
            self.from_name = current_app.config.get('FROM_NAME', 'Vsable')
        else:
            self.smtp_server = os.environ.get('SMTP_SERVER', 'smtp.gmail.com')
            self.smtp_port = int(os.environ.get('SMTP_PORT', '587'))
            self.smtp_username = os.environ.get('SMTP_USERNAME', '<EMAIL>')
            self.smtp_password = os.environ.get('SMTP_PASSWORD', 'your-app-password')
            self.from_email = os.environ.get('FROM_EMAIL', '<EMAIL>')
            self.from_name = os.environ.get('FROM_NAME', 'Vsable')

    def send_email(self, to_email, subject, html_content, text_content=None):
        """Send an email"""
        try:
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = f"{self.from_name} <{self.from_email}>"
            msg['To'] = to_email

            # Add text content
            if text_content:
                text_part = MIMEText(text_content, 'plain')
                msg.attach(text_part)

            # Add HTML content
            html_part = MIMEText(html_content, 'html')
            msg.attach(html_part)

            # Send email
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.smtp_username, self.smtp_password)
                server.send_message(msg)

            logger.info(f"Email sent successfully to {to_email}")
            return True

        except Exception as e:
            logger.error(f"Failed to send email to {to_email}: {str(e)}")
            return False

    def send_otp_email(self, to_email, otp_code, user_name):
        """Send OTP verification email"""
        subject = "Your Vsable Verification Code"

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Verification Code</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: #000; color: white; padding: 20px; text-align: center; }}
                .content {{ background: #f9f9f9; padding: 30px; }}
                .otp-code {{
                    font-size: 32px;
                    font-weight: bold;
                    color: #000;
                    text-align: center;
                    padding: 20px;
                    background: white;
                    border: 2px solid #000;
                    margin: 20px 0;
                    letter-spacing: 5px;
                }}
                .footer {{ background: #000; color: white; padding: 20px; text-align: center; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Vsable</h1>
                </div>
                <div class="content">
                    <h2>Hello {user_name},</h2>
                    <p>Your verification code is:</p>
                    <div class="otp-code">{otp_code}</div>
                    <p>This code will expire in 10 minutes. Please do not share this code with anyone.</p>
                    <p>If you didn't request this code, please ignore this email.</p>
                </div>
                <div class="footer">
                    <p>&copy; 2024 Vsable. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        """

        text_content = f"""
        Hello {user_name},

        Your Vsable verification code is: {otp_code}

        This code will expire in 10 minutes. Please do not share this code with anyone.

        If you didn't request this code, please ignore this email.

        Best regards,
        Vsable Team
        """

        return self.send_email(to_email, subject, html_content, text_content)

    def send_password_reset_email(self, to_email, reset_token, user_name):
        """Send password reset email"""
        reset_url = f"{os.environ.get('FRONTEND_URL', 'http://localhost:3000')}/reset-password?token={reset_token}"
        subject = "Reset Your Vsable Password"

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Password Reset</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: #000; color: white; padding: 20px; text-align: center; }}
                .content {{ background: #f9f9f9; padding: 30px; }}
                .button {{
                    display: inline-block;
                    background: #000;
                    color: white;
                    padding: 12px 30px;
                    text-decoration: none;
                    border-radius: 5px;
                    margin: 20px 0;
                }}
                .footer {{ background: #000; color: white; padding: 20px; text-align: center; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Vsable</h1>
                </div>
                <div class="content">
                    <h2>Hello {user_name},</h2>
                    <p>You requested to reset your password. Click the button below to reset it:</p>
                    <p style="text-align: center;">
                        <a href="{reset_url}" class="button">Reset Password</a>
                    </p>
                    <p>This link will expire in 1 hour. If you didn't request this reset, please ignore this email.</p>
                    <p>If the button doesn't work, copy and paste this link into your browser:</p>
                    <p style="word-break: break-all;">{reset_url}</p>
                </div>
                <div class="footer">
                    <p>&copy; 2024 Vsable. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        """

        text_content = f"""
        Hello {user_name},

        You requested to reset your password. Click the link below to reset it:

        {reset_url}

        This link will expire in 1 hour. If you didn't request this reset, please ignore this email.

        Best regards,
        Vsable Team
        """

        return self.send_email(to_email, subject, html_content, text_content)

    def send_welcome_email(self, to_email, user_name, verification_token=None):
        """Send welcome email with optional email verification"""
        subject = "Welcome to Vsable!"

        verification_section = ""
        if verification_token:
            verification_url = f"{os.environ.get('FRONTEND_URL', 'http://localhost:3000')}/verify-email?token={verification_token}"
            verification_section = f"""
                <p>Please verify your email address by clicking the button below:</p>
                <p style="text-align: center;">
                    <a href="{verification_url}" class="button">Verify Email</a>
                </p>
            """

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Welcome to Vsable</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: #000; color: white; padding: 20px; text-align: center; }}
                .content {{ background: #f9f9f9; padding: 30px; }}
                .button {{
                    display: inline-block;
                    background: #000;
                    color: white;
                    padding: 12px 30px;
                    text-decoration: none;
                    border-radius: 5px;
                    margin: 20px 0;
                }}
                .footer {{ background: #000; color: white; padding: 20px; text-align: center; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Vsable</h1>
                </div>
                <div class="content">
                    <h2>Welcome {user_name}!</h2>
                    <p>Thank you for joining Vsable, the multi-vendor platform where you can discover unique products from various vendors.</p>
                    {verification_section}
                    <p>Get started by exploring our marketplace or creating your own store if you're a vendor.</p>
                    <p>If you have any questions, feel free to contact our support team.</p>
                </div>
                <div class="footer">
                    <p>&copy; 2024 Vsable. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        """

        return self.send_email(to_email, subject, html_content)

# Global email service instance
email_service = EmailService()
