import json
import os
import uuid
from werkzeug.utils import secure_filename
from flask import current_app
import urllib3
from minio import Minio
from minio.error import S3Error

# Disable insecure HTTPS warnings in development
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Allowed file extensions
ALLOWED_IMAGE_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
ALLOWED_VIDEO_EXTENSIONS = {'mp4', 'webm', 'mov', 'avi'}

# Maximum file sizes (in bytes)
MAX_IMAGE_SIZE = 5 * 1024 * 1024  # 5MB
MAX_VIDEO_SIZE = 50 * 1024 * 1024  # 50MB

# Storage type
STORAGE_TYPE = os.environ.get('STORAGE_TYPE', 'gcs').lower()  # 'gcs' or 'minio'

def allowed_image_file(filename):
    """Check if the file is an allowed image type"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_IMAGE_EXTENSIONS

def allowed_video_file(filename):
    """Check if the file is an allowed video type"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_VIDEO_EXTENSIONS

def get_minio_client():
    """Get MinIO client with proper SSL handling"""
    try:
        minio_endpoint = current_app.config.get('MINIO_ENDPOINT', '35.240.129.146:9000')
        minio_access_key = current_app.config.get('MINIO_ACCESS_KEY', 'minioadmin')
        minio_secret_key = current_app.config.get('MINIO_SECRET_KEY', 'minioadmin')
        minio_secure = current_app.config.get('MINIO_SECURE', True)
        verify_ssl = current_app.config.get('MINIO_VERIFY_SSL', False)

        # Clean endpoint URL
        if minio_endpoint.startswith(('http://', 'https://')):
            minio_endpoint = minio_endpoint.split('//')[-1]

        print(f"Connecting to MinIO: {minio_endpoint} (secure={minio_secure}, verify_ssl={verify_ssl})")
        
        return Minio(
            endpoint=minio_endpoint,
            access_key=minio_access_key,
            secret_key=minio_secret_key,
            secure=minio_secure,
            http_client=None if verify_ssl else urllib3.PoolManager(
                timeout=urllib3.Timeout.DEFAULT_TIMEOUT,
                cert_reqs='CERT_NONE',
                maxsize=10
            )
        )
    except Exception as e:
        print(f"Error creating MinIO client: {str(e)}")
        raise

def upload_to_minio(file, object_name, content_type=None):
    """Upload file to MinIO with improved error handling"""
    try:
        minio_client = get_minio_client()
        bucket_name = current_app.config.get('MINIO_BUCKET', 'uploads')
        
        # Ensure bucket exists
        try:
            if not minio_client.bucket_exists(bucket_name):
                print(f"Creating bucket: {bucket_name}")
                minio_client.make_bucket(bucket_name)
                # Set bucket policy to public-read if needed
                policy = {
                    "Version": "2012-10-17",
                    "Statement": [
                        {
                            "Effect": "Allow",
                            "Principal": {"AWS": "*"},
                            "Action": ["s3:GetObject"],
                            "Resource": [f"arn:aws:s3:::{bucket_name}/*"]
                        }
                    ]
                }
                minio_client.set_bucket_policy(bucket_name, json.dumps(policy))
        except Exception as e:
            print(f"Bucket creation error (non-fatal): {str(e)}")

        # Get file size and reset file pointer
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)  # Reset to beginning

        # Upload file with known size
        result = minio_client.put_object(
            bucket_name,
            object_name,
            file,
            length=file_size,  # Specify the file size
            content_type=content_type or 'application/octet-stream'
        )
        print(f"Successfully uploaded {result.object_name} (etag: {result.etag})")
        
        # Construct the full URL
        minio_url = current_app.config.get('MINIO_URL', 'https://35.240.129.146:9000')
        return f"{minio_url}/{bucket_name}/{object_name}"
    
    except Exception as e:
        print(f"MinIO upload error: {str(e)}")
        raise

def save_file(file, file_type='image', use_minio=False):
    """Save a file to the appropriate storage location"""
    # Validate file type
    if file_type == 'image' and not allowed_image_file(file.filename):
        raise ValueError(f"Invalid image format. Allowed formats: {', '.join(ALLOWED_IMAGE_EXTENSIONS)}")
    elif file_type == 'video' and not allowed_video_file(file.filename):
        raise ValueError(f"Invalid video format. Allowed formats: {', '.join(ALLOWED_VIDEO_EXTENSIONS)}")

    # Validate file size
    file.seek(0, os.SEEK_END)
    file_size = file.tell()
    file.seek(0)  # Reset file pointer

    if file_type == 'image' and file_size > MAX_IMAGE_SIZE:
        raise ValueError(f"Image size exceeds the maximum allowed size of {MAX_IMAGE_SIZE/1024/1024}MB")
    elif file_type == 'video' and file_size > MAX_VIDEO_SIZE:
        raise ValueError(f"Video size exceeds the maximum allowed size of {MAX_VIDEO_SIZE/1024/1024}MB")

    # Generate a unique filename
    filename = secure_filename(file.filename)
    unique_filename = f"{uuid.uuid4().hex}_{filename}"
    
    # Use a consistent path format
    relative_path = f"uploads/{file_type}s/{unique_filename}"

    if use_minio and current_app.config.get('MINIO_ENDPOINT'):
        try:
            # Upload to MinIO using the same path format
            upload_to_minio(file, relative_path, content_type=file.content_type)
            print(f"File uploaded to MinIO: {relative_path}")
            return relative_path
        except Exception as e:
            print(f"Error uploading to MinIO: {str(e)}")
            print("Falling back to local storage")

    # For local storage, maintain the same path structure
    upload_folder = os.path.join(current_app.root_path, 'uploads', file_type + 's')
    os.makedirs(upload_folder, exist_ok=True)
    
    file_path = os.path.join(upload_folder, unique_filename)
    file.save(file_path)
    
    return relative_path

def handle_upload_error(e: Exception, context: str = '') -> tuple[str, int]:
    """
    Handle upload errors and return appropriate error messages and status codes
    """
    if isinstance(e, ValueError):
        return str(e), 400
    elif isinstance(e, S3Error):
        print(f"MinIO error in {context}: {str(e)}")
        return f"Storage error: {str(e)}", 500
    else:
        print(f"Unexpected error in {context}: {str(e)}")
        return f"Error uploading file: {str(e)}", 500
