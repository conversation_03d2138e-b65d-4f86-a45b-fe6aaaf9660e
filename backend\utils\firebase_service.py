"""
Firebase Admin SDK service for verifying Firebase ID tokens
"""

import os
import json
import firebase_admin
from firebase_admin import credentials, auth
from flask import current_app

class FirebaseService:
    _app = None
    _initialized = False

    @classmethod
    def initialize(cls):
        """Initialize Firebase Admin SDK"""
        if cls._initialized:
            return

        # Check if we're in development mode (no Firebase credentials)
        development_mode = os.environ.get('FLASK_ENV') == 'development' or os.environ.get('FLASK_DEBUG') == 'True'

        if development_mode:
            print("Running in development mode - Firebase Admin SDK disabled")
            print("Using mock Firebase verification for development")
            cls._initialized = False
            return

        try:
            # Check if Firebase is already initialized
            if firebase_admin._apps:
                cls._app = firebase_admin.get_app()
                cls._initialized = True
                return

            # Try to get service account from environment variable
            service_account_path = os.environ.get('FIREBASE_SERVICE_ACCOUNT_PATH')

            if service_account_path and os.path.exists(service_account_path):
                # Use service account file
                cred = credentials.Certificate(service_account_path)
                cls._app = firebase_admin.initialize_app(cred)
                print("Firebase initialized with service account file")
            else:
                # Try to get service account from environment variable as JSON
                service_account_json = os.environ.get('FIREBASE_SERVICE_ACCOUNT_JSON')

                if service_account_json:
                    # Parse JSON from environment variable
                    service_account_info = json.loads(service_account_json)
                    cred = credentials.Certificate(service_account_info)
                    cls._app = firebase_admin.initialize_app(cred)
                    print("Firebase initialized with service account JSON")
                else:
                    # No credentials found - use development mode
                    print("No Firebase credentials found - using development mode")
                    cls._initialized = False
                    return

            cls._initialized = True

        except Exception as e:
            print(f"Firebase initialization error: {e}")
            print("Falling back to development mode")
            cls._initialized = False

    @classmethod
    def verify_id_token(cls, id_token, user_data=None):
        """
        Verify Firebase ID token and return decoded token

        Args:
            id_token (str): Firebase ID token
            user_data (dict): User data from frontend (for development mode)

        Returns:
            dict: Decoded token data or None if verification fails
        """
        if not cls._initialized:
            cls.initialize()

        if not cls._initialized:
            # Firebase not available, use development mode
            print("Warning: Firebase not initialized, using development mode")
            # In development mode, we'll use the user data from the frontend
            # This should only be used for development/testing
            if user_data:
                return {
                    'uid': f'dev_uid_{user_data.get("firebaseUid", id_token[-10:])}',
                    'email': user_data.get('email', '<EMAIL>'),
                    'name': user_data.get('name', 'Development User'),
                    'picture': user_data.get('photoURL'),
                    'email_verified': True
                }
            else:
                return {
                    'uid': f'dev_uid_{id_token[-10:]}',
                    'email': '<EMAIL>',
                    'name': 'Development User',
                    'picture': None,
                    'email_verified': True
                }

        try:
            # Verify the ID token
            decoded_token = auth.verify_id_token(id_token)

            return {
                'uid': decoded_token.get('uid'),
                'email': decoded_token.get('email'),
                'name': decoded_token.get('name'),
                'picture': decoded_token.get('picture'),
                'email_verified': decoded_token.get('email_verified', False)
            }

        except auth.InvalidIdTokenError:
            print("Invalid Firebase ID token")
            return None
        except auth.ExpiredIdTokenError:
            print("Expired Firebase ID token")
            return None
        except Exception as e:
            print(f"Firebase token verification error: {e}")
            return None

    @classmethod
    def get_user(cls, uid):
        """
        Get user information from Firebase

        Args:
            uid (str): Firebase user UID

        Returns:
            dict: User data or None if not found
        """
        if not cls._initialized:
            cls.initialize()

        if not cls._initialized:
            return None

        try:
            user_record = auth.get_user(uid)
            return {
                'uid': user_record.uid,
                'email': user_record.email,
                'display_name': user_record.display_name,
                'photo_url': user_record.photo_url,
                'email_verified': user_record.email_verified,
                'disabled': user_record.disabled
            }
        except auth.UserNotFoundError:
            print(f"Firebase user not found: {uid}")
            return None
        except Exception as e:
            print(f"Firebase get user error: {e}")
            return None

# Initialize Firebase when module is imported
firebase_service = FirebaseService()
firebase_service.initialize()
