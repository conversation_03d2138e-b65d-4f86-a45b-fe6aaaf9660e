[{"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\admin\\dashboard\\page.tsx": "1", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\layout.tsx": "2", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\login\\page.tsx": "3", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\page.tsx": "4", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\register\\page.tsx": "5", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\store\\[slug]\\page.tsx": "6", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\dashboard\\page.tsx": "7", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\products\\page.tsx": "8", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\store\\edit-details\\page.tsx": "9", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\store\\page.tsx": "10", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\store-settings\\page.tsx": "11", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\FileUpload.tsx": "12", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\Navbar.tsx": "13", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\ProtectedRoute.tsx": "14", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\SafeImage.tsx": "15", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\store-templates\\TemplateDefault.tsx": "16", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\store-templates\\TemplateFive.tsx": "17", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\store-templates\\TemplateFour.tsx": "18", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\store-templates\\TemplateOne.tsx": "19", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\store-templates\\TemplateThree.tsx": "20", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\store-templates\\TemplateTwo.tsx": "21", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\vendor\\StoreTemplateSelector.tsx": "22", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\contexts\\AuthContext.tsx": "23", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\types\\index.ts": "24", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\utils\\imageUtils.ts": "25", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\forgot-password\\page.tsx": "26", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\not-found.tsx": "27", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\profile\\page.tsx": "28", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\reset-password\\page.tsx": "29", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\verify-email\\page.tsx": "30", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\verify-otp\\page.tsx": "31", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\ErrorBoundary.tsx": "32", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\GoogleSignIn.tsx": "33", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\LoadingSpinner.tsx": "34", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\vendor\\TemplatePreview.tsx": "35", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\hooks\\useFirebaseAuth.ts": "36", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\lib\\firebase.ts": "37", "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\RoleSelector.tsx": "38"}, {"size": 8040, "mtime": 1747620884604, "results": "39", "hashOfConfig": "40"}, {"size": 970, "mtime": 1747206055777, "results": "41", "hashOfConfig": "40"}, {"size": 6593, "mtime": 1748361381580, "results": "42", "hashOfConfig": "40"}, {"size": 6820, "mtime": 1748426330362, "results": "43", "hashOfConfig": "40"}, {"size": 6165, "mtime": 1748361521003, "results": "44", "hashOfConfig": "40"}, {"size": 6703, "mtime": 1747929182691, "results": "45", "hashOfConfig": "40"}, {"size": 14234, "mtime": 1747930930794, "results": "46", "hashOfConfig": "40"}, {"size": 25697, "mtime": 1747620803849, "results": "47", "hashOfConfig": "40"}, {"size": 2471, "mtime": 1747620185734, "results": "48", "hashOfConfig": "40"}, {"size": 26564, "mtime": 1748278129783, "results": "49", "hashOfConfig": "40"}, {"size": 5223, "mtime": 1747929362601, "results": "50", "hashOfConfig": "40"}, {"size": 8877, "mtime": 1748426411620, "results": "51", "hashOfConfig": "40"}, {"size": 8557, "mtime": 1748104197318, "results": "52", "hashOfConfig": "40"}, {"size": 1632, "mtime": 1747206870765, "results": "53", "hashOfConfig": "40"}, {"size": 1923, "mtime": 1747846584223, "results": "54", "hashOfConfig": "40"}, {"size": 11365, "mtime": 1748360688024, "results": "55", "hashOfConfig": "40"}, {"size": 7698, "mtime": 1748360620248, "results": "56", "hashOfConfig": "40"}, {"size": 8653, "mtime": 1748278377440, "results": "57", "hashOfConfig": "40"}, {"size": 16072, "mtime": 1748361841020, "results": "58", "hashOfConfig": "40"}, {"size": 10878, "mtime": 1748278398939, "results": "59", "hashOfConfig": "40"}, {"size": 15586, "mtime": 1748360144068, "results": "60", "hashOfConfig": "40"}, {"size": 8285, "mtime": 1748104298782, "results": "61", "hashOfConfig": "40"}, {"size": 7796, "mtime": 1748105586500, "results": "62", "hashOfConfig": "40"}, {"size": 1673, "mtime": 1748278153807, "results": "63", "hashOfConfig": "40"}, {"size": 5707, "mtime": 1747847275472, "results": "64", "hashOfConfig": "40"}, {"size": 6122, "mtime": 1748280162030, "results": "65", "hashOfConfig": "40"}, {"size": 2613, "mtime": 1748280223801, "results": "66", "hashOfConfig": "40"}, {"size": 11071, "mtime": 1748280292086, "results": "67", "hashOfConfig": "40"}, {"size": 8654, "mtime": 1748280555317, "results": "68", "hashOfConfig": "40"}, {"size": 7501, "mtime": 1748280671648, "results": "69", "hashOfConfig": "40"}, {"size": 8252, "mtime": 1748280438426, "results": "70", "hashOfConfig": "40"}, {"size": 3700, "mtime": 1748103981829, "results": "71", "hashOfConfig": "40"}, {"size": 5436, "mtime": 1748361570169, "results": "72", "hashOfConfig": "40"}, {"size": 3488, "mtime": 1748104008230, "results": "73", "hashOfConfig": "40"}, {"size": 8183, "mtime": 1748277808789, "results": "74", "hashOfConfig": "40"}, {"size": 2339, "mtime": 1748280507493, "results": "75", "hashOfConfig": "40"}, {"size": 1801, "mtime": 1748280767259, "results": "76", "hashOfConfig": "40"}, {"size": 3324, "mtime": 1748282585985, "results": "77", "hashOfConfig": "40"}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "kocb7e", {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\admin\\dashboard\\page.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\layout.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\login\\page.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\page.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\register\\page.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\store\\[slug]\\page.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\dashboard\\page.tsx", [], ["192"], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\products\\page.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\store\\edit-details\\page.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\store\\page.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\store-settings\\page.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\FileUpload.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\Navbar.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\ProtectedRoute.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\SafeImage.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\store-templates\\TemplateDefault.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\store-templates\\TemplateFive.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\store-templates\\TemplateFour.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\store-templates\\TemplateOne.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\store-templates\\TemplateThree.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\store-templates\\TemplateTwo.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\vendor\\StoreTemplateSelector.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\contexts\\AuthContext.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\types\\index.ts", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\utils\\imageUtils.ts", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\forgot-password\\page.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\not-found.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\profile\\page.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\reset-password\\page.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\verify-email\\page.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\verify-otp\\page.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\ErrorBoundary.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\GoogleSignIn.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\LoadingSpinner.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\vendor\\TemplatePreview.tsx", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\hooks\\useFirebaseAuth.ts", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\lib\\firebase.ts", [], [], "E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\RoleSelector.tsx", [], [], {"ruleId": "193", "severity": 1, "message": "194", "line": 287, "column": 33, "nodeType": "195", "endLine": 291, "endColumn": 35, "suppressions": "196"}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["197"], {"kind": "198", "justification": "199"}, "directive", ""]