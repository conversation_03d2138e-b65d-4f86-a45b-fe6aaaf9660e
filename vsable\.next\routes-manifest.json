{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/store/[slug]", "regex": "^/store/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/store/(?<nxtPslug>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/admin/dashboard", "regex": "^/admin/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/dashboard(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/forgot-password", "regex": "^/forgot\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/forgot\\-password(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}, {"page": "/register", "regex": "^/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/register(?:/)?$"}, {"page": "/reset-password", "regex": "^/reset\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/reset\\-password(?:/)?$"}, {"page": "/vendor/dashboard", "regex": "^/vendor/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/vendor/dashboard(?:/)?$"}, {"page": "/vendor/products", "regex": "^/vendor/products(?:/)?$", "routeKeys": {}, "namedRegex": "^/vendor/products(?:/)?$"}, {"page": "/vendor/store", "regex": "^/vendor/store(?:/)?$", "routeKeys": {}, "namedRegex": "^/vendor/store(?:/)?$"}, {"page": "/vendor/store/edit-details", "regex": "^/vendor/store/edit\\-details(?:/)?$", "routeKeys": {}, "namedRegex": "^/vendor/store/edit\\-details(?:/)?$"}, {"page": "/vendor/store-settings", "regex": "^/vendor/store\\-settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/vendor/store\\-settings(?:/)?$"}, {"page": "/verify-email", "regex": "^/verify\\-email(?:/)?$", "routeKeys": {}, "namedRegex": "^/verify\\-email(?:/)?$"}, {"page": "/verify-otp", "regex": "^/verify\\-otp(?:/)?$", "routeKeys": {}, "namedRegex": "^/verify\\-otp(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}