(()=>{var e={};e.id=957,e.ids=[957],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19573:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(60687),a=r(43210);r(51060);var d=r(63213),i=r(20769),l=r(17019);function n(){let{user:e}=(0,d.A)(),[s,r]=(0,a.useState)([]),[n,c]=(0,a.useState)(!0),[o,x]=(0,a.useState)(null);return(0,t.jsx)(i.A,{allowedRoles:["admin"],children:(0,t.jsx)("div",{className:"bg-gray-100 dark:bg-gray-900 min-h-screen",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,t.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-white",children:"Admin Dashboard"}),(0,t.jsxs)("p",{className:"mt-1 text-sm text-gray-600 dark:text-gray-400",children:["Welcome back, ",e?.name]}),(0,t.jsxs)("div",{className:"mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:[(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,t.jsx)("div",{className:"p-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(l.cfS,{className:"h-6 w-6 text-gray-400"})}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"Total Users"}),(0,t.jsx)("dd",{children:(0,t.jsx)("div",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"256"})})]})})]})})}),(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,t.jsx)("div",{className:"p-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(l.p45,{className:"h-6 w-6 text-gray-400"})}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"Total Vendors"}),(0,t.jsx)("dd",{children:(0,t.jsx)("div",{className:"text-lg font-medium text-gray-900 dark:text-white",children:s.length})})]})})]})})}),(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,t.jsx)("div",{className:"p-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(l.y52,{className:"h-6 w-6 text-gray-400"})}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"Total Orders"}),(0,t.jsx)("dd",{children:(0,t.jsx)("div",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"1,234"})})]})})]})})}),(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,t.jsx)("div",{className:"p-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(l.z8N,{className:"h-6 w-6 text-gray-400"})}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"Total Revenue"}),(0,t.jsx)("dd",{children:(0,t.jsx)("div",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"$89,432"})})]})})]})})})]}),(0,t.jsxs)("div",{className:"mt-8",children:[(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Registered Vendors"}),n?(0,t.jsx)("div",{className:"mt-4 flex justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):o?(0,t.jsx)("div",{className:"mt-4 bg-red-50 dark:bg-red-900/20 p-4 rounded-md",children:(0,t.jsx)("p",{className:"text-sm text-red-700 dark:text-red-400",children:o})}):(0,t.jsx)("div",{className:"mt-4 bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md",children:(0,t.jsx)("ul",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:s.map(e=>(0,t.jsx)("li",{children:(0,t.jsxs)("div",{className:"px-4 py-4 sm:px-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-blue-600 dark:text-blue-400 truncate",children:e.name}),(0,t.jsx)("div",{className:"ml-2 flex-shrink-0 flex",children:(0,t.jsx)("p",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",children:"Active"})})]}),(0,t.jsxs)("div",{className:"mt-2 sm:flex sm:justify-between",children:[(0,t.jsx)("div",{className:"sm:flex",children:(0,t.jsx)("p",{className:"flex items-center text-sm text-gray-500 dark:text-gray-400",children:e.email})}),(0,t.jsx)("div",{className:"mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400 sm:mt-0",children:(0,t.jsxs)("p",{children:["Joined: ",new Date(e.created_at).toLocaleDateString()]})})]})]})},e.id))})})]})]})})})})}},20652:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var t=r(65239),a=r(48088),d=r(88170),i=r.n(d),l=r(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(s,n);let c={children:["",{children:["admin",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,71031)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\admin\\dashboard\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\admin\\dashboard\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/dashboard/page",pathname:"/admin/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},20769:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});var t=r(60687),a=r(43210),d=r(16189),i=r(63213);let l=({children:e,allowedRoles:s=[]})=>{let{user:r,loading:l,isAuthenticated:n}=(0,i.A)(),c=(0,d.useRouter)();return((0,a.useEffect)(()=>{l||n?!l&&n&&s.length>0&&r&&!s.includes(r.role)&&("admin"===r.role?c.replace("/admin/dashboard"):"vendor"===r.role?c.replace("/vendor/dashboard"):c.replace("/")):c.replace("/login")},[l,n,r,c,s]),l)?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):!n||s.length>0&&r&&!s.includes(r.role)?null:(0,t.jsx)(t.Fragment,{children:e})}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},45051:(e,s,r)=>{Promise.resolve().then(r.bind(r,19573))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71031:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\StartUpJourney\\\\Development\\\\Test_dev\\\\vsable\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\admin\\dashboard\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79395:(e,s,r)=>{Promise.resolve().then(r.bind(r,71031))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,579,846],()=>r(20652));module.exports=t})();