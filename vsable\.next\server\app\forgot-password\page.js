(()=>{var e={};e.id=162,e.ids=[162],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10038:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(60687),a=t(43210),o=t(85814),i=t.n(o),n=t(51060),l=t(17019),d=t(33823);function c(){let[e,r]=(0,a.useState)(""),[t,o]=(0,a.useState)(!1),[c,p]=(0,a.useState)(!1),[u,x]=(0,a.useState)(""),m=async r=>{if(r.preventDefault(),x(""),o(!0),!e){x("Please enter your email address"),o(!1);return}try{await n.A.post("http://localhost:5000/api/auth/forgot-password",{email:e}),p(!0)}catch(e){x(n.A.isAxiosError(e)&&e.response?.data?.message||"Failed to send reset email")}finally{o(!1)}};return c?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-6",children:(0,s.jsx)(l.YrT,{className:"h-8 w-8 text-green-600 dark:text-green-400"})}),(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Check your email"}),(0,s.jsxs)("p",{className:"mt-4 text-gray-600 dark:text-gray-400",children:["If an account with ",(0,s.jsx)("strong",{children:e})," exists, you will receive a password reset link shortly."]}),(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-500 dark:text-gray-500",children:"Didn't receive the email? Check your spam folder or try again."})]}),(0,s.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,s.jsx)("button",{onClick:()=>{p(!1),r("")},className:"w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-colors",children:"Try different email"}),(0,s.jsx)(i(),{href:"/login",className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-colors",children:"Back to login"})]})]})}):(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsx)("div",{children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Forgot your password?"}),(0,s.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"Enter your email address and we'll send you a link to reset your password."})]})}),(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:m,children:[u&&(0,s.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4",children:(0,s.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:u})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"sr-only",children:"Email address"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(l.pHD,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>r(e.target.value),className:"appearance-none rounded-md relative block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm",placeholder:"Enter your email address",disabled:t})]})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:t,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:t?(0,s.jsx)(d.Ay,{size:"sm",color:"white"}):"Send reset link"})}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)(i(),{href:"/login",className:"inline-flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white transition-colors",children:[(0,s.jsx)(l.kRp,{className:"mr-2 h-4 w-4"}),"Back to login"]})})]})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12281:(e,r,t)=>{Promise.resolve().then(t.bind(t,10038))},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33823:(e,r,t)=>{"use strict";t.d(r,{Ay:()=>a});var s=t(60687);t(43210);let a=({size:e="md",color:r="black",text:t,fullScreen:a=!1})=>{let o=(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,s.jsx)("div",{className:`
          ${{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12",xl:"h-16 w-16"}[e]} 
          ${{black:"border-black border-t-transparent",white:"border-white border-t-transparent",gray:"border-gray-300 border-t-transparent dark:border-gray-600"}[r]} 
          border-2 rounded-full animate-spin
        `}),t&&(0,s.jsx)("p",{className:`mt-3 ${{sm:"text-sm",md:"text-base",lg:"text-lg",xl:"text-xl"}[e]} text-gray-600 dark:text-gray-400`,children:t})]});return a?(0,s.jsx)("div",{className:"fixed inset-0 bg-white dark:bg-gray-900 flex items-center justify-center z-50",children:o}):o}},33873:e=>{"use strict";e.exports=require("path")},36200:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\StartUpJourney\\\\Development\\\\Test_dev\\\\vsable\\\\src\\\\app\\\\forgot-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\forgot-password\\page.tsx","default")},41657:(e,r,t)=>{Promise.resolve().then(t.bind(t,36200))},43320:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=t(65239),a=t(48088),o=t(88170),i=t.n(o),n=t(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let d={children:["",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,36200)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\forgot-password\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\forgot-password\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/forgot-password/page",pathname:"/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,579,846],()=>t(43320));module.exports=s})();