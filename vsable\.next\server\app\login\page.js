(()=>{var e={};e.id=520,e.ids=[520],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10935:(e,r,t)=>{Promise.resolve().then(t.bind(t,94934))},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64079:(e,r,t)=>{Promise.resolve().then(t.bind(t,69488))},69488:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687),a=t(43210),o=t(85814),i=t.n(o),n=t(63213),l=t(82057);function d(){let[e,r]=(0,a.useState)(""),[t,o]=(0,a.useState)(""),[d,c]=(0,a.useState)(!1),[u,p]=(0,a.useState)(""),[m,g]=(0,a.useState)(!1),{login:x,loading:h,error:b,sendOTP:f}=(0,n.A)(),y=async r=>{if(r.preventDefault(),p(""),m){if(!e)return void p("Please enter your email address");try{await f(e),window.location.href=`/verify-otp?email=${encodeURIComponent(e)}`}catch{}}else{if(!e||!t)return void p("Please enter both email and password");try{await x(e,t,d)}catch{}}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white",children:"Sign in to your account"}),(0,s.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600 dark:text-gray-400",children:["Or"," ",(0,s.jsx)(i(),{href:"/register",className:"font-medium text-black hover:text-gray-700 dark:text-white dark:hover:text-gray-300",children:"create a new account"})]})]}),(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:y,children:[(b||u)&&(0,s.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,s.jsx)("div",{className:"flex",children:(0,s.jsx)("div",{className:"ml-3",children:(0,s.jsx)("h3",{className:"text-sm font-medium text-red-800",children:u||b})})})}),(0,s.jsxs)("div",{className:"rounded-md shadow-sm -space-y-px",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email-address",className:"sr-only",children:"Email address"}),(0,s.jsx)("input",{id:"email-address",name:"email",type:"email",autoComplete:"email",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-white",placeholder:"Email address",value:e,onChange:e=>r(e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"sr-only",children:"Password"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!m,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-white",placeholder:"Password",value:t,onChange:e=>o(e.target.value),disabled:h||m,style:{display:m?"none":"block"}})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsx)("button",{type:"button",onClick:()=>{g(!m),o(""),p("")},className:"text-sm text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white transition-colors",children:m?"Use password instead":"Login with OTP"})}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[!m&&(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-black focus:ring-black border-gray-300 rounded dark:bg-gray-800 dark:border-gray-700",checked:d,onChange:e=>c(e.target.checked)}),(0,s.jsx)("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-900 dark:text-gray-300",children:"Remember me"})]}),(0,s.jsx)("div",{className:"text-sm",children:(0,s.jsx)(i(),{href:"/forgot-password",className:"font-medium text-black hover:text-gray-700 dark:text-white dark:hover:text-gray-300 transition-colors",children:"Forgot your password?"})})]})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:h,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:h?m?"Sending OTP...":"Signing in...":m?"Send OTP":"Sign in"})}),(0,s.jsx)(l.A,{mode:"signin",disabled:h})]})]})})}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82057:(e,r,t)=>{"use strict";t.d(r,{A:()=>x});var s=t(60687),a=t(43210),o=t(73253),i=t(51060),n=t(63213),l=t(19978),d=t(67989);let c=["NEXT_PUBLIC_FIREBASE_API_KEY","NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN","NEXT_PUBLIC_FIREBASE_PROJECT_ID","NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET","NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID","NEXT_PUBLIC_FIREBASE_APP_ID"].filter(e=>!process.env[e]);c.length>0&&(console.warn(`Missing Firebase environment variables: ${c.join(", ")}`),console.warn("Firebase authentication will use mock mode."));let u=null,p=null,m=null;try{u=(0,d.Wp)({apiKey:"AIzaSyAgrJR95fG9_BqXSaPtYu8beGZz1wqkvGU",authDomain:"vsable-dev.firebaseapp.com",projectId:"vsable-dev",storageBucket:"vsable-dev.firebasestorage.app",messagingSenderId:"763818917063",appId:"1:763818917063:web:112971147059619b7e12e2"}),p=(0,l.xI)(u),(m=new l.HF).addScope("email"),m.addScope("profile"),console.log("Firebase initialized successfully")}catch(e){console.error("Firebase initialization error:",e),console.warn("Firebase authentication will use mock mode.")}let g=()=>{let[e,r]=(0,a.useState)(null),[t,s]=(0,a.useState)(!0),[o,i]=(0,a.useState)(null);(0,a.useEffect)(()=>{if(!p)return void s(!1);let e=(0,l.hg)(p,e=>{r(e),s(!1)});return()=>e()},[]);let n=async()=>{if(!e)return null;try{return await e.getIdToken()}catch(e){return console.error("Get ID token error:",e),i(e instanceof Error?e.message:"Failed to get ID token"),null}};return{user:e,loading:t,error:o,signInWithGoogle:async()=>{if(!p||!m)return i("Firebase is not properly configured"),null;try{return s(!0),i(null),(await (0,l.df)(p,m)).user}catch(e){return console.error("Google sign-in error:",e),i(e instanceof Error?e.message:"Failed to sign in with Google"),null}finally{s(!1)}},signOut:async()=>{if(!p)return void i("Firebase is not properly configured");try{await (0,l.CI)(p),r(null)}catch(e){console.error("Sign out error:",e),i(e instanceof Error?e.message:"Failed to sign out")}},getIdToken:n}},x=({mode:e,role:r="customer",onSuccess:t,onError:l,disabled:d=!1})=>{let[c,u]=(0,a.useState)(!1),{setUser:p}=(0,n.A)(),{signInWithGoogle:m,getIdToken:x,error:h}=g(),b=async()=>{u(!0);try{let e=await m();if(!e)throw Error(h||"Failed to sign in with Google");let s=await x();if(!s)throw Error("Failed to get authentication token");let a=await i.A.post("http://localhost:5000/api/auth/google",{idToken:s,firebaseUid:e.uid,email:e.email,name:e.displayName,photoURL:e.photoURL,role:r});p&&p(a.data.user),t&&t(s),"admin"===a.data.user.role?window.location.href="/admin/dashboard":"vendor"===a.data.user.role?window.location.href="/vendor/dashboard":window.location.href="/"}catch(r){console.error("Google OAuth error:",r);let e=r instanceof Error?r.message:"Google sign-in failed";l?l(r):alert(e)}finally{u(!1)}};return(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-gray-300 dark:border-gray-600"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,s.jsx)("span",{className:"px-2 bg-gray-50 dark:bg-gray-900 text-gray-500 dark:text-gray-400",children:"Or continue with"})})]}),(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsxs)("button",{type:"button",onClick:b,disabled:d||c,className:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,s.jsx)(o.F4b,{className:"h-5 w-5 mr-3"}),c?"Connecting...":"signin"===e?"Sign in with Google":"Sign up with Google"]})}),(0,s.jsxs)("div",{className:"mt-4 p-3 bg-gray-50 dark:bg-gray-900/20 border border-gray-200 dark:border-gray-800 rounded-md",children:[(0,s.jsxs)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:[(0,s.jsx)("strong",{children:"Firebase Setup Required:"})," Configure your Firebase project for Google OAuth:"]}),(0,s.jsxs)("ul",{className:"text-xs text-gray-600 dark:text-gray-400 mt-1 ml-4 list-disc",children:[(0,s.jsx)("li",{children:"Create Firebase project at console.firebase.google.com"}),(0,s.jsx)("li",{children:"Enable Google authentication in Firebase Auth"}),(0,s.jsx)("li",{children:"Update environment variables with Firebase config"})]})]})]})}},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},94934:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\StartUpJourney\\\\Development\\\\Test_dev\\\\vsable\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\login\\page.tsx","default")},95136:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=t(65239),a=t(48088),o=t(88170),i=t.n(o),n=t(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);t.d(r,l);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94934)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\login\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,579,26,846],()=>t(95136));module.exports=s})();