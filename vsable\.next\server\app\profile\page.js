(()=>{var e={};e.id=636,e.ids=[636],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5075:(e,r,t)=>{Promise.resolve().then(t.bind(t,75758))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},15308:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>d.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var s=t(65239),a=t(48088),i=t(88170),d=t.n(i),l=t(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);t.d(r,n);let o={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,75758)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\profile\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\profile\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33823:(e,r,t)=>{"use strict";t.d(r,{Ay:()=>a});var s=t(60687);t(43210);let a=({size:e="md",color:r="black",text:t,fullScreen:a=!1})=>{let i=(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,s.jsx)("div",{className:`
          ${{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12",xl:"h-16 w-16"}[e]} 
          ${{black:"border-black border-t-transparent",white:"border-white border-t-transparent",gray:"border-gray-300 border-t-transparent dark:border-gray-600"}[r]} 
          border-2 rounded-full animate-spin
        `}),t&&(0,s.jsx)("p",{className:`mt-3 ${{sm:"text-sm",md:"text-base",lg:"text-lg",xl:"text-xl"}[e]} text-gray-600 dark:text-gray-400`,children:t})]});return a?(0,s.jsx)("div",{className:"fixed inset-0 bg-white dark:bg-gray-900 flex items-center justify-center z-50",children:i}):i}},33873:e=>{"use strict";e.exports=require("path")},42027:(e,r,t)=>{Promise.resolve().then(t.bind(t,87032))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75758:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\StartUpJourney\\\\Development\\\\Test_dev\\\\vsable\\\\src\\\\app\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\profile\\page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},87032:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(60687),a=t(43210),i=t(63213),d=t(17019),l=t(33823),n=t(30474);function o(){let{user:e,updateProfile:r,loading:t}=(0,i.A)(),[o,c]=(0,a.useState)(!1),[x,m]=(0,a.useState)({name:e?.name||"",email:e?.email||""}),[u,p]=(0,a.useState)(!1),[g,b]=(0,a.useState)(""),[h,f]=(0,a.useState)("");if(t)return(0,s.jsx)(l.Ay,{fullScreen:!0,text:"Loading profile..."});if(!e)return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:"Access Denied"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Please log in to view your profile."})]})});let y=e=>{m({...x,[e.target.name]:e.target.value})},v=async e=>{e.preventDefault(),p(!0),f(""),b("");try{await r(x),b("Profile updated successfully!"),c(!1),setTimeout(()=>b(""),3e3)}catch(e){console.error("Profile update error:",e),f("Failed to update profile. Please try again.")}finally{p(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-12",children:(0,s.jsx)("div",{className:"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:[(0,s.jsx)("div",{className:"px-6 py-8 border-b border-gray-200 dark:border-gray-700",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"relative",children:[e.profile_picture?(0,s.jsx)(n.default,{src:e.profile_picture,alt:e.name,width:80,height:80,className:"rounded-full object-cover"}):(0,s.jsx)("div",{className:"w-20 h-20 bg-black dark:bg-white rounded-full flex items-center justify-center",children:(0,s.jsx)(d.JXP,{className:"h-10 w-10 text-white dark:text-black"})}),(0,s.jsx)("button",{className:"absolute bottom-0 right-0 bg-black dark:bg-white text-white dark:text-black rounded-full p-2 hover:bg-gray-800 dark:hover:bg-gray-200 transition-colors",children:(0,s.jsx)(d.PoE,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e.name}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 capitalize",children:e.role})]})]}),!o&&(0,s.jsx)("button",{onClick:()=>c(!0),className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors",children:"Edit Profile"})]})}),(0,s.jsxs)("div",{className:"px-6 py-8",children:[g&&(0,s.jsx)("div",{className:"mb-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(d.YrT,{className:"h-5 w-5 text-green-600 dark:text-green-400 mr-2"}),(0,s.jsx)("p",{className:"text-sm text-green-600 dark:text-green-400",children:g})]})}),h&&(0,s.jsx)("div",{className:"mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(d.yGN,{className:"h-5 w-5 text-red-600 dark:text-red-400 mr-2"}),(0,s.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:h})]})}),(0,s.jsxs)("form",{onSubmit:v,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Basic Information"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Full Name"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(d.JXP,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{type:"text",id:"name",name:"name",value:x.name,onChange:y,disabled:!o,className:"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-black focus:border-black dark:focus:ring-white dark:focus:border-white disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:cursor-not-allowed"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email Address"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(d.pHD,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{type:"email",id:"email",name:"email",value:x.email,onChange:y,disabled:!o,className:"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-black focus:border-black dark:focus:ring-white dark:focus:border-white disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:cursor-not-allowed"})]})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Account Status"}),(0,s.jsx)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(d.pcC,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Email Verification"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.email_verified?"Your email is verified":"Your email is not verified"})]})]}),(0,s.jsx)("div",{className:`px-3 py-1 rounded-full text-xs font-medium ${e.email_verified?"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200":"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200"}`,children:e.email_verified?"Verified":"Unverified"})]})})]}),o&&(0,s.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,s.jsx)("button",{type:"button",onClick:()=>{m({name:e.name,email:e.email}),c(!1),f("")},className:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors",children:"Cancel"}),(0,s.jsx)("button",{type:"submit",disabled:u,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:u?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(l.Ay,{size:"sm",color:"white"}),(0,s.jsx)("span",{className:"ml-2",children:"Saving..."})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.Bc_,{className:"mr-2 h-4 w-4"}),"Save Changes"]})})]})]})]})]})})})}},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,579,474,846],()=>t(15308));module.exports=s})();