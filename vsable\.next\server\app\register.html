<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/7e4b333761995f65.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-8ba223b515e4ba9a.js"/><script src="/_next/static/chunks/4bd1b696-9a99fb4dbb7f9f2a.js" async=""></script><script src="/_next/static/chunks/684-b41e262155657333.js" async=""></script><script src="/_next/static/chunks/main-app-bf1ff2cf16dfe76b.js" async=""></script><script src="/_next/static/chunks/ee560e2c-2fc9e51a7200082a.js" async=""></script><script src="/_next/static/chunks/673-a54ed489fba98276.js" async=""></script><script src="/_next/static/chunks/874-91cb42999561f326.js" async=""></script><script src="/_next/static/chunks/app/layout-3e396362383a2ab4.js" async=""></script><script src="/_next/static/chunks/app/not-found-a01b316d5e858778.js" async=""></script><script src="/_next/static/chunks/fc2f6fa8-f66e1488fc192822.js" async=""></script><script src="/_next/static/chunks/f01af2bd-b3d99da6922beace.js" async=""></script><script src="/_next/static/chunks/851-5591b40f16808416.js" async=""></script><script src="/_next/static/chunks/608-98f6d3efc838467a.js" async=""></script><script src="/_next/static/chunks/app/register/page-022ff4c76f47b8b2.js" async=""></script><title>Multi-Vendor Platform</title><meta name="description" content="A multi-vendor platform with storefront, vendor dashboard, and admin panel"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_5cfdac __variable_9a8899 antialiased"><nav class="bg-white dark:bg-black border-b border-gray-200 dark:border-gray-800"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex justify-between h-16"><div class="flex"><div class="flex-shrink-0 flex items-center"><a class="flex items-center space-x-2" href="/"><div class="w-8 h-8 bg-black dark:bg-white rounded-full flex items-center justify-center"><span class="text-sm font-bold text-white dark:text-black">V</span></div><span class="text-xl font-bold text-black dark:text-white">Vsable</span></a></div><div class="hidden sm:ml-6 sm:flex sm:space-x-8"><a class="border-transparent text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white hover:border-black dark:hover:border-white inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors" href="/">Home</a></div></div><div class="hidden sm:ml-6 sm:flex sm:items-center"><div class="flex space-x-4"><a class="flex items-center text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white" href="/login"><svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round" class="mr-1" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path><polyline points="10 17 15 12 10 7"></polyline><line x1="15" y1="12" x2="3" y2="12"></line></svg>Login</a><a class="flex items-center text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white" href="/register"><svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round" class="mr-1" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="8.5" cy="7" r="4"></circle><line x1="20" y1="8" x2="20" y2="14"></line><line x1="23" y1="11" x2="17" y2="11"></line></svg>Register</a></div></div><div class="-mr-2 flex items-center sm:hidden"><button class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"><span class="sr-only">Open main menu</span><svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round" class="block h-6 w-6" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><line x1="3" y1="12" x2="21" y2="12"></line><line x1="3" y1="6" x2="21" y2="6"></line><line x1="3" y1="18" x2="21" y2="18"></line></svg></button></div></div></div></nav><main class="min-h-screen"><div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8"><div class="max-w-md w-full space-y-8"><div><h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">Create a new account</h2><p class="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">Or<!-- --> <a class="font-medium text-black hover:text-gray-700 dark:text-white dark:hover:text-gray-300" href="/login">sign in to your existing account</a></p></div><form class="mt-8 space-y-6"><div class="rounded-md shadow-sm -space-y-px"><div><label for="name" class="sr-only">Full Name</label><input id="name" type="text" autoComplete="name" required="" class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-white" placeholder="Full Name" name="name" value=""/></div><div><label for="email-address" class="sr-only">Email address</label><input id="email-address" type="email" autoComplete="email" required="" class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-white" placeholder="Email address" name="email" value=""/></div><div><label for="password" class="sr-only">Password</label><input id="password" type="password" autoComplete="new-password" required="" class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-white" placeholder="Password" name="password" value=""/></div><div><label for="confirm-password" class="sr-only">Confirm Password</label><input id="confirm-password" type="password" autoComplete="new-password" required="" class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-white" placeholder="Confirm Password" name="confirm-password" value=""/></div></div><div class="space-y-3"><label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Select your role</label><div class="grid grid-cols-1 gap-3"><button type="button" disabled="" class="
                relative flex items-center p-4 border rounded-lg text-left transition-all
                border-black dark:border-white bg-black/5 dark:bg-white/5
                opacity-50 cursor-not-allowed
              "><div class="
                flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-4
                bg-black dark:bg-white text-white dark:text-black
              "><svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg></div><div class="flex-1"><div class="flex items-center justify-between"><h3 class="
                    text-sm font-medium
                    text-black dark:text-white
                  ">Customer</h3><div class="w-4 h-4 rounded-full bg-black dark:bg-white flex items-center justify-center"><div class="w-2 h-2 rounded-full bg-white dark:bg-black"></div></div></div><p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Browse and purchase products</p></div></button><button type="button" disabled="" class="
                relative flex items-center p-4 border rounded-lg text-left transition-all
                border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500
                opacity-50 cursor-not-allowed
              "><div class="
                flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-4
                bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400
              "><svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path><line x1="3" y1="6" x2="21" y2="6"></line><path d="M16 10a4 4 0 0 1-8 0"></path></svg></div><div class="flex-1"><div class="flex items-center justify-between"><h3 class="
                    text-sm font-medium
                    text-gray-900 dark:text-gray-100
                  ">Vendor</h3></div><p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Sell products and manage store</p></div></button><button type="button" disabled="" class="
                relative flex items-center p-4 border rounded-lg text-left transition-all
                border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500
                opacity-50 cursor-not-allowed
              "><div class="
                flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-4
                bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400
              "><svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" stroke-linecap="round" stroke-linejoin="round" class="w-5 h-5" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path></svg></div><div class="flex-1"><div class="flex items-center justify-between"><h3 class="
                    text-sm font-medium
                    text-gray-900 dark:text-gray-100
                  ">Admin</h3></div><p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Manage platform and users</p></div></button></div></div><div><button type="submit" disabled="" class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed">Creating account...</button></div><div class="mt-6"><div class="relative"><div class="absolute inset-0 flex items-center"><div class="w-full border-t border-gray-300 dark:border-gray-600"></div></div><div class="relative flex justify-center text-sm"><span class="px-2 bg-gray-50 dark:bg-gray-900 text-gray-500 dark:text-gray-400">Or continue with</span></div></div><div class="mt-6"><button type="button" disabled="" class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors"><svg stroke="currentColor" fill="currentColor" stroke-width="0" version="1.1" x="0px" y="0px" viewBox="0 0 48 48" enable-background="new 0 0 48 48" class="h-5 w-5 mr-3" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg"><path fill="#FFC107" d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12
	c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24
	c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"></path><path fill="#FF3D00" d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657
	C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"></path><path fill="#4CAF50" d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36
	c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"></path><path fill="#1976D2" d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571
	c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"></path></svg>Sign up with Google</button></div><div class="mt-4 p-3 bg-gray-50 dark:bg-gray-900/20 border border-gray-200 dark:border-gray-800 rounded-md"><p class="text-xs text-gray-600 dark:text-gray-400"><strong>Firebase Setup Required:</strong> Configure your Firebase project for Google OAuth:</p><ul class="text-xs text-gray-600 dark:text-gray-400 mt-1 ml-4 list-disc"><li>Create Firebase project at console.firebase.google.com</li><li>Enable Google authentication in Firebase Auth</li><li>Update environment variables with Firebase config</li></ul></div></div></form></div></div><!--$--><!--/$--><!--$--><!--/$--></main><script src="/_next/static/chunks/webpack-8ba223b515e4ba9a.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[283,[\"844\",\"static/chunks/ee560e2c-2fc9e51a7200082a.js\",\"673\",\"static/chunks/673-a54ed489fba98276.js\",\"874\",\"static/chunks/874-91cb42999561f326.js\",\"177\",\"static/chunks/app/layout-3e396362383a2ab4.js\"],\"AuthProvider\"]\n3:I[5494,[\"844\",\"static/chunks/ee560e2c-2fc9e51a7200082a.js\",\"673\",\"static/chunks/673-a54ed489fba98276.js\",\"874\",\"static/chunks/874-91cb42999561f326.js\",\"177\",\"static/chunks/app/layout-3e396362383a2ab4.js\"],\"default\"]\n4:I[7555,[],\"\"]\n5:I[1295,[],\"\"]\n6:I[9543,[\"844\",\"static/chunks/ee560e2c-2fc9e51a7200082a.js\",\"874\",\"static/chunks/874-91cb42999561f326.js\",\"345\",\"static/chunks/app/not-found-a01b316d5e858778.js\"],\"default\"]\n7:I[894,[],\"ClientPageRoot\"]\n8:I[836,[\"844\",\"static/chunks/ee560e2c-2fc9e51a7200082a.js\",\"753\",\"static/chunks/fc2f6fa8-f66e1488fc192822.js\",\"965\",\"static/chunks/f01af2bd-b3d99da6922beace.js\",\"673\",\"static/chunks/673-a54ed489fba98276.js\",\"874\",\"static/chunks/874-91cb42999561f326.js\",\"851\",\"static/chunks/851-5591b40f16808416.js\",\"608\",\"static/chunks/608-98f6d3efc838467a.js\",\"454\",\"static/chunks/app/register/page-022ff4c76f47b8b2.js\"],\"default\"]\nb:I[9665,[],\"MetadataBoundary\"]\nd:I[9665,[],\"OutletBoundary\"]\n10:I[4911,[],\"AsyncMetadataOutlet\"]\n12:I[9665,[],\"ViewportBoundary\"]\n14:I[6614,[],\"\"]\n:HL[\"/_next/static/css/7e4b333761995f65.css\",\"style\"]\n0:{\"P\":null,\"b\":\"8Lfbezp4QsUeP_ZjIWdw-\",\"p\":\"\",\"c\":[\"\",\"register\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"register\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/7e4b333761995f65.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_5cfdac __variable_9a8899 antialiased\",\"children\":[\"$\",\"$L2\",null,{\"children\":[[\"$\",\"$L3\",null,{}],[\"$\",\"main\",null,{\"className\":\"min-h-screen\",\"children\":[\"$\",\"$L4\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template"])</script><script>self.__next_f.push([1,"\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$L6\",null,{}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]]}]}]}]]}],{\"children\":[\"register\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L4\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L5\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L7\",null,{\"Component\":\"$8\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@9\",\"$@a\"]}],[\"$\",\"$Lb\",null,{\"children\":\"$Lc\"}],null,[\"$\",\"$Ld\",null,{\"children\":[\"$Le\",\"$Lf\",[\"$\",\"$L10\",null,{\"promise\":\"$@11\"}]]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"TGACxjEaJBq_ZRPxpWHkE\",{\"children\":[[\"$\",\"$L12\",null,{\"children\":\"$L13\"}],null]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$14\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"15:\"$Sreact.suspense\"\n16:I[4911,[],\"AsyncMetadata\"]\n9:{}\na:{}\nc:[\"$\",\"$15\",null,{\"fallback\":null,\"children\":[\"$\",\"$L16\",null,{\"promise\":\"$@17\"}]}]\n"])</script><script>self.__next_f.push([1,"f:null\n"])</script><script>self.__next_f.push([1,"13:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\ne:null\n"])</script><script>self.__next_f.push([1,"17:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Multi-Vendor Platform\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"A multi-vendor platform with storefront, vendor dashboard, and admin panel\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]],\"error\":null,\"digest\":\"$undefined\"}\n11:{\"metadata\":\"$17:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>