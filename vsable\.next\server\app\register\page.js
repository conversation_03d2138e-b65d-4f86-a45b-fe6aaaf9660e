(()=>{var e={};e.id=454,e.ids=[454],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39377:(e,r,t)=>{Promise.resolve().then(t.bind(t,40082))},40049:(e,r,t)=>{Promise.resolve().then(t.bind(t,94530))},40082:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var a=t(60687),s=t(43210),o=t(85814),n=t.n(o),i=t(63213),l=t(82057),d=t(17019);let c=({selectedRole:e,onRoleChange:r,disabled:t=!1})=>{let s=[{id:"customer",name:"Customer",description:"Browse and purchase products",icon:d.JXP,color:"blue"},{id:"vendor",name:"Vendor",description:"Sell products and manage store",icon:d.y52,color:"green"},{id:"admin",name:"Admin",description:"Manage platform and users",icon:d.VSk,color:"purple"}];return(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Select your role"}),(0,a.jsx)("div",{className:"grid grid-cols-1 gap-3",children:s.map(s=>{let o=s.icon,n=e===s.id;return(0,a.jsxs)("button",{type:"button",onClick:()=>!t&&r(s.id),disabled:t,className:`
                relative flex items-center p-4 border rounded-lg text-left transition-all
                ${n?"border-black dark:border-white bg-black/5 dark:bg-white/5":"border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"}
                ${t?"opacity-50 cursor-not-allowed":"cursor-pointer"}
              `,children:[(0,a.jsx)("div",{className:`
                flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-4
                ${n?"bg-black dark:bg-white text-white dark:text-black":"bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400"}
              `,children:(0,a.jsx)(o,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:`
                    text-sm font-medium
                    ${n?"text-black dark:text-white":"text-gray-900 dark:text-gray-100"}
                  `,children:s.name}),n&&(0,a.jsx)("div",{className:"w-4 h-4 rounded-full bg-black dark:bg-white flex items-center justify-center",children:(0,a.jsx)("div",{className:"w-2 h-2 rounded-full bg-white dark:bg-black"})})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:s.description})]})]},s.id)})})]})};function u(){let[e,r]=(0,s.useState)(""),[t,o]=(0,s.useState)(""),[d,u]=(0,s.useState)(""),[p,m]=(0,s.useState)(""),[g,x]=(0,s.useState)("customer"),[h,b]=(0,s.useState)(""),{register:f,loading:y,error:v}=(0,i.A)(),w=async r=>{if(r.preventDefault(),b(""),!e||!t||!d||!p)return void b("Please fill in all fields");if(d!==p)return void b("Passwords do not match");try{await f(e,t,d,g)}catch{}};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white",children:"Create a new account"}),(0,a.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600 dark:text-gray-400",children:["Or"," ",(0,a.jsx)(n(),{href:"/login",className:"font-medium text-black hover:text-gray-700 dark:text-white dark:hover:text-gray-300",children:"sign in to your existing account"})]})]}),(0,a.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:w,children:[(v||h)&&(0,a.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,a.jsx)("div",{className:"flex",children:(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800",children:h||v})})})}),(0,a.jsxs)("div",{className:"rounded-md shadow-sm -space-y-px",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"sr-only",children:"Full Name"}),(0,a.jsx)("input",{id:"name",name:"name",type:"text",autoComplete:"name",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-white",placeholder:"Full Name",value:e,onChange:e=>r(e.target.value)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email-address",className:"sr-only",children:"Email address"}),(0,a.jsx)("input",{id:"email-address",name:"email",type:"email",autoComplete:"email",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-white",placeholder:"Email address",value:t,onChange:e=>o(e.target.value)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"sr-only",children:"Password"}),(0,a.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-white",placeholder:"Password",value:d,onChange:e=>u(e.target.value)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"confirm-password",className:"sr-only",children:"Confirm Password"}),(0,a.jsx)("input",{id:"confirm-password",name:"confirm-password",type:"password",autoComplete:"new-password",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-white",placeholder:"Confirm Password",value:p,onChange:e=>m(e.target.value)})]})]}),(0,a.jsx)(c,{selectedRole:g,onRoleChange:x,disabled:y}),(0,a.jsx)("div",{children:(0,a.jsx)("button",{type:"submit",disabled:y,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed",children:y?"Creating account...":"Create account"})}),(0,a.jsx)(l.A,{mode:"signup",role:g,disabled:y})]})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82057:(e,r,t)=>{"use strict";t.d(r,{A:()=>x});var a=t(60687),s=t(43210),o=t(73253),n=t(51060),i=t(63213),l=t(19978),d=t(67989);let c=["NEXT_PUBLIC_FIREBASE_API_KEY","NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN","NEXT_PUBLIC_FIREBASE_PROJECT_ID","NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET","NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID","NEXT_PUBLIC_FIREBASE_APP_ID"].filter(e=>!process.env[e]);c.length>0&&(console.warn(`Missing Firebase environment variables: ${c.join(", ")}`),console.warn("Firebase authentication will use mock mode."));let u=null,p=null,m=null;try{u=(0,d.Wp)({apiKey:"AIzaSyAgrJR95fG9_BqXSaPtYu8beGZz1wqkvGU",authDomain:"vsable-dev.firebaseapp.com",projectId:"vsable-dev",storageBucket:"vsable-dev.firebasestorage.app",messagingSenderId:"763818917063",appId:"1:763818917063:web:112971147059619b7e12e2"}),p=(0,l.xI)(u),(m=new l.HF).addScope("email"),m.addScope("profile"),console.log("Firebase initialized successfully")}catch(e){console.error("Firebase initialization error:",e),console.warn("Firebase authentication will use mock mode.")}let g=()=>{let[e,r]=(0,s.useState)(null),[t,a]=(0,s.useState)(!0),[o,n]=(0,s.useState)(null);(0,s.useEffect)(()=>{if(!p)return void a(!1);let e=(0,l.hg)(p,e=>{r(e),a(!1)});return()=>e()},[]);let i=async()=>{if(!e)return null;try{return await e.getIdToken()}catch(e){return console.error("Get ID token error:",e),n(e instanceof Error?e.message:"Failed to get ID token"),null}};return{user:e,loading:t,error:o,signInWithGoogle:async()=>{if(!p||!m)return n("Firebase is not properly configured"),null;try{return a(!0),n(null),(await (0,l.df)(p,m)).user}catch(e){return console.error("Google sign-in error:",e),n(e instanceof Error?e.message:"Failed to sign in with Google"),null}finally{a(!1)}},signOut:async()=>{if(!p)return void n("Firebase is not properly configured");try{await (0,l.CI)(p),r(null)}catch(e){console.error("Sign out error:",e),n(e instanceof Error?e.message:"Failed to sign out")}},getIdToken:i}},x=({mode:e,role:r="customer",onSuccess:t,onError:l,disabled:d=!1})=>{let[c,u]=(0,s.useState)(!1),{setUser:p}=(0,i.A)(),{signInWithGoogle:m,getIdToken:x,error:h}=g(),b=async()=>{u(!0);try{let e=await m();if(!e)throw Error(h||"Failed to sign in with Google");let a=await x();if(!a)throw Error("Failed to get authentication token");let s=await n.A.post("http://localhost:5000/api/auth/google",{idToken:a,firebaseUid:e.uid,email:e.email,name:e.displayName,photoURL:e.photoURL,role:r});p&&p(s.data.user),t&&t(a),"admin"===s.data.user.role?window.location.href="/admin/dashboard":"vendor"===s.data.user.role?window.location.href="/vendor/dashboard":window.location.href="/"}catch(r){console.error("Google OAuth error:",r);let e=r instanceof Error?r.message:"Google sign-in failed";l?l(r):alert(e)}finally{u(!1)}};return(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-gray-300 dark:border-gray-600"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsx)("span",{className:"px-2 bg-gray-50 dark:bg-gray-900 text-gray-500 dark:text-gray-400",children:"Or continue with"})})]}),(0,a.jsx)("div",{className:"mt-6",children:(0,a.jsxs)("button",{type:"button",onClick:b,disabled:d||c,className:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,a.jsx)(o.F4b,{className:"h-5 w-5 mr-3"}),c?"Connecting...":"signin"===e?"Sign in with Google":"Sign up with Google"]})}),(0,a.jsxs)("div",{className:"mt-4 p-3 bg-gray-50 dark:bg-gray-900/20 border border-gray-200 dark:border-gray-800 rounded-md",children:[(0,a.jsxs)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:[(0,a.jsx)("strong",{children:"Firebase Setup Required:"})," Configure your Firebase project for Google OAuth:"]}),(0,a.jsxs)("ul",{className:"text-xs text-gray-600 dark:text-gray-400 mt-1 ml-4 list-disc",children:[(0,a.jsx)("li",{children:"Create Firebase project at console.firebase.google.com"}),(0,a.jsx)("li",{children:"Enable Google authentication in Firebase Auth"}),(0,a.jsx)("li",{children:"Update environment variables with Firebase config"})]})]})]})}},83997:e=>{"use strict";e.exports=require("tty")},92174:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=t(65239),s=t(48088),o=t(88170),n=t.n(o),i=t(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d={children:["",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94530)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\register\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\register\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94530:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\StartUpJourney\\\\Development\\\\Test_dev\\\\vsable\\\\src\\\\app\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\register\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,579,26,846],()=>t(92174));module.exports=a})();