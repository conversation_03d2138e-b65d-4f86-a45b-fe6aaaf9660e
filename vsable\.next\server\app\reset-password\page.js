(()=>{var e={};e.id=700,e.ids=[700],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8687:(e,r,t)=>{Promise.resolve().then(t.bind(t,90322))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33823:(e,r,t)=>{"use strict";t.d(r,{Ay:()=>a});var s=t(60687);t(43210);let a=({size:e="md",color:r="black",text:t,fullScreen:a=!1})=>{let o=(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,s.jsx)("div",{className:`
          ${{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12",xl:"h-16 w-16"}[e]} 
          ${{black:"border-black border-t-transparent",white:"border-white border-t-transparent",gray:"border-gray-300 border-t-transparent dark:border-gray-600"}[r]} 
          border-2 rounded-full animate-spin
        `}),t&&(0,s.jsx)("p",{className:`mt-3 ${{sm:"text-sm",md:"text-base",lg:"text-lg",xl:"text-xl"}[e]} text-gray-600 dark:text-gray-400`,children:t})]});return a?(0,s.jsx)("div",{className:"fixed inset-0 bg-white dark:bg-gray-900 flex items-center justify-center z-50",children:o}):o}},33873:e=>{"use strict";e.exports=require("path")},53902:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=t(65239),a=t(48088),o=t(88170),n=t.n(o),l=t(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);t.d(r,i);let d={children:["",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,85316)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\reset-password\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\reset-password\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/reset-password/page",pathname:"/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55543:(e,r,t)=>{Promise.resolve().then(t.bind(t,85316))},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85316:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\StartUpJourney\\\\Development\\\\Test_dev\\\\vsable\\\\src\\\\app\\\\reset-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\reset-password\\page.tsx","default")},90322:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(60687),a=t(43210),o=t(85814),n=t.n(o),l=t(16189),i=t(51060),d=t(17019),c=t(33823);function p(){let[e,r]=(0,a.useState)(""),[t,o]=(0,a.useState)(""),[p,u]=(0,a.useState)(!1),[x,m]=(0,a.useState)(!1),[h,b]=(0,a.useState)(!1),[g,f]=(0,a.useState)(!1),[y,v]=(0,a.useState)(""),[w,j]=(0,a.useState)("");(0,l.useSearchParams)();let k=e=>e.length<8?"Password must be at least 8 characters long":"",N=async r=>{if(r.preventDefault(),v(""),!w)return void v("Invalid reset link. Please request a new password reset.");let s=k(e);if(s)return void v(s);if(e!==t)return void v("Passwords do not match");b(!0);try{await i.A.post("http://localhost:5000/api/auth/reset-password",{token:w,password:e}),f(!0)}catch(e){v(i.A.isAxiosError(e)&&e.response?.data?.message||"Failed to reset password")}finally{b(!1)}};return g?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-6",children:(0,s.jsx)(d.YrT,{className:"h-8 w-8 text-green-600 dark:text-green-400"})}),(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Password reset successful"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600 dark:text-gray-400",children:"Your password has been successfully reset. You can now log in with your new password."})]}),(0,s.jsx)("div",{children:(0,s.jsx)(n(),{href:"/login",className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-colors",children:"Go to login"})})]})}):(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsx)("div",{children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Reset your password"}),(0,s.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"Enter your new password below"})]})}),(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:N,children:[y&&(0,s.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4",children:(0,s.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:y})}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"sr-only",children:"New Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(d.F5$,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"password",name:"password",type:p?"text":"password",required:!0,value:e,onChange:e=>r(e.target.value),className:"appearance-none rounded-md relative block w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm",placeholder:"New password",disabled:h}),(0,s.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>u(!p),children:p?(0,s.jsx)(d._NO,{className:"h-5 w-5 text-gray-400"}):(0,s.jsx)(d.Vap,{className:"h-5 w-5 text-gray-400"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"sr-only",children:"Confirm Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(d.F5$,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:x?"text":"password",required:!0,value:t,onChange:e=>o(e.target.value),className:"appearance-none rounded-md relative block w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm",placeholder:"Confirm new password",disabled:h}),(0,s.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>m(!x),children:x?(0,s.jsx)(d._NO,{className:"h-5 w-5 text-gray-400"}):(0,s.jsx)(d.Vap,{className:"h-5 w-5 text-gray-400"})})]})]})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:h||!w,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:h?(0,s.jsx)(c.Ay,{size:"sm",color:"white"}):"Reset password"})}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)(n(),{href:"/login",className:"text-sm text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white transition-colors",children:"Back to login"})})]})]})})}function u(){return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)(c.Ay,{fullScreen:!0,text:"Loading..."}),children:(0,s.jsx)(p,{})})}},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,579,846],()=>t(53902));module.exports=s})();