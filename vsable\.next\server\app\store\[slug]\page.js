(()=>{var e={};e.id=904,e.ids=[904],e.modules={168:(e,t,r)=>{Promise.resolve().then(r.bind(r,53378))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36616:(e,t,r)=>{Promise.resolve().then(r.bind(r,45708))},45708:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\StartUpJourney\\\\Development\\\\Test_dev\\\\vsable\\\\src\\\\app\\\\store\\\\[slug]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\store\\[slug]\\page.tsx","default")},53378:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var s=r(60687),a=r(43210),n=r(85814),o=r.n(n);r(51060);var i=r(16189),l=r(65675),u=r(44056),p=r(49130),d=r(49480),c=r(56070),x=r(25834);function m(){let e=(0,i.useParams)();e&&"string"==typeof e.slug?e.slug:Array.isArray(e?.slug)&&e.slug[0];let[t,r]=(0,a.useState)(""),[n,m]=(0,a.useState)("All"),[h,v]=(0,a.useState)(null),[g,f]=(0,a.useState)([]),[b,y]=(0,a.useState)(["All"]),[_,j]=(0,a.useState)(!0),[w,q]=(0,a.useState)(null),A=(0,a.useMemo)(()=>g.filter(e=>{let r=e.name.toLowerCase().includes(t.toLowerCase())||e.description&&e.description.toLowerCase().includes(t.toLowerCase()),s="All"===n||e.category===n;return r&&s}),[g,t,n]);if(_)return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})});if(w||!h)return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,s.jsxs)("div",{className:"text-center p-4",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"Store Not Available"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:w||"The store you're looking for doesn't exist or has been removed."}),(0,s.jsx)(o(),{href:"/",className:"mt-4 inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Go to Homepage"})]})});let C={store:h,products:A,categories:b,searchQuery:t,selectedCategory:n,handleContactClick:e=>{e&&window.open(e,"_blank","noopener,noreferrer")}},P={...C,onSearchQueryChange:r,onSelectedCategoryChange:m},S={...C,onSearchQueryChange:r,onSelectedCategoryChange:m};switch(h.selected_template_id||"default"){case"default":return(0,s.jsx)(l.A,{...P});case"template1":return(0,s.jsx)(u.A,{...S});case"template2":return(0,s.jsx)(p.A,{...S});case"template3":return(0,s.jsx)(d.A,{...S});case"template4":return(0,s.jsx)(c.A,{...S});case"template5":return(0,s.jsx)(x.A,{...S});default:return console.warn(`Unknown template ID: ${h.selected_template_id}. Defaulting to TemplateDefault.`),(0,s.jsx)(l.A,{...P})}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89894:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,pages:()=>p,routeModule:()=>c,tree:()=>u});var s=r(65239),a=r(48088),n=r(88170),o=r.n(n),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let u={children:["",{children:["store",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,45708)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\store\\[slug]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\store\\[slug]\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/store/[slug]/page",pathname:"/store/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,579,474,281,846,982],()=>r(89894));module.exports=s})();