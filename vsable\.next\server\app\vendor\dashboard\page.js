(()=>{var e={};e.id=382,e.ids=[382],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6520:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>c});var t=r(60687),a=r(43210),d=r(85814),i=r.n(d);r(51060);var l=r(17019),n=r(63213),o=r(20769);function c(){let{user:e}=(0,n.A)(),[s,r]=(0,a.useState)([]),[d,c]=(0,a.useState)({total_views:0}),[m,x]=(0,a.useState)(""),[h,u]=(0,a.useState)(!0),[p,g]=(0,a.useState)(null);return(0,t.jsx)(o.A,{allowedRoles:["vendor","admin"],children:(0,t.jsx)("div",{className:"bg-gray-100 dark:bg-gray-900 min-h-screen",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,t.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-white",children:"Vendor Dashboard"}),(0,t.jsxs)("p",{className:"mt-1 text-sm text-gray-600 dark:text-gray-400",children:["Welcome back, ",e?.name]}),(0,t.jsx)("div",{className:"mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-2",children:[{icon:l.cfS,label:"Total Views",value:d.total_views},{icon:l.ND1,label:"Menu Items",value:s.length}].map(({icon:e,label:s,value:r})=>(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,t.jsx)("div",{className:"p-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(e,{className:"h-6 w-6 text-gray-400 flex-shrink-0"}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:s}),(0,t.jsx)("dd",{children:(0,t.jsx)("div",{className:"text-lg font-medium text-gray-900 dark:text-white",children:r})})]})})]})})},s))}),(0,t.jsxs)("div",{className:"mt-8",children:[(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Quick Actions"}),(0,t.jsxs)("div",{className:"mt-4 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3",children:[(0,t.jsx)(i(),{href:"/vendor/store",className:"block",children:(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow",children:(0,t.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0 bg-blue-500 rounded-md p-3",children:(0,t.jsx)(l.SG1,{className:"h-6 w-6 text-white"})}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"Manage Store Profile"}),(0,t.jsx)("dd",{className:"mt-1",children:(0,t.jsx)("div",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Edit store details"})})]})})]})})})}),(0,t.jsx)(i(),{href:"/vendor/products",className:"block",children:(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow",children:(0,t.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0 bg-green-500 rounded-md p-3",children:(0,t.jsx)(l.y52,{className:"h-6 w-6 text-white"})}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"Manage Products"}),(0,t.jsx)("dd",{className:"mt-1",children:(0,t.jsx)("div",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Add or edit products"})})]})})]})})})}),m&&(0,t.jsx)(()=>(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow cursor-pointer",onClick:()=>window.open(`/store/${m}`,"_blank"),children:(0,t.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0 bg-purple-500 rounded-md p-3",children:(0,t.jsx)(l.HaR,{className:"h-6 w-6 text-white"})}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"View Your Store"}),(0,t.jsx)("dd",{className:"mt-1",children:(0,t.jsx)("div",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"See customer view"})})]})})]})})}),{})]})]}),(0,t.jsxs)("div",{className:"mt-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Your Menu Items"}),(0,t.jsxs)(i(),{href:"/vendor/products",className:"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,t.jsx)(l.GGD,{className:"-ml-1 mr-2 h-4 w-4"}),"Add Product"]})]}),h?(0,t.jsx)("div",{className:"mt-4 flex justify-center",children:(0,t.jsx)("span",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):p?(0,t.jsx)("div",{className:"mt-4 bg-red-50 dark:bg-red-900/20 p-4 rounded-md",children:(0,t.jsx)("p",{className:"text-sm text-red-700 dark:text-red-400",children:p})}):(0,t.jsx)("div",{className:"mt-4 bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md",children:(0,t.jsx)("ul",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:s.length>0?s.map(e=>(0,t.jsx)("li",{className:"px-4 py-4 sm:px-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-16 h-16 rounded-md overflow-hidden bg-gray-200 dark:bg-gray-700",children:e.imageUrl?(0,t.jsx)("img",{src:e.imageUrl,alt:e.name,className:"w-full h-full object-cover"}):(0,t.jsx)("div",{className:"flex items-center justify-center w-full h-full",children:(0,t.jsx)(l.fZZ,{className:"h-6 w-6 text-gray-400"})})}),(0,t.jsxs)("div",{className:"ml-4 min-w-0 flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-blue-600 dark:text-blue-400 truncate",children:e.name}),(0,t.jsxs)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",children:["$",e.price]})]}),(0,t.jsxs)("div",{className:"mt-1 sm:flex sm:justify-between",children:[e.description&&(0,t.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.description}),e.category&&(0,t.jsxs)("p",{className:"mt-1 sm:mt-0 text-sm text-gray-500 dark:text-gray-400",children:["Category:\xa0",e.category]})]})]})]})},e.id)):(0,t.jsx)("li",{className:"px-4 py-4 sm:px-6",children:(0,t.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"No menu items yet. Click “Add Product” to create your first item."})})})})]})]})})})})}},8898:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>o});var t=r(65239),a=r(48088),d=r(88170),i=r.n(d),l=r(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(s,n);let o={children:["",{children:["vendor",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,57914)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\dashboard\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\dashboard\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/vendor/dashboard/page",pathname:"/vendor/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20769:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});var t=r(60687),a=r(43210),d=r(16189),i=r(63213);let l=({children:e,allowedRoles:s=[]})=>{let{user:r,loading:l,isAuthenticated:n}=(0,i.A)(),o=(0,d.useRouter)();return((0,a.useEffect)(()=>{l||n?!l&&n&&s.length>0&&r&&!s.includes(r.role)&&("admin"===r.role?o.replace("/admin/dashboard"):"vendor"===r.role?o.replace("/vendor/dashboard"):o.replace("/")):o.replace("/login")},[l,n,r,o,s]),l)?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):!n||s.length>0&&r&&!s.includes(r.role)?null:(0,t.jsx)(t.Fragment,{children:e})}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38924:(e,s,r)=>{Promise.resolve().then(r.bind(r,6520))},52076:(e,s,r)=>{Promise.resolve().then(r.bind(r,57914))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57914:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\StartUpJourney\\\\Development\\\\Test_dev\\\\vsable\\\\src\\\\app\\\\vendor\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\dashboard\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[447,579,846],()=>r(8898));module.exports=t})();