(()=>{var e={};e.id=14,e.ids=[14],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20769:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var s=r(60687),a=r(43210),l=r(16189),i=r(63213);let d=({children:e,allowedRoles:t=[]})=>{let{user:r,loading:d,isAuthenticated:n}=(0,i.A)(),o=(0,l.useRouter)();return((0,a.useEffect)(()=>{d||n?!d&&n&&t.length>0&&r&&!t.includes(r.role)&&("admin"===r.role?o.replace("/admin/dashboard"):"vendor"===r.role?o.replace("/vendor/dashboard"):o.replace("/")):o.replace("/login")},[d,n,r,o,t]),d)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):!n||t.length>0&&r&&!t.includes(r.role)?null:(0,s.jsx)(s.Fragment,{children:e})}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29045:(e,t,r)=>{"use strict";r.d(t,{Dw:()=>l});let s="http://localhost:5000/api".replace(/\/api$/,""),a=("https://**************:9000".replace(/^http:/,"https:"),`${s}/api/upload/serve`);function l(e){if(!e)return"";let t="";if(e.includes("**************:9000")){let r=e.split("/").pop();if(r)return i(e,t=`${a}/${r}`,"MinIO direct URL converted to proxy"),t}if((e.startsWith("http://")||e.startsWith("https://"))&&!e.includes("**************:9000"))return t=e,i(e,t,"External absolute URL"),t;if(e.startsWith("//"))return t=`https:${e}`,i(e,t,"Protocol-relative URL fixed"),t;if(e.startsWith("uploads/")||e.startsWith("images/")){let r=e.split("/").pop();return r&&r.match(/[0-9a-f]{32}_/)?i(e,t=`${a}/${r}`,"Path with UUID pattern"):(t=`/${e}`,i(e,t,"Added leading slash for Next.js")),t}let r=e.startsWith("/")?e:e.replace(/^\/+/,"");if(r.match(/[0-9a-f]{32}_/)){let s=r.split("/").pop();return i(e,t=`${a}/${s}`,"UUID-based filename"),t}if(r.includes("uploads/")){let s=r.split("/").pop();return i(e,t=`${a}/${s}`,"Uploads directory"),t}return i(e,t=r.startsWith("/")?r:`/${r}`,"Ensured leading slash for Next.js"),t}function i(e,t,r=""){}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41376:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var s=r(60687),a=r(30474),l=r(29045),i=r(43210);let d=({src:e,debug:t=!1,...r})=>{let[d,n]=(0,i.useState)(null),o=(0,l.Dw)(e),c=o.startsWith("http://")||o.startsWith("https://");return((0,i.useEffect)(()=>{t&&console.log(`[SafeImage] Original: "${e}" → Safe: "${o}"`)},[e,o,t]),d)?(0,s.jsx)("div",{className:"flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-md p-2",children:(0,s.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Image not available"})}):(0,s.jsx)(a.default,{src:o,...r,alt:r.alt||"Image",onError:()=>{if(n(`Failed to load image: ${o}`),console.error(`[SafeImage] Error loading image: "${o}" (original: "${e}")`),o.includes("/api/upload/serve/")){let e=o.split("/").pop(),t=`https://**************:9000/uploads/${e}`;console.log(`[SafeImage] Trying fallback URL: ${t}`)}},unoptimized:c})}},49448:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>o});var s=r(65239),a=r(48088),l=r(88170),i=r.n(l),d=r(30893),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);r.d(t,n);let o={children:["",{children:["vendor",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,72336)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\products\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\products\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/vendor/products/page",pathname:"/vendor/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68326:(e,t,r)=>{Promise.resolve().then(r.bind(r,89050))},72336:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\StartUpJourney\\\\Development\\\\Test_dev\\\\vsable\\\\src\\\\app\\\\vendor\\\\products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\products\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83849:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var s=r(60687),a=r(43210),l=r(51060),i=r(30474),d=r(17019),n=r(29045),o=r(41376);let c=({onFileUploaded:e,endpoint:t,acceptedFileTypes:r="image/*,video/*",maxSizeMB:c=5,currentFileUrl:u=null,label:m="Upload File",className:x=""})=>{let[g,p]=(0,a.useState)(null),[h,f]=(0,a.useState)(u),[b,y]=(0,a.useState)(!1),[v,j]=(0,a.useState)(null),[w,N]=(0,a.useState)(!1),k=(0,a.useRef)(null),_=(0,a.useId)();(0,a.useEffect)(()=>{f(u||null)},[u]);let P=async()=>{if(!g)return void j("Please select a file first");y(!0),j(null),N(!1);let r=new FormData;r.append("file",g);try{let{data:s}=await l.A.post(`http://localhost:5000/api${t}`,r,{headers:{"Content-Type":"multipart/form-data"},withCredentials:!0});N(!0);let a=s?.store?.cover_image_url??s?.store?.logo_url??s?.file_url??s?.file_path;if(a){console.log("Original URL from server:",a);let t=(0,n.Dw)(a);console.log("Formatted URL for Next.js:",t),e(t)}else j("Upload succeeded but no file URL returned")}catch(e){j(l.A.isAxiosError(e)&&e.response?.data?.message||"Error uploading file")}finally{y(!1)}},$=()=>h?g?.type.startsWith("image/")||h.startsWith("data:image/")?(0,s.jsx)(d.fZZ,{className:"w-12 h-12 text-gray-400"}):g?.type.startsWith("video/")||h.startsWith("data:video/")?(0,s.jsx)(d.pVQ,{className:"w-12 h-12 text-gray-400"}):(0,s.jsx)(d.QuH,{className:"w-12 h-12 text-gray-400"}):(0,s.jsx)(d.QuH,{className:"w-12 h-12 text-gray-400"});return(0,s.jsxs)("div",{className:`w-full ${x}`,children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:m}),(0,s.jsxs)("div",{className:"w-full h-48 bg-gray-100 dark:bg-gray-800 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 flex items-center justify-center overflow-hidden relative",children:[h?h.startsWith("data:image/")||h.match(/\.(jpe?g|png|gif|webp|avif|svg)$/i)?(0,s.jsx)("div",{className:"relative w-full h-full",children:h.startsWith("data:")?(0,s.jsx)(i.default,{src:h,alt:"preview",fill:!0,style:{objectFit:"contain"},unoptimized:!0}):(0,s.jsx)(o.A,{src:h,alt:"preview",fill:!0,style:{objectFit:"contain"},debug:!0})}):h.startsWith("data:video/")||h.match(/\.(mp4|mov|webm|ogg)$/i)?(0,s.jsx)("video",{src:h,controls:!0,className:"max-h-full max-w-full"}):$():(0,s.jsxs)("div",{className:"text-center p-4",children:[$(),(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-500 dark:text-gray-400",children:"Drag & drop a file here, or click to select"}),(0,s.jsxs)("p",{className:"text-xs text-gray-400 dark:text-gray-500 mt-1",children:[r.replace("*","").replace(/,/g,", ")," files up to ",c," MB"]})]}),h&&(0,s.jsx)("button",{type:"button",onClick:()=>{p(null),f(u||null),j(null),N(!1),k.current&&(k.current.value="")},className:"absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:(0,s.jsx)(d.yGN,{className:"w-4 h-4"})})]}),(0,s.jsxs)("div",{className:"mt-4 flex items-center space-x-3 w-full",children:[(0,s.jsx)("input",{id:_,ref:k,type:"file",accept:r,className:"hidden",onChange:e=>{j(null),N(!1);let t=e.target.files?.[0];if(!t)return;let r=1024*c*1024;if(t.size>r)return void j(`File size exceeds ${c} MB`);p(t);let s=new FileReader;s.onloadend=()=>f(s.result),s.readAsDataURL(t)}}),(0,s.jsx)("label",{htmlFor:_,className:"flex-1 text-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer",children:"Select file"}),(0,s.jsx)("button",{type:"button",onClick:P,disabled:!g||b,className:`px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center justify-center ${!g||b?"opacity-50 cursor-not-allowed":""}`,children:b?(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Uploading…"]}):w?(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(d.YrT,{className:"-ml-1 mr-2 h-4 w-4"}),"Uploaded"]}):(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(d.B88,{className:"-ml-1 mr-2 h-4 w-4"}),"Upload"]})})]}),v&&(0,s.jsx)("div",{className:"mt-2 text-sm text-red-600 dark:text-red-400",children:v})]})}},83997:e=>{"use strict";e.exports=require("tty")},89050:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var s=r(60687),a=r(43210),l=r(30474),i=r(51060),d=r(63213),n=r(20769),o=r(83849),c=r(17019);let u="http://localhost:5000/api";function m(){let{user:e}=(0,d.A)(),[t,r]=(0,a.useState)(!0),[m,x]=(0,a.useState)([]),[g,p]=(0,a.useState)(null),[h,f]=(0,a.useState)(null),[b,y]=(0,a.useState)(!1),[v,j]=(0,a.useState)("create"),[w,N]=(0,a.useState)(null),[k,_]=(0,a.useState)(""),[P,$]=(0,a.useState)(""),[S,C]=(0,a.useState)(""),[A,F]=(0,a.useState)(""),[U,E]=(0,a.useState)(""),[q,D]=(0,a.useState)(""),[W,I]=(0,a.useState)(!0),[R,T]=(0,a.useState)(!1),[z,G]=(0,a.useState)(null),M=()=>{_(""),$(""),C(""),F(""),E(""),D(""),I(!0)},L=()=>{M(),j("create"),y(!0)},B=e=>{N(e),_(e.name),$(e.description||""),C(e.price.toString()),F(e.image_url||""),E(e.category||""),D(e.tags.join(", ")),I(e.is_active),j("edit"),y(!0)},J=()=>{y(!1),N(null)},O=async e=>{if(e.preventDefault(),p(null),f(null),!k||!S)return void p("Name and price are required");let t=q.split(",").map(e=>e.trim()).filter(e=>e);try{if("create"===v){let e=await i.A.post(`${u}/product/create`,{name:k,description:P||null,price:parseFloat(S),image_url:A||null,category:U||null,tags:t,is_active:W},{withCredentials:!0});x([...m,e.data.product]),f("Product created successfully!")}else if("edit"===v&&w){let e=await i.A.put(`${u}/product/${w.id}`,{name:k,description:P||null,price:parseFloat(S),image_url:A||null,category:U||null,tags:t,is_active:W},{withCredentials:!0});x(m.map(t=>t.id===w.id?e.data.product:t)),f("Product updated successfully!")}J()}catch(e){p(e instanceof Error?e.message:"Failed to save product")}},X=e=>{G(e),T(!0)},Z=async()=>{if(z){p(null),f(null);try{await i.A.delete(`${u}/product/${z}`,{withCredentials:!0}),x(m.filter(e=>e.id!==z)),f("Product deleted successfully!"),T(!1),G(null)}catch(e){p(e instanceof Error?e.message:"Failed to delete product")}}},H=async e=>{p(null),f(null);try{let t=await i.A.put(`${u}/product/${e}/toggle`,{},{withCredentials:!0});x(m.map(r=>r.id===e?t.data.product:r)),f(`Product ${t.data.product.is_active?"activated":"deactivated"} successfully!`)}catch(e){p(e instanceof Error?e.message:"Failed to update product status")}};return(0,s.jsxs)(n.A,{allowedRoles:["vendor","admin"],children:[(0,s.jsx)("div",{className:"bg-gray-100 dark:bg-gray-900 min-h-screen",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-white",children:"Products"}),(0,s.jsxs)("button",{onClick:L,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,s.jsx)(c.GGD,{className:"-ml-1 mr-2 h-5 w-5"}),"Add Product"]})]}),g&&(0,s.jsx)("div",{className:"mt-4 bg-red-50 dark:bg-red-900/20 p-4 rounded-md",children:(0,s.jsx)("p",{className:"text-sm text-red-700 dark:text-red-400",children:g})}),h&&(0,s.jsx)("div",{className:"mt-4 bg-green-50 dark:bg-green-900/20 p-4 rounded-md",children:(0,s.jsx)("p",{className:"text-sm text-green-700 dark:text-green-400",children:h})}),t?(0,s.jsx)("div",{className:"mt-6 flex justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):0===m.length?(0,s.jsxs)("div",{className:"mt-6 text-center py-12 bg-white dark:bg-gray-800 rounded-lg shadow",children:[(0,s.jsx)(c.cnX,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,s.jsx)("h3",{className:"mt-2 text-lg font-medium text-gray-900 dark:text-white",children:"No products yet"}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Get started by adding your first product."}),(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsxs)("button",{onClick:L,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,s.jsx)(c.GGD,{className:"-ml-1 mr-2 h-5 w-5"}),"Add Product"]})})]}):(0,s.jsx)("div",{className:"mt-6 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3",children:m.map(e=>(0,s.jsxs)("div",{className:`bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg ${!e.is_active?"opacity-60":""}`,children:[(0,s.jsxs)("div",{className:"h-48 w-full relative",children:[e.image_url?(0,s.jsx)(l.default,{src:e.image_url,alt:e.name,fill:!0,style:{objectFit:"cover"}}):(0,s.jsx)("div",{className:"h-full w-full flex items-center justify-center bg-gray-200 dark:bg-gray-700",children:(0,s.jsx)(c.fZZ,{className:"h-12 w-12 text-gray-400"})}),!e.is_active&&(0,s.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center",children:(0,s.jsx)("span",{className:"px-2 py-1 bg-red-500 text-white text-xs font-bold uppercase rounded",children:"Inactive"})})]}),(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e.name}),(0,s.jsxs)("span",{className:"text-blue-600 dark:text-blue-400 font-bold",children:["$",e.price.toFixed(2)]})]}),(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400 line-clamp-2",children:e.description||"No description"}),e.category&&(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",children:e.category})}),(0,s.jsxs)("div",{className:"mt-4 flex justify-between",children:[(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("button",{onClick:()=>B(e),className:"inline-flex items-center p-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:bg-gray-600",children:(0,s.jsx)(c.WXf,{className:"h-4 w-4"})}),(0,s.jsx)("button",{onClick:()=>X(e.id),className:"inline-flex items-center p-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:bg-gray-600",children:(0,s.jsx)(c.IXo,{className:"h-4 w-4 text-red-500"})})]}),(0,s.jsx)("button",{onClick:()=>H(e.id),className:"inline-flex items-center p-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:bg-gray-600",title:e.is_active?"Deactivate":"Activate",children:e.is_active?(0,s.jsx)(c._NO,{className:"h-4 w-4"}):(0,s.jsx)(c.Vap,{className:"h-4 w-4"})})]})]})]},e.id))})]})})}),b&&(0,s.jsx)("div",{className:"fixed inset-0 overflow-y-auto z-50",children:(0,s.jsxs)("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,s.jsx)("span",{className:"hidden sm:inline-block sm:align-middle sm:h-screen","aria-hidden":"true",children:"​"}),(0,s.jsx)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:(0,s.jsxs)("form",{onSubmit:O,children:[(0,s.jsxs)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:[(0,s.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-white",children:"create"===v?"Add New Product":"Edit Product"}),(0,s.jsxs)("div",{className:"mt-4 space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Product Name *"}),(0,s.jsx)("div",{className:"mt-1 relative rounded-md shadow-sm",children:(0,s.jsx)("input",{type:"text",name:"name",id:"name",required:!0,className:"focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"Product Name",value:k,onChange:e=>_(e.target.value)})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"price",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Price *"}),(0,s.jsxs)("div",{className:"mt-1 relative rounded-md shadow-sm",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)("span",{className:"text-gray-500 dark:text-gray-400 sm:text-sm",children:"$"})}),(0,s.jsx)("input",{type:"number",name:"price",id:"price",required:!0,min:"0",step:"0.01",className:"focus:ring-blue-500 focus:border-blue-500 block w-full pl-7 sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"0.00",value:S,onChange:e=>C(e.target.value)})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Description"}),(0,s.jsx)("div",{className:"mt-1",children:(0,s.jsx)("textarea",{id:"description",name:"description",rows:3,className:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"Product description",value:P,onChange:e=>$(e.target.value)})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Product Image"}),(0,s.jsx)("div",{className:"mt-1",children:w&&"edit"===v?(0,s.jsx)(o.A,{onFileUploaded:e=>F(e),endpoint:`/product/${w.id}/upload-image`,acceptedFileTypes:"image/*,video/*",maxSizeMB:5,currentFileUrl:A,label:"Product Image"}):(0,s.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,s.jsx)(o.A,{onFileUploaded:e=>F(e),endpoint:"/upload/image",acceptedFileTypes:"image/*,video/*",maxSizeMB:5,currentFileUrl:A,label:"Product Image"}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"You can upload the image now, or add it after creating the product."})]})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Category"}),(0,s.jsx)("div",{className:"mt-1",children:(0,s.jsx)("input",{type:"text",name:"category",id:"category",className:"focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"e.g. Main Course, Dessert, etc.",value:U,onChange:e=>E(e.target.value)})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"tags",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Tags (comma separated)"}),(0,s.jsx)("div",{className:"mt-1",children:(0,s.jsx)("input",{type:"text",name:"tags",id:"tags",className:"focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"e.g. vegetarian, spicy, gluten-free",value:q,onChange:e=>D(e.target.value)})})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{id:"is-active",name:"is-active",type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600",checked:W,onChange:e=>I(e.target.checked)}),(0,s.jsx)("label",{htmlFor:"is-active",className:"ml-2 block text-sm text-gray-900 dark:text-gray-300",children:"Product is active and visible to customers"})]})]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,s.jsx)("button",{type:"submit",className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm",children:"create"===v?"Add Product":"Update Product"}),(0,s.jsx)("button",{type:"button",className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700",onClick:J,children:"Cancel"})]})]})})]})}),R&&(0,s.jsx)("div",{className:"fixed inset-0 overflow-y-auto z-50",children:(0,s.jsxs)("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,s.jsx)("span",{className:"hidden sm:inline-block sm:align-middle sm:h-screen","aria-hidden":"true",children:"​"}),(0,s.jsxs)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[(0,s.jsx)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,s.jsxs)("div",{className:"sm:flex sm:items-start",children:[(0,s.jsx)("div",{className:"mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10",children:(0,s.jsx)(c.IXo,{className:"h-6 w-6 text-red-600"})}),(0,s.jsxs)("div",{className:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left",children:[(0,s.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-white",children:"Delete Product"}),(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Are you sure you want to delete this product? This action cannot be undone."})})]})]})}),(0,s.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,s.jsx)("button",{type:"button",className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm",onClick:Z,children:"Delete"}),(0,s.jsx)("button",{type:"button",className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700",onClick:()=>{G(null),T(!1)},children:"Cancel"})]})]})]})})]})}},94735:e=>{"use strict";e.exports=require("events")},99870:(e,t,r)=>{Promise.resolve().then(r.bind(r,72336))}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,579,474,846],()=>r(49448));module.exports=s})();