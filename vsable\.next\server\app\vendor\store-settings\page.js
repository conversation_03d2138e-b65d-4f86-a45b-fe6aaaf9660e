(()=>{var e={};e.id=203,e.ids=[203],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11629:(e,t,r)=>{Promise.resolve().then(r.bind(r,23421))},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23421:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\StartUpJourney\\\\Development\\\\Test_dev\\\\vsable\\\\src\\\\app\\\\vendor\\\\store-settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\store-settings\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28971:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(60687),o=r(43210);r(51060);var n=r(99623),a=r(20769),i=r(85814),d=r.n(i);function l(){let[e,t]=(0,o.useState)(null),[r,i]=(0,o.useState)(!0),[l,p]=(0,o.useState)(null);return r?(0,s.jsx)(a.A,{allowedRoles:["vendor"],children:(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})})}):(0,s.jsx)(a.A,{allowedRoles:["vendor"],children:(0,s.jsx)("div",{className:"min-h-screen bg-gray-100 dark:bg-gray-900 py-8",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)(d(),{href:"/vendor/dashboard",className:"text-blue-600 dark:text-blue-400 hover:underline",children:"← Back to Dashboard"})}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:"Store Appearance Settings"}),(0,s.jsx)("p",{className:"text-md text-gray-600 dark:text-gray-400 mb-8",children:"Customize the look and feel of your public storefront."}),l&&!e&&(0,s.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md mb-6",children:[(0,s.jsx)("p",{className:"text-sm text-red-700 dark:text-red-400",children:l}),l.includes("create a store first")&&(0,s.jsx)(d(),{href:"/vendor/store/create",className:"mt-2 inline-block text-sm text-blue-600 dark:text-blue-400 hover:underline",children:"Create your store now →"})]}),e?(0,s.jsx)(n.A,{currentStore:e,onTemplateUpdate:e=>{t(e)}}):!r&&!l&&(0,s.jsx)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-md",children:(0,s.jsx)("p",{className:"text-sm text-yellow-700 dark:text-yellow-400",children:"Store data is not available. If you've just created your store, please try refreshing."})})]})})})}},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63520:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>p,routeModule:()=>c,tree:()=>l});var s=r(65239),o=r(48088),n=r(88170),a=r.n(n),i=r(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(t,d);let l={children:["",{children:["vendor",{children:["store-settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,23421)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\store-settings\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\store-settings\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/vendor/store-settings/page",pathname:"/vendor/store-settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},93069:(e,t,r)=>{Promise.resolve().then(r.bind(r,28971))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,579,474,281,846,982,95],()=>r(63520));module.exports=s})();