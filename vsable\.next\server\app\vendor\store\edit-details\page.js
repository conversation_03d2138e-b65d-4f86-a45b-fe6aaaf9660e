(()=>{var e={};e.id=179,e.ids=[179],e.modules={1627:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(60687),o=r(43210);r(51060);var a=r(20769),i=r(99623);function n(){let[e,t]=(0,o.useState)(null),[r,n]=(0,o.useState)(!0),[d,l]=(0,o.useState)(null);return r?(0,s.jsx)("div",{className:"p-4",children:"Loading store details..."}):d?(0,s.jsxs)("div",{className:"p-4 text-red-500",children:["Error: ",d]}):e?(0,s.jsx)(a.A,{allowedRoles:["vendor"],children:(0,s.jsxs)("div",{className:"container mx-auto p-4",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Edit Store Details & Appearance"}),(0,s.jsxs)("div",{className:"mb-8 p-6 bg-white dark:bg-gray-800 shadow rounded-lg",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-3",children:"Store Information"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Form for store name, description, logo will go here."})]}),(0,s.jsx)(i.A,{currentStore:e,onTemplateUpdate:e=>{t(e)}})]})}):(0,s.jsx)("div",{className:"p-4",children:"No store data available. You might need to create a store first."})}},1976:(e,t,r)=>{Promise.resolve().then(r.bind(r,1627))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31300:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>p,routeModule:()=>c,tree:()=>l});var s=r(65239),o=r(48088),a=r(88170),i=r.n(a),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(t,d);let l={children:["",{children:["vendor",{children:["store",{children:["edit-details",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,79997)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\store\\edit-details\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\store\\edit-details\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/vendor/store/edit-details/page",pathname:"/vendor/store/edit-details",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},79997:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\StartUpJourney\\\\Development\\\\Test_dev\\\\vsable\\\\src\\\\app\\\\vendor\\\\store\\\\edit-details\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\store\\edit-details\\page.tsx","default")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},92248:(e,t,r)=>{Promise.resolve().then(r.bind(r,79997))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,579,474,281,846,982,95],()=>r(31300));module.exports=s})();