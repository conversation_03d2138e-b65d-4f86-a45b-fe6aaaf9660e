(()=>{var e={};e.id=555,e.ids=[555],e.modules={2439:(e,r,t)=>{Promise.resolve().then(t.bind(t,74501))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13923:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\StartUpJourney\\\\Development\\\\Test_dev\\\\vsable\\\\src\\\\app\\\\vendor\\\\store\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\store\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41376:(e,r,t)=>{"use strict";t.d(r,{A:()=>d});var a=t(60687),s=t(30474),l=t(29045),o=t(43210);let d=({src:e,debug:r=!1,...t})=>{let[d,n]=(0,o.useState)(null),i=(0,l.Dw)(e),c=i.startsWith("http://")||i.startsWith("https://");return((0,o.useEffect)(()=>{r&&console.log(`[SafeImage] Original: "${e}" → Safe: "${i}"`)},[e,i,r]),d)?(0,a.jsx)("div",{className:"flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-md p-2",children:(0,a.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Image not available"})}):(0,a.jsx)(s.default,{src:i,...t,alt:t.alt||"Image",onError:()=>{if(n(`Failed to load image: ${i}`),console.error(`[SafeImage] Error loading image: "${i}" (original: "${e}")`),i.includes("/api/upload/serve/")){let e=i.split("/").pop(),r=`https://35.240.129.146:9000/uploads/${e}`;console.log(`[SafeImage] Trying fallback URL: ${r}`)}},unoptimized:c})}},42607:(e,r,t)=>{Promise.resolve().then(t.bind(t,13923))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60862:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>i});var a=t(65239),s=t(48088),l=t(88170),o=t.n(l),d=t(30893),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);t.d(r,n);let i={children:["",{children:["vendor",{children:["store",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,13923)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\store\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\vendor\\store\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/vendor/store/page",pathname:"/vendor/store",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},74501:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var a=t(60687),s=t(43210),l=t(16189),o=t(51060),d=t(63213),n=t(20769),i=t(99623),c=t(83849),m=t(17019),u=t(69587);let x="http://localhost:5000/api";function g(){let{user:e}=(0,d.A)(),r=(0,l.useRouter)(),[t,g]=(0,s.useState)(!0),[p,h]=(0,s.useState)(!1),[b,y]=(0,s.useState)(null),[f,v]=(0,s.useState)(null),[k,j]=(0,s.useState)(null),[N,w]=(0,s.useState)(null),[S,_]=(0,s.useState)(""),[F,U]=(0,s.useState)(""),[C,A]=(0,s.useState)(""),[P,q]=(0,s.useState)(""),[E,T]=(0,s.useState)(""),[$,z]=(0,s.useState)(""),[D,R]=(0,s.useState)(""),[I,W]=(0,s.useState)(""),[L,M]=(0,s.useState)(""),[B,O]=(0,s.useState)(""),G=async e=>{e.preventDefault(),h(!0),y(null),v(null);try{let e=await o.A.post(`${x}/store/create`,{name:S,description:F||null,logo_url:C||null,cover_image_url:P||null,telegram:E||null,whatsapp:$||null,phone:D||null,instagram:I||null,facebook:L||null,tiktok:B||null},{withCredentials:!0});j(e.data.store),w(!0),v("Store created successfully!"),setTimeout(()=>{r.replace("/vendor/store")},1500)}catch(e){y(o.A.isAxiosError(e)&&e.response?.data?.message||"Failed to create store")}finally{h(!1)}},Y=async e=>{e.preventDefault(),h(!0),y(null),v(null);try{let e=await o.A.put(`${x}/store/update`,{name:S,description:F||null,logo_url:C||null,cover_image_url:P||null,telegram:E||null,whatsapp:$||null,phone:D||null,instagram:I||null,facebook:L||null,tiktok:B||null},{withCredentials:!0});j(e.data.store),v("Store updated successfully!")}catch(e){y(o.A.isAxiosError(e)&&e.response?.data?.message||"Failed to update store")}finally{h(!1)}};return(0,a.jsx)(n.A,{allowedRoles:["vendor","admin"],children:(0,a.jsx)("div",{className:"bg-gray-100 dark:bg-gray-900 min-h-screen",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,a.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-white",children:N?"Store Profile":"Create Your Store"}),t?(0,a.jsx)("div",{className:"mt-6 flex justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):(0,a.jsxs)("div",{className:"mt-6",children:[b&&(0,a.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 p-4 rounded-md",children:(0,a.jsx)("p",{className:"text-sm text-red-700 dark:text-red-400",children:b})}),f&&(0,a.jsx)("div",{className:"mb-4 bg-green-50 dark:bg-green-900/20 p-4 rounded-md",children:(0,a.jsx)("p",{className:"text-sm text-green-700 dark:text-green-400",children:f})}),(0,a.jsxs)("form",{onSubmit:N?Y:G,className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow px-4 py-5 sm:rounded-lg sm:p-6",children:(0,a.jsxs)("div",{className:"md:grid md:grid-cols-3 md:gap-6",children:[(0,a.jsxs)("div",{className:"md:col-span-1",children:[(0,a.jsx)("h3",{className:"text-lg font-medium leading-6 text-gray-900 dark:text-white",children:"Store Information"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"This information will be displayed publicly on your store page."})]}),(0,a.jsx)("div",{className:"mt-5 md:mt-0 md:col-span-2",children:(0,a.jsxs)("div",{className:"grid grid-cols-6 gap-6",children:[(0,a.jsxs)("div",{className:"col-span-6 sm:col-span-4",children:[(0,a.jsx)("label",{htmlFor:"store-name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Store Name *"}),(0,a.jsxs)("div",{className:"mt-1 flex rounded-md shadow-sm",children:[(0,a.jsx)("span",{className:"inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm",children:(0,a.jsx)(m.qYx,{className:"h-4 w-4"})}),(0,a.jsx)("input",{type:"text",name:"store-name",id:"store-name",required:!0,className:"focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-white",placeholder:"Your Store Name",value:S,onChange:e=>_(e.target.value)})]})]}),(0,a.jsxs)("div",{className:"col-span-6",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Store Logo"}),(0,a.jsx)("div",{className:"mt-1",children:k?(0,a.jsx)(c.A,{onFileUploaded:e=>{if(console.log("Logo uploaded:",e),A(e),k){let r={...k,logo_url:e};console.log("Updating store with new logo:",r),j(r)}},endpoint:"/store/upload-logo",acceptedFileTypes:"image/*,video/*",maxSizeMB:5,currentFileUrl:C,label:"Store Logo"}):(0,a.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,a.jsx)(c.A,{onFileUploaded:e=>A(e),endpoint:"/upload/image",acceptedFileTypes:"image/*,video/*",maxSizeMB:5,currentFileUrl:C,label:"Store Logo"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"You can upload the logo now, or add it after creating the store."})]})}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500 dark:text-gray-400",children:"Upload a logo for your store. Recommended size: 200x200 pixels."})]}),(0,a.jsxs)("div",{className:"col-span-6",children:[(0,a.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Description"}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)("textarea",{id:"description",name:"description",rows:3,className:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-800 dark:border-gray-600 dark:text-white",placeholder:"Describe your store and what you offer",value:F,onChange:e=>U(e.target.value)})})]}),(0,a.jsxs)("div",{className:"col-span-6",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Cover Image"}),(0,a.jsx)("div",{className:"mt-1",children:k?(0,a.jsx)(c.A,{onFileUploaded:e=>{if(console.log("Cover image uploaded:",e),q(e),k){let r={...k,cover_image_url:e};console.log("Updating store with new cover image:",r),j(r)}},endpoint:"/store/upload-cover",acceptedFileTypes:"image/*,video/*",maxSizeMB:10,currentFileUrl:P,label:"Cover Image"}):(0,a.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,a.jsx)(c.A,{onFileUploaded:e=>q(e),endpoint:"/upload/image",acceptedFileTypes:"image/*,video/*",maxSizeMB:10,currentFileUrl:P,label:"Cover Image"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"You can upload the cover image now, or add it after creating the store."})]})}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500 dark:text-gray-400",children:"Upload a cover image for your store's landing page. Recommended size: 1920x1080 pixels."})]})]})})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow px-4 py-5 sm:rounded-lg sm:p-6",children:(0,a.jsxs)("div",{className:"md:grid md:grid-cols-3 md:gap-6",children:[(0,a.jsxs)("div",{className:"md:col-span-1",children:[(0,a.jsx)("h3",{className:"text-lg font-medium leading-6 text-gray-900 dark:text-white",children:"Contact Information"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"How customers can reach you to place orders."})]}),(0,a.jsx)("div",{className:"mt-5 md:mt-0 md:col-span-2",children:(0,a.jsxs)("div",{className:"grid grid-cols-6 gap-6",children:[(0,a.jsxs)("div",{className:"col-span-6 sm:col-span-4",children:[(0,a.jsx)("label",{htmlFor:"telegram",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Telegram Username or Link"}),(0,a.jsxs)("div",{className:"mt-1 flex rounded-md shadow-sm",children:[(0,a.jsx)("span",{className:"inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm",children:(0,a.jsx)(u.hFS,{className:"h-4 w-4 text-blue-500"})}),(0,a.jsx)("input",{type:"text",name:"telegram",id:"telegram",className:"focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-white",placeholder:"@username or https://t.me/username",value:E,onChange:e=>T(e.target.value)})]})]}),(0,a.jsxs)("div",{className:"col-span-6 sm:col-span-4",children:[(0,a.jsx)("label",{htmlFor:"whatsapp",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"WhatsApp Number (Optional)"}),(0,a.jsxs)("div",{className:"mt-1 flex rounded-md shadow-sm",children:[(0,a.jsx)("span",{className:"inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm",children:(0,a.jsx)(u.EcP,{className:"h-4 w-4 text-green-500"})}),(0,a.jsx)("input",{type:"text",name:"whatsapp",id:"whatsapp",className:"focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-white",placeholder:"+1234567890",value:$,onChange:e=>z(e.target.value)})]})]}),(0,a.jsxs)("div",{className:"col-span-6 sm:col-span-4",children:[(0,a.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Phone Number (Optional)"}),(0,a.jsxs)("div",{className:"mt-1 flex rounded-md shadow-sm",children:[(0,a.jsx)("span",{className:"inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm",children:(0,a.jsx)(m.QFc,{className:"h-4 w-4"})}),(0,a.jsx)("input",{type:"tel",name:"phone",id:"phone",className:"focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-white",placeholder:"+1234567890",value:D,onChange:e=>R(e.target.value)})]})]}),(0,a.jsxs)("div",{className:"col-span-6 sm:col-span-4",children:[(0,a.jsx)("label",{htmlFor:"instagram",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Instagram Username (Optional)"}),(0,a.jsxs)("div",{className:"mt-1 flex rounded-md shadow-sm",children:[(0,a.jsx)("span",{className:"inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm",children:(0,a.jsx)(u.ao$,{className:"h-4 w-4 text-pink-500"})}),(0,a.jsx)("input",{type:"text",name:"instagram",id:"instagram",className:"focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-white",placeholder:"username",value:I,onChange:e=>W(e.target.value)})]})]}),(0,a.jsxs)("div",{className:"col-span-6 sm:col-span-4",children:[(0,a.jsx)("label",{htmlFor:"facebook",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Facebook Page or Profile (Optional)"}),(0,a.jsxs)("div",{className:"mt-1 flex rounded-md shadow-sm",children:[(0,a.jsx)("span",{className:"inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm",children:(0,a.jsx)(u.iYk,{className:"h-4 w-4 text-blue-600"})}),(0,a.jsx)("input",{type:"text",name:"facebook",id:"facebook",className:"focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-white",placeholder:"https://facebook.com/yourpage or username",value:L,onChange:e=>M(e.target.value)})]})]}),(0,a.jsxs)("div",{className:"col-span-6 sm:col-span-4",children:[(0,a.jsx)("label",{htmlFor:"tiktok",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"TikTok Username (Optional)"}),(0,a.jsxs)("div",{className:"mt-1 flex rounded-md shadow-sm",children:[(0,a.jsx)("span",{className:"inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm",children:(0,a.jsx)(u.kkU,{className:"h-4 w-4 text-black dark:text-white"})}),(0,a.jsx)("input",{type:"text",name:"tiktok",id:"tiktok",className:"focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-white",placeholder:"@username",value:B,onChange:e=>O(e.target.value)})]})]})]})})]})}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("button",{type:"submit",disabled:p,className:"inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:[(0,a.jsx)(m.Bc_,{className:"mr-2 -ml-1 h-5 w-5"}),p?"Saving...":N?"Update Store":"Create Store"]}),N&&k&&(0,a.jsxs)("button",{type:"button",onClick:()=>{k&&k.slug&&window.open(`/store/${k.slug}`,"_blank")},className:"inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:bg-gray-600",children:[(0,a.jsx)(m.ayE,{className:"mr-2 -ml-1 h-5 w-5"}),"Preview Store"]})]})]}),N&&k&&(0,a.jsx)("div",{className:"mt-8",children:(0,a.jsx)(i.A,{currentStore:k,onTemplateUpdate:e=>{j(e),v("Store template updated successfully!")}})})]})]})})})})}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83849:(e,r,t)=>{"use strict";t.d(r,{A:()=>c});var a=t(60687),s=t(43210),l=t(51060),o=t(30474),d=t(17019),n=t(29045),i=t(41376);let c=({onFileUploaded:e,endpoint:r,acceptedFileTypes:t="image/*,video/*",maxSizeMB:c=5,currentFileUrl:m=null,label:u="Upload File",className:x=""})=>{let[g,p]=(0,s.useState)(null),[h,b]=(0,s.useState)(m),[y,f]=(0,s.useState)(!1),[v,k]=(0,s.useState)(null),[j,N]=(0,s.useState)(!1),w=(0,s.useRef)(null),S=(0,s.useId)();(0,s.useEffect)(()=>{b(m||null)},[m]);let _=async()=>{if(!g)return void k("Please select a file first");f(!0),k(null),N(!1);let t=new FormData;t.append("file",g);try{let{data:a}=await l.A.post(`http://localhost:5000/api${r}`,t,{headers:{"Content-Type":"multipart/form-data"},withCredentials:!0});N(!0);let s=a?.store?.cover_image_url??a?.store?.logo_url??a?.file_url??a?.file_path;if(s){console.log("Original URL from server:",s);let r=(0,n.Dw)(s);console.log("Formatted URL for Next.js:",r),e(r)}else k("Upload succeeded but no file URL returned")}catch(e){k(l.A.isAxiosError(e)&&e.response?.data?.message||"Error uploading file")}finally{f(!1)}},F=()=>h?g?.type.startsWith("image/")||h.startsWith("data:image/")?(0,a.jsx)(d.fZZ,{className:"w-12 h-12 text-gray-400"}):g?.type.startsWith("video/")||h.startsWith("data:video/")?(0,a.jsx)(d.pVQ,{className:"w-12 h-12 text-gray-400"}):(0,a.jsx)(d.QuH,{className:"w-12 h-12 text-gray-400"}):(0,a.jsx)(d.QuH,{className:"w-12 h-12 text-gray-400"});return(0,a.jsxs)("div",{className:`w-full ${x}`,children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:u}),(0,a.jsxs)("div",{className:"w-full h-48 bg-gray-100 dark:bg-gray-800 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 flex items-center justify-center overflow-hidden relative",children:[h?h.startsWith("data:image/")||h.match(/\.(jpe?g|png|gif|webp|avif|svg)$/i)?(0,a.jsx)("div",{className:"relative w-full h-full",children:h.startsWith("data:")?(0,a.jsx)(o.default,{src:h,alt:"preview",fill:!0,style:{objectFit:"contain"},unoptimized:!0}):(0,a.jsx)(i.A,{src:h,alt:"preview",fill:!0,style:{objectFit:"contain"},debug:!0})}):h.startsWith("data:video/")||h.match(/\.(mp4|mov|webm|ogg)$/i)?(0,a.jsx)("video",{src:h,controls:!0,className:"max-h-full max-w-full"}):F():(0,a.jsxs)("div",{className:"text-center p-4",children:[F(),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500 dark:text-gray-400",children:"Drag & drop a file here, or click to select"}),(0,a.jsxs)("p",{className:"text-xs text-gray-400 dark:text-gray-500 mt-1",children:[t.replace("*","").replace(/,/g,", ")," files up to ",c," MB"]})]}),h&&(0,a.jsx)("button",{type:"button",onClick:()=>{p(null),b(m||null),k(null),N(!1),w.current&&(w.current.value="")},className:"absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:(0,a.jsx)(d.yGN,{className:"w-4 h-4"})})]}),(0,a.jsxs)("div",{className:"mt-4 flex items-center space-x-3 w-full",children:[(0,a.jsx)("input",{id:S,ref:w,type:"file",accept:t,className:"hidden",onChange:e=>{k(null),N(!1);let r=e.target.files?.[0];if(!r)return;let t=1024*c*1024;if(r.size>t)return void k(`File size exceeds ${c} MB`);p(r);let a=new FileReader;a.onloadend=()=>b(a.result),a.readAsDataURL(r)}}),(0,a.jsx)("label",{htmlFor:S,className:"flex-1 text-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer",children:"Select file"}),(0,a.jsx)("button",{type:"button",onClick:_,disabled:!g||y,className:`px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center justify-center ${!g||y?"opacity-50 cursor-not-allowed":""}`,children:y?(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Uploading…"]}):j?(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(d.YrT,{className:"-ml-1 mr-2 h-4 w-4"}),"Uploaded"]}):(0,a.jsxs)("span",{className:"flex items-center",children:[(0,a.jsx)(d.B88,{className:"-ml-1 mr-2 h-4 w-4"}),"Upload"]})})]}),v&&(0,a.jsx)("div",{className:"mt-2 text-sm text-red-600 dark:text-red-400",children:v})]})}},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,579,474,281,846,982,95],()=>t(60862));module.exports=a})();