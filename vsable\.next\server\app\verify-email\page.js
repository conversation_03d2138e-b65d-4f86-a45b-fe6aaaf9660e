(()=>{var e={};e.id=839,e.ids=[839],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20681:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m});var s=t(60687),a=t(43210),i=t(85814),n=t.n(i),l=t(16189),o=t(51060),d=t(17019),c=t(33823);let x="http://localhost:5000/api";function u(){let[e,r]=(0,a.useState)(!0),[t,i]=(0,a.useState)(!1),[u,m]=(0,a.useState)(""),[p,g]=(0,a.useState)(!1),[h,f]=(0,a.useState)(!1),[y,b]=(0,a.useState)("");(0,l.useSearchParams)();let v=(0,l.useRouter)();(0,a.useCallback)(async e=>{try{await o.A.post(`${x}/auth/verify-email`,{token:e}),i(!0),setTimeout(()=>{v.push("/login?verified=true")},3e3)}catch(e){m(o.A.isAxiosError(e)?e.response?.data?.message||"Invalid or expired verification link":"Verification failed")}finally{r(!1)}},[v]);let j=async()=>{if(!y)return void m("Email address is required to resend verification.");g(!0),m(""),f(!1);try{await o.A.post(`${x}/auth/send-verification-email`,{email:y}),f(!0),setTimeout(()=>f(!1),5e3)}catch(e){m(o.A.isAxiosError(e)&&e.response?.data?.message||"Failed to resend verification email")}finally{g(!1)}};return e?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",children:(0,s.jsx)(c.Ay,{size:"lg",text:"Verifying your email..."})}):t?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-6",children:(0,s.jsx)(d.YrT,{className:"h-8 w-8 text-green-600 dark:text-green-400"})}),(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Email Verified!"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600 dark:text-gray-400",children:"Your email has been successfully verified. You can now access all features of your account."}),(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-500 dark:text-gray-500",children:"Redirecting to login page..."})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)(n(),{href:"/login",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-colors",children:"Continue to Login"})})]})}):(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:`mx-auto h-16 w-16 rounded-full flex items-center justify-center mb-6 ${u?"bg-red-100 dark:bg-red-900":"bg-black dark:bg-white"}`,children:u?(0,s.jsx)(d.yGN,{className:"h-8 w-8 text-red-600 dark:text-red-400"}):(0,s.jsx)(d.pHD,{className:"h-8 w-8 text-white dark:text-black"})}),(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:u?"Verification Failed":"Verify Your Email"}),u?(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("p",{className:"text-red-600 dark:text-red-400 mb-4",children:u}),y&&(0,s.jsxs)("div",{className:"space-y-4",children:[h&&(0,s.jsx)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4",children:(0,s.jsx)("p",{className:"text-sm text-green-600 dark:text-green-400",children:"Verification email sent successfully! Please check your inbox."})}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"Need a new verification link?"}),(0,s.jsx)("button",{onClick:j,disabled:p,className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:p?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.Ay,{size:"sm",color:"black"}),(0,s.jsx)("span",{className:"ml-2",children:"Sending..."})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.jTZ,{className:"mr-2 h-4 w-4"}),"Resend Verification Email"]})})]})]}):(0,s.jsx)("p",{className:"mt-4 text-gray-600 dark:text-gray-400",children:"Please check your email for the verification link."})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)(n(),{href:"/login",className:"text-sm text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white transition-colors",children:"Back to login"})})]})})}function m(){return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)(c.Ay,{fullScreen:!0,text:"Loading..."}),children:(0,s.jsx)(u,{})})}},21820:e=>{"use strict";e.exports=require("os")},23539:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\StartUpJourney\\\\Development\\\\Test_dev\\\\vsable\\\\src\\\\app\\\\verify-email\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\verify-email\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33823:(e,r,t)=>{"use strict";t.d(r,{Ay:()=>a});var s=t(60687);t(43210);let a=({size:e="md",color:r="black",text:t,fullScreen:a=!1})=>{let i=(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,s.jsx)("div",{className:`
          ${{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12",xl:"h-16 w-16"}[e]} 
          ${{black:"border-black border-t-transparent",white:"border-white border-t-transparent",gray:"border-gray-300 border-t-transparent dark:border-gray-600"}[r]} 
          border-2 rounded-full animate-spin
        `}),t&&(0,s.jsx)("p",{className:`mt-3 ${{sm:"text-sm",md:"text-base",lg:"text-lg",xl:"text-xl"}[e]} text-gray-600 dark:text-gray-400`,children:t})]});return a?(0,s.jsx)("div",{className:"fixed inset-0 bg-white dark:bg-gray-900 flex items-center justify-center z-50",children:i}):i}},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64854:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(r,o);let d={children:["",{children:["verify-email",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,23539)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\verify-email\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\verify-email\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/verify-email/page",pathname:"/verify-email",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},72130:(e,r,t)=>{Promise.resolve().then(t.bind(t,23539))},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},90282:(e,r,t)=>{Promise.resolve().then(t.bind(t,20681))},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,579,846],()=>t(64854));module.exports=s})();