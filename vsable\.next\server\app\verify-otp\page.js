(()=>{var e={};e.id=734,e.ids=[734],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10385:(e,t,r)=>{Promise.resolve().then(r.bind(r,32738))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13788:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(60687),a=r(43210),i=r(85814),n=r.n(i),o=r(16189),l=r(63213),d=r(17019),c=r(33823);function x(){let[e,t]=(0,a.useState)(["","","","","",""]),[r,i]=(0,a.useState)(""),[x,u]=(0,a.useState)(!1),[p,m]=(0,a.useState)(!1),[h,f]=(0,a.useState)(""),[g,b]=(0,a.useState)(!1),[y,v]=(0,a.useState)(0),{sendOTP:j,verifyOTP:k}=(0,l.A)();(0,o.useSearchParams)();let w=(0,o.useRouter)(),N=(r,s)=>{if(s.length>1)return;let a=[...e];if(a[r]=s,t(a),s&&r<5){let e=document.getElementById(`otp-${r+1}`);e?.focus()}},P=(t,r)=>{if("Backspace"===r.key&&!e[t]&&t>0){let e=document.getElementById(`otp-${t-1}`);e?.focus()}},_=async t=>{t.preventDefault(),f("");let s=e.join("");if(6!==s.length)return void f("Please enter the complete 6-digit code");u(!0);try{await k(r,s),m(!0),setTimeout(()=>{w.push("/login")},2e3)}catch(e){console.error("OTP verification error:",e),f("Invalid or expired OTP code. Please try again.")}finally{u(!1)}},q=async()=>{if(!(y>0)){b(!0),f("");try{await j(r),v(60),t(["","","","","",""])}catch(e){console.error("OTP resend error:",e),f("Failed to resend OTP. Please try again.")}finally{b(!1)}}};return p?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-6",children:(0,s.jsx)(d.YrT,{className:"h-8 w-8 text-green-600 dark:text-green-400"})}),(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Verification Successful"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600 dark:text-gray-400",children:"Your email has been verified successfully. Redirecting to login..."})]})})}):(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsx)("div",{children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto h-16 w-16 bg-black dark:bg-white rounded-full flex items-center justify-center mb-6",children:(0,s.jsx)(d.pHD,{className:"h-8 w-8 text-white dark:text-black"})}),(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Verify your email"}),(0,s.jsxs)("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:["We've sent a 6-digit code to ",(0,s.jsx)("strong",{children:r})]})]})}),(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:_,children:[h&&(0,s.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4",children:(0,s.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:h})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Enter verification code"}),(0,s.jsx)("div",{className:"flex space-x-2 justify-center",children:e.map((e,t)=>(0,s.jsx)("input",{id:`otp-${t}`,type:"text",maxLength:1,value:e,onChange:e=>N(t,e.target.value),onKeyDown:e=>P(t,e),className:"w-12 h-12 text-center text-xl font-bold border-2 border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-black dark:focus:ring-white dark:focus:border-white bg-white dark:bg-gray-800 text-gray-900 dark:text-white",disabled:x},t))})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:x||6!==e.join("").length,className:"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:x?(0,s.jsx)(c.Ay,{size:"sm",color:"white"}):"Verify Code"})}),(0,s.jsxs)("div",{className:"text-center space-y-4",children:[(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Didn't receive the code?"}),(0,s.jsx)("button",{type:"button",onClick:q,disabled:g||y>0,className:"inline-flex items-center text-sm font-medium text-black dark:text-white hover:text-gray-700 dark:hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:g?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.Ay,{size:"sm",color:"black"}),(0,s.jsx)("span",{className:"ml-2",children:"Sending..."})]}):y>0?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.jTZ,{className:"mr-2 h-4 w-4"}),"Resend in ",y,"s"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.jTZ,{className:"mr-2 h-4 w-4"}),"Resend code"]})})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)(n(),{href:"/login",className:"inline-flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white transition-colors",children:[(0,s.jsx)(d.kRp,{className:"mr-2 h-4 w-4"}),"Back to login"]})})]})]})})}function u(){return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)(c.Ay,{fullScreen:!0,text:"Loading..."}),children:(0,s.jsx)(x,{})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26065:(e,t,r)=>{Promise.resolve().then(r.bind(r,13788))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32738:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\StartUpJourney\\\\Development\\\\Test_dev\\\\vsable\\\\src\\\\app\\\\verify-otp\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\verify-otp\\page.tsx","default")},33823:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>a});var s=r(60687);r(43210);let a=({size:e="md",color:t="black",text:r,fullScreen:a=!1})=>{let i=(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,s.jsx)("div",{className:`
          ${{sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12",xl:"h-16 w-16"}[e]} 
          ${{black:"border-black border-t-transparent",white:"border-white border-t-transparent",gray:"border-gray-300 border-t-transparent dark:border-gray-600"}[t]} 
          border-2 rounded-full animate-spin
        `}),r&&(0,s.jsx)("p",{className:`mt-3 ${{sm:"text-sm",md:"text-base",lg:"text-lg",xl:"text-xl"}[e]} text-gray-600 dark:text-gray-400`,children:r})]});return a?(0,s.jsx)("div",{className:"fixed inset-0 bg-white dark:bg-gray-900 flex items-center justify-center z-50",children:i}):i}},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83102:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["verify-otp",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,32738)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\verify-otp\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\verify-otp\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/verify-otp/page",pathname:"/verify-otp",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,579,846],()=>r(83102));module.exports=s})();