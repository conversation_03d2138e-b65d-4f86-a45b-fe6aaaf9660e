exports.id=281,exports.ids=[281],exports.modules={7044:(t,e,i)=>{"use strict";i.d(e,{B:()=>r});let r="undefined"!=typeof window},12157:(t,e,i)=>{"use strict";i.d(e,{L:()=>r});let r=(0,i(43210).createContext)({})},15124:(t,e,i)=>{"use strict";i.d(e,{E:()=>n});var r=i(43210);let n=i(7044).B?r.useLayoutEffect:r.useEffect},18171:(t,e,i)=>{"use strict";i.d(e,{s:()=>n});var r=i(74479);function n(t){return(0,r.G)(t)&&"offsetHeight"in t}},21279:(t,e,i)=>{"use strict";i.d(e,{t:()=>r});let r=(0,i(43210).createContext)(null)},26001:(t,e,i)=>{"use strict";let r;function n(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function s(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function o(t,e,i,r){if("function"==typeof e){let[n,o]=s(r);e=e(void 0!==i?i:t.custom,n,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[n,o]=s(r);e=e(void 0!==i?i:t.custom,n,o)}return e}function a(t,e,i){let r=t.getProps();return o(r,e,void 0!==i?i:r.custom,t)}function l(t,e){return t?.[e]??t?.default??t}i.d(e,{P:()=>sj});let u=t=>t,h={},c=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],p={value:null,addProjectionMetrics:null};function d(t,e){let i=!1,r=!0,n={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,o=c.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,r=new Set,n=!1,s=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(h.schedule(e),t()),l++,e(a)}let h={schedule:(t,e=!1,s=!1)=>{let a=s&&n?i:r;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{r.delete(t),o.delete(t)},process:t=>{if(a=t,n){s=!0;return}n=!0,[i,r]=[r,i],i.forEach(u),e&&p.value&&p.value.frameloop[e].push(l),l=0,i.clear(),n=!1,s&&(s=!1,h.process(t))}};return h}(s,e?i:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:d,update:f,preRender:m,render:y,postRender:v}=o,g=()=>{let s=h.useManualTiming?n.timestamp:performance.now();i=!1,h.useManualTiming||(n.delta=r?1e3/60:Math.max(Math.min(s-n.timestamp,40),1)),n.timestamp=s,n.isProcessing=!0,a.process(n),l.process(n),u.process(n),d.process(n),f.process(n),m.process(n),y.process(n),v.process(n),n.isProcessing=!1,i&&e&&(r=!1,t(g))},b=()=>{i=!0,r=!0,n.isProcessing||t(g)};return{schedule:c.reduce((t,e)=>{let r=o[e];return t[e]=(t,e=!1,n=!1)=>(i||b(),r.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<c.length;e++)o[c[e]].cancel(t)},state:n,steps:o}}let{schedule:f,cancel:m,state:y,steps:v}=d("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),g=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],b=new Set(g),x=new Set(["width","height","top","left","right","bottom",...g]);function w(t,e){-1===t.indexOf(e)&&t.push(e)}function P(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class T{constructor(){this.subscriptions=[]}add(t){return w(this.subscriptions,t),()=>P(this.subscriptions,t)}notify(t,e,i){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](t,e,i);else for(let n=0;n<r;n++){let r=this.subscriptions[n];r&&r(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function S(){r=void 0}let O={now:()=>(void 0===r&&O.set(y.isProcessing||h.useManualTiming?y.timestamp:performance.now()),r),set:t=>{r=t,queueMicrotask(S)}},E=t=>!isNaN(parseFloat(t)),j={current:void 0};class M{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=O.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=O.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=E(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new T);let i=this.events[t].add(e);return"change"===t?()=>{i(),f.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return j.current&&j.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=O.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function A(t,e){return new M(t,e)}let V=t=>Array.isArray(t),C=t=>!!(t&&t.getVelocity);function k(t,e){let i=t.getValue("willChange");if(C(i)&&i.add)return i.add(e);if(!i&&h.WillChange){let i=new h.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let D=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),R="data-"+D("framerAppearId"),L=(t,e)=>i=>e(t(i)),B=(...t)=>t.reduce(L),F=(t,e,i)=>i>e?e:i<t?t:i,I=t=>1e3*t,U=t=>t/1e3,N={layout:0,mainThread:0,waapi:0},$=()=>{},W=()=>{},_=t=>e=>"string"==typeof e&&e.startsWith(t),z=_("--"),Y=_("var(--"),H=t=>!!Y(t)&&X.test(t.split("/*")[0].trim()),X=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,q={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},K={...q,transform:t=>F(0,1,t)},G={...q,default:1},Z=t=>Math.round(1e5*t)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tt=(t,e)=>i=>!!("string"==typeof i&&J.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),te=(t,e,i)=>r=>{if("string"!=typeof r)return r;let[n,s,o,a]=r.match(Q);return{[t]:parseFloat(n),[e]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},ti=t=>F(0,255,t),tr={...q,transform:t=>Math.round(ti(t))},tn={test:tt("rgb","red"),parse:te("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:r=1})=>"rgba("+tr.transform(t)+", "+tr.transform(e)+", "+tr.transform(i)+", "+Z(K.transform(r))+")"},ts={test:tt("#"),parse:function(t){let e="",i="",r="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),r=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),r=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,r+=r,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:n?parseInt(n,16)/255:1}},transform:tn.transform},to=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),ta=to("deg"),tl=to("%"),tu=to("px"),th=to("vh"),tc=to("vw"),tp={...tl,parse:t=>tl.parse(t)/100,transform:t=>tl.transform(100*t)},td={test:tt("hsl","hue"),parse:te("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:r=1})=>"hsla("+Math.round(t)+", "+tl.transform(Z(e))+", "+tl.transform(Z(i))+", "+Z(K.transform(r))+")"},tf={test:t=>tn.test(t)||ts.test(t)||td.test(t),parse:t=>tn.test(t)?tn.parse(t):td.test(t)?td.parse(t):ts.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tn.transform(t):td.transform(t)},tm=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,ty="number",tv="color",tg=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tb(t){let e=t.toString(),i=[],r={color:[],number:[],var:[]},n=[],s=0,o=e.replace(tg,t=>(tf.test(t)?(r.color.push(s),n.push(tv),i.push(tf.parse(t))):t.startsWith("var(")?(r.var.push(s),n.push("var"),i.push(t)):(r.number.push(s),n.push(ty),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:o,indexes:r,types:n}}function tx(t){return tb(t).values}function tw(t){let{split:e,types:i}=tb(t),r=e.length;return t=>{let n="";for(let s=0;s<r;s++)if(n+=e[s],void 0!==t[s]){let e=i[s];e===ty?n+=Z(t[s]):e===tv?n+=tf.transform(t[s]):n+=t[s]}return n}}let tP=t=>"number"==typeof t?0:t,tT={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(Q)?.length||0)+(t.match(tm)?.length||0)>0},parse:tx,createTransformer:tw,getAnimatableNone:function(t){let e=tx(t);return tw(t)(e.map(tP))}};function tS(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tO(t,e){return i=>i>0?e:t}let tE=(t,e,i)=>t+(e-t)*i,tj=(t,e,i)=>{let r=t*t,n=i*(e*e-r)+r;return n<0?0:Math.sqrt(n)},tM=[ts,tn,td],tA=t=>tM.find(e=>e.test(t));function tV(t){let e=tA(t);if($(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===td&&(i=function({hue:t,saturation:e,lightness:i,alpha:r}){t/=360,i/=100;let n=0,s=0,o=0;if(e/=100){let r=i<.5?i*(1+e):i+e-i*e,a=2*i-r;n=tS(a,r,t+1/3),s=tS(a,r,t),o=tS(a,r,t-1/3)}else n=s=o=i;return{red:Math.round(255*n),green:Math.round(255*s),blue:Math.round(255*o),alpha:r}}(i)),i}let tC=(t,e)=>{let i=tV(t),r=tV(e);if(!i||!r)return tO(t,e);let n={...i};return t=>(n.red=tj(i.red,r.red,t),n.green=tj(i.green,r.green,t),n.blue=tj(i.blue,r.blue,t),n.alpha=tE(i.alpha,r.alpha,t),tn.transform(n))},tk=new Set(["none","hidden"]);function tD(t,e){return i=>tE(t,e,i)}function tR(t){return"number"==typeof t?tD:"string"==typeof t?H(t)?tO:tf.test(t)?tC:tF:Array.isArray(t)?tL:"object"==typeof t?tf.test(t)?tC:tB:tO}function tL(t,e){let i=[...t],r=i.length,n=t.map((t,i)=>tR(t)(t,e[i]));return t=>{for(let e=0;e<r;e++)i[e]=n[e](t);return i}}function tB(t,e){let i={...t,...e},r={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(r[n]=tR(t[n])(t[n],e[n]));return t=>{for(let e in r)i[e]=r[e](t);return i}}let tF=(t,e)=>{let i=tT.createTransformer(e),r=tb(t),n=tb(e);return r.indexes.var.length===n.indexes.var.length&&r.indexes.color.length===n.indexes.color.length&&r.indexes.number.length>=n.indexes.number.length?tk.has(t)&&!n.values.length||tk.has(e)&&!r.values.length?function(t,e){return tk.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):B(tL(function(t,e){let i=[],r={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){let s=e.types[n],o=t.indexes[s][r[s]],a=t.values[o]??0;i[n]=a,r[s]++}return i}(r,n),n.values),i):($(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tO(t,e))};function tI(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tE(t,e,i):tR(t)(t,e)}let tU=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>f.update(e,t),stop:()=>m(e),now:()=>y.isProcessing?y.timestamp:O.now()}},tN=(t,e,i=10)=>{let r="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)r+=t(e/(n-1))+", ";return`linear(${r.substring(0,r.length-2)})`};function t$(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function tW(t,e,i){var r,n;let s=Math.max(e-5,0);return r=i-t(s),(n=e-s)?1e3/n*r:0}let t_={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tz(t,e){return t*Math.sqrt(1-e*e)}let tY=["duration","bounce"],tH=["stiffness","damping","mass"];function tX(t,e){return e.some(e=>void 0!==t[e])}function tq(t=t_.visualDuration,e=t_.bounce){let i,r="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:s}=r,o=r.keyframes[0],a=r.keyframes[r.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:c,duration:p,velocity:d,isResolvedFromDuration:f}=function(t){let e={velocity:t_.velocity,stiffness:t_.stiffness,damping:t_.damping,mass:t_.mass,isResolvedFromDuration:!1,...t};if(!tX(t,tH)&&tX(t,tY))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),r=i*i,n=2*F(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:t_.mass,stiffness:r,damping:n}}else{let i=function({duration:t=t_.duration,bounce:e=t_.bounce,velocity:i=t_.velocity,mass:r=t_.mass}){let n,s;$(t<=I(t_.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=F(t_.minDamping,t_.maxDamping,o),t=F(t_.minDuration,t_.maxDuration,U(t)),o<1?(n=e=>{let r=e*o,n=r*t;return .001-(r-i)/tz(e,o)*Math.exp(-n)},s=e=>{let r=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-r),l=tz(Math.pow(e,2),o);return(r*i+i-s)*a*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let r=i;for(let i=1;i<12;i++)r-=t(r)/e(r);return r}(n,s,5/t);if(t=I(t),isNaN(a))return{stiffness:t_.stiffness,damping:t_.damping,duration:t};{let e=Math.pow(a,2)*r;return{stiffness:e,damping:2*o*Math.sqrt(r*e),duration:t}}}(t);(e={...e,...i,mass:t_.mass}).isResolvedFromDuration=!0}return e}({...r,velocity:-U(r.velocity||0)}),m=d||0,y=h/(2*Math.sqrt(u*c)),v=a-o,g=U(Math.sqrt(u/c)),b=5>Math.abs(v);if(n||(n=b?t_.restSpeed.granular:t_.restSpeed.default),s||(s=b?t_.restDelta.granular:t_.restDelta.default),y<1){let t=tz(g,y);i=e=>a-Math.exp(-y*g*e)*((m+y*g*v)/t*Math.sin(t*e)+v*Math.cos(t*e))}else if(1===y)i=t=>a-Math.exp(-g*t)*(v+(m+g*v)*t);else{let t=g*Math.sqrt(y*y-1);i=e=>{let i=Math.exp(-y*g*e),r=Math.min(t*e,300);return a-i*((m+y*g*v)*Math.sinh(r)+t*v*Math.cosh(r))/t}}let x={calculatedDuration:f&&p||null,next:t=>{let e=i(t);if(f)l.done=t>=p;else{let r=0===t?m:0;y<1&&(r=0===t?I(m):tW(i,t,e));let o=Math.abs(a-e)<=s;l.done=Math.abs(r)<=n&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(t$(x),2e4),e=tN(e=>x.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return x}function tK({keyframes:t,velocity:e=0,power:i=.8,timeConstant:r=325,bounceDamping:n=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let c,p,d=t[0],f={done:!1,value:d},m=t=>void 0!==a&&t<a||void 0!==l&&t>l,y=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l,v=i*e,g=d+v,b=void 0===o?g:o(g);b!==g&&(v=b-d);let x=t=>-v*Math.exp(-t/r),w=t=>b+x(t),P=t=>{let e=x(t),i=w(t);f.done=Math.abs(e)<=u,f.value=f.done?b:i},T=t=>{m(f.value)&&(c=t,p=tq({keyframes:[f.value,y(f.value)],velocity:tW(w,t,f.value),damping:n,stiffness:s,restDelta:u,restSpeed:h}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return(p||void 0!==c||(e=!0,P(t),T(t)),void 0!==c&&t>=c)?p.next(t-c):(e||P(t),f)}}}tq.applyToOptions=t=>{let e=function(t,e=100,i){let r=i({...t,keyframes:[0,e]}),n=Math.min(t$(r),2e4);return{type:"keyframes",ease:t=>r.next(n*t).value/e,duration:U(n)}}(t,100,tq);return t.ease=e.ease,t.duration=I(e.duration),t.type="keyframes",t};let tG=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tZ(t,e,i,r){if(t===e&&i===r)return u;let n=e=>(function(t,e,i,r,n){let s,o,a=0;do(s=tG(o=e+(i-e)/2,r,n)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:tG(n(t),e,r)}let tQ=tZ(.42,0,1,1),tJ=tZ(0,0,.58,1),t0=tZ(.42,0,.58,1),t1=t=>Array.isArray(t)&&"number"!=typeof t[0],t2=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t3=t=>e=>1-t(1-e),t4=tZ(.33,1.53,.69,.99),t9=t3(t4),t5=t2(t9),t6=t=>(t*=2)<1?.5*t9(t):.5*(2-Math.pow(2,-10*(t-1))),t7=t=>1-Math.sin(Math.acos(t)),t8=t3(t7),et=t2(t7),ee=t=>Array.isArray(t)&&"number"==typeof t[0],ei={linear:u,easeIn:tQ,easeInOut:t0,easeOut:tJ,circIn:t7,circInOut:et,circOut:t8,backIn:t9,backInOut:t5,backOut:t4,anticipate:t6},er=t=>"string"==typeof t,en=t=>{if(ee(t)){W(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,r,n]=t;return tZ(e,i,r,n)}return er(t)?(W(void 0!==ei[t],`Invalid easing type '${t}'`),ei[t]):t},es=(t,e,i)=>{let r=e-t;return 0===r?1:(i-t)/r};function eo({duration:t=300,keyframes:e,times:i,ease:r="easeInOut"}){var n;let s=t1(r)?r.map(en):en(r),o={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:r,mixer:n}={}){let s=t.length;if(W(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let r=[],n=i||h.mix||tI,s=t.length-1;for(let i=0;i<s;i++){let s=n(t[i],t[i+1]);e&&(s=B(Array.isArray(e)?e[i]||u:e,s)),r.push(s)}return r}(e,r,n),l=a.length,c=i=>{if(o&&i<t[0])return e[0];let r=0;if(l>1)for(;r<t.length-2&&!(i<t[r+1]);r++);let n=es(t[r],t[r+1],i);return a[r](n)};return i?e=>c(F(t[0],t[s-1],e)):c}((n=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let r=1;r<=e;r++){let n=es(0,e,r);t.push(tE(i,1,n))}}(e,t.length-1),e}(e),n.map(e=>e*t)),e,{ease:Array.isArray(s)?s:e.map(()=>s||t0).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let ea=t=>null!==t;function el(t,{repeat:e,repeatType:i="loop"},r,n=1){let s=t.filter(ea),o=n<0||e&&"loop"!==i&&e%2==1?0:s.length-1;return o&&void 0!==r?r:s[o]}let eu={decay:tK,inertia:tK,tween:eo,keyframes:eo,spring:tq};function eh(t){"string"==typeof t.type&&(t.type=eu[t.type])}class ec{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let ep=t=>t/100;class ed extends ec{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=(t=!0)=>{if(t){let{motionValue:t}=this.options;t&&t.updatedAt!==O.now()&&this.tick(O.now())}this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},N.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;eh(t);let{type:e=eo,repeat:i=0,repeatDelay:r=0,repeatType:n,velocity:s=0}=t,{keyframes:o}=t,a=e||eo;a!==eo&&"number"!=typeof o[0]&&(this.mixKeyframes=B(ep,tI(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===n&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=t$(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(i+1)-r,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:r,mixKeyframes:n,mirroredGenerator:s,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:c,repeatDelay:p,type:d,onUpdate:f,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-r/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let y=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?y<0:y>r;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let g=this.currentTime,b=i;if(h){let t=Math.min(this.currentTime,r)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,h+1))%2&&("reverse"===c?(i=1-i,p&&(i-=p/o)):"mirror"===c&&(b=s)),g=F(0,1,i)*o}let x=v?{done:!1,value:u[0]}:b.next(g);n&&(x.value=n(x.value));let{done:w}=x;v||null===a||(w=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let P=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return P&&d!==tK&&(x.value=el(u,this.options,m,this.speed)),f&&f(x.value),P&&this.finish(),x}then(t,e){return this.finished.then(t,e)}get duration(){return U(this.calculatedDuration)}get time(){return U(this.currentTime)}set time(t){t=I(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(O.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=U(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tU,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(O.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,N.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let ef=t=>180*t/Math.PI,em=t=>ev(ef(Math.atan2(t[1],t[0]))),ey={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:em,rotateZ:em,skewX:t=>ef(Math.atan(t[1])),skewY:t=>ef(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},ev=t=>((t%=360)<0&&(t+=360),t),eg=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),eb=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),ex={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:eg,scaleY:eb,scale:t=>(eg(t)+eb(t))/2,rotateX:t=>ev(ef(Math.atan2(t[6],t[5]))),rotateY:t=>ev(ef(Math.atan2(-t[2],t[0]))),rotateZ:em,rotate:em,skewX:t=>ef(Math.atan(t[4])),skewY:t=>ef(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function ew(t){return+!!t.includes("scale")}function eP(t,e){let i,r;if(!t||"none"===t)return ew(e);let n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=ex,r=n;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=ey,r=e}if(!r)return ew(e);let s=i[e],o=r[1].split(",").map(eS);return"function"==typeof s?s(o):o[s]}let eT=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return eP(i,e)};function eS(t){return parseFloat(t.trim())}let eO=t=>t===q||t===tu,eE=new Set(["x","y","z"]),ej=g.filter(t=>!eE.has(t)),eM={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>eP(e,"x"),y:(t,{transform:e})=>eP(e,"y")};eM.translateX=eM.x,eM.translateY=eM.y;let eA=new Set,eV=!1,eC=!1,ek=!1;function eD(){if(eC){let t=Array.from(eA).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return ej.forEach(i=>{let r=t.getValue(i);void 0!==r&&(e.push([i,r.get()]),r.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eC=!1,eV=!1,eA.forEach(t=>t.complete(ek)),eA.clear()}function eR(){eA.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eC=!0)})}class eL{constructor(t,e,i,r,n,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=r,this.element=n,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(eA.add(this),eV||(eV=!0,f.read(eR),f.resolveKeyframes(eD))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:r}=this;if(null===t[0]){let n=r?.get(),s=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let r=i.readValue(e,s);null!=r&&(t[0]=r)}void 0===t[0]&&(t[0]=s),r&&void 0===n&&r.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eA.delete(this)}cancel(){"scheduled"===this.state&&(eA.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eB=t=>t.startsWith("--");function eF(t){let e;return()=>(void 0===e&&(e=t()),e)}let eI=eF(()=>void 0!==window.ScrollTimeline),eU={},eN=function(t,e){let i=eF(t);return()=>eU[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),e$=([t,e,i,r])=>`cubic-bezier(${t}, ${e}, ${i}, ${r})`,eW={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:e$([0,.65,.55,1]),circOut:e$([.55,0,1,.45]),backIn:e$([.31,.01,.66,-.59]),backOut:e$([.33,1.53,.69,.99])};function e_(t){return"function"==typeof t&&"applyToOptions"in t}class ez extends ec{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:r,pseudoElement:n,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!n,this.allowFlatten=s,this.options=t,W("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return e_(t)&&eN()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:r=0,duration:n=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let c=function t(e,i){if(e)return"function"==typeof e?eN()?tN(e,i):"ease-out":ee(e)?e$(e):Array.isArray(e)?e.map(e=>t(e,i)||eW.easeOut):eW[e]}(a,n);Array.isArray(c)&&(h.easing=c),p.value&&N.waapi++;let d={delay:r,duration:n,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};u&&(d.pseudoElement=u);let f=t.animate(h,d);return p.value&&f.finished.finally(()=>{N.waapi--}),f}(e,i,r,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let t=el(r,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){eB(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return U(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return U(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=I(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eI())?(this.animation.timeline=t,u):e(this)}}let eY={anticipate:t6,backInOut:t5,circInOut:et};class eH extends ez{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eY&&(t.ease=eY[t.ease])}(t),eh(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:r,element:n,...s}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new ed({...s,autoplay:!1}),a=I(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let eX=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tT.test(t)||"0"===t)&&!t.startsWith("url("));var eq,eK,eG=i(18171);let eZ=new Set(["opacity","clipPath","filter","transform"]),eQ=eF(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eJ extends ec{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:r=0,repeatDelay:n=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=O.now();let c={autoplay:t,delay:e,type:i,repeat:r,repeatDelay:n,repeatType:s,name:a,motionValue:l,element:u,...h},p=u?.KeyframeResolver||eL;this.keyframeResolver=new p(o,(t,e,i)=>this.onKeyframesResolved(t,e,c,!i),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,r){this.keyframeResolver=void 0;let{name:n,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:c}=i;this.resolvedAt=O.now(),!function(t,e,i,r){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=eX(n,e),a=eX(s,e);return $(o===a,`You are trying to animate ${e} from "${n}" to "${s}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${s} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||e_(i))&&r)}(t,n,s,o)&&((h.instantAnimations||!a)&&c?.(el(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let p={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},d=!l&&function(t){let{motionValue:e,name:i,repeatDelay:r,repeatType:n,damping:s,type:o}=t;if(!(0,eG.s)(e?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return eQ()&&i&&eZ.has(i)&&("transform"!==i||!l)&&!a&&!r&&"mirror"!==n&&0!==s&&"inertia"!==o}(p)?new eH({...p,element:p.motionValue.owner.current}):new ed(p);d.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),ek=!0,eR(),eD(),ek=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e0=t=>null!==t,e1={type:"spring",stiffness:500,damping:25,restSpeed:10},e2=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),e3={type:"keyframes",duration:.8},e4={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e9=(t,{keyframes:e})=>e.length>2?e3:b.has(t)?t.startsWith("scale")?e2(e[1]):e1:e4,e5=(t,e,i,r={},n,s)=>o=>{let a=l(r,t)||{},u=a.delay||r.delay||0,{elapsed:c=0}=r;c-=I(u);let p={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:s?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:r,staggerDirection:n,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&Object.assign(p,e9(t,p)),p.duration&&(p.duration=I(p.duration)),p.repeatDelay&&(p.repeatDelay=I(p.repeatDelay)),void 0!==p.from&&(p.keyframes[0]=p.from);let d=!1;if(!1!==p.type&&(0!==p.duration||p.repeatDelay)||(p.duration=0,0===p.delay&&(d=!0)),(h.instantAnimations||h.skipAnimations)&&(d=!0,p.duration=0,p.delay=0),p.allowFlatten=!a.type&&!a.ease,d&&!s&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},r){let n=t.filter(e0),s=e&&"loop"!==i&&e%2==1?0:n.length-1;return n[s]}(p.keyframes,a);if(void 0!==t)return void f.update(()=>{p.onUpdate(t),p.onComplete()})}return a.isSync?new ed(p):new eJ(p)};function e6(t,e,{delay:i=0,transitionOverride:r,type:n}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:o,...u}=e;r&&(s=r);let h=[],c=n&&t.animationState&&t.animationState.getState()[n];for(let e in u){let r=t.getValue(e,t.latestValues[e]??null),n=u[e];if(void 0===n||c&&function({protectedKeys:t,needsAnimating:e},i){let r=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,r}(c,e))continue;let o={delay:i,...l(s||{},e)},a=r.get();if(void 0!==a&&!r.isAnimating&&!Array.isArray(n)&&n===a&&!o.velocity)continue;let p=!1;if(window.MotionHandoffAnimation){let i=t.props[R];if(i){let t=window.MotionHandoffAnimation(i,e,f);null!==t&&(o.startTime=t,p=!0)}}k(t,e),r.start(e5(e,r,n,t.shouldReduceMotion&&x.has(e)?{type:!1}:o,t,p));let d=r.animation;d&&h.push(d)}return o&&Promise.all(h).then(()=>{f.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:r={},...n}=a(t,e)||{};for(let e in n={...n,...i}){var s;let i=V(s=n[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,A(i))}}(t,o)})}),h}function e7(t,e,i={}){let r=a(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:n=t.getDefaultTransition()||{}}=r||{};i.transitionOverride&&(n=i.transitionOverride);let s=r?()=>Promise.all(e6(t,r,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(r=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=n;return function(t,e,i=0,r=0,n=1,s){let o=[],a=(t.variantChildren.size-1)*r,l=1===n?(t=0)=>t*r:(t=0)=>a-t*r;return Array.from(t.variantChildren).sort(e8).forEach((t,r)=>{t.notify("AnimationStart",e),o.push(e7(t,e,{...s,delay:i+l(r)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,s+r,o,a,i)}:()=>Promise.resolve(),{when:l}=n;if(!l)return Promise.all([s(),o(i.delay)]);{let[t,e]="beforeChildren"===l?[s,o]:[o,s];return t().then(()=>e())}}function e8(t,e){return t.sortNodePosition(e)}function it(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let r=0;r<i;r++)if(e[r]!==t[r])return!1;return!0}function ie(t){return"string"==typeof t||Array.isArray(t)}let ii=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ir=["initial",...ii],is=ir.length,io=[...ii].reverse(),ia=ii.length;function il(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iu(){return{animate:il(!0),whileInView:il(),whileHover:il(),whileTap:il(),whileDrag:il(),whileFocus:il(),exit:il()}}class ih{constructor(t){this.isMounted=!1,this.node=t}update(){}}class ic extends ih{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let r;if(t.notify("AnimationStart",e),Array.isArray(e))r=Promise.all(e.map(e=>e7(t,e,i)));else if("string"==typeof e)r=e7(t,e,i);else{let n="function"==typeof e?a(t,e,i.custom):e;r=Promise.all(e6(t,n,i))}return r.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=iu(),r=!0,s=e=>(i,r)=>{let n=a(t,r,"exit"===e?t.presenceContext?.custom:void 0);if(n){let{transition:t,transitionEnd:e,...r}=n;i={...i,...r,...e}}return i};function o(o){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<is;t++){let r=ir[t],n=e.props[r];(ie(n)||!1===n)&&(i[r]=n)}return i}(t.parent)||{},h=[],c=new Set,p={},d=1/0;for(let e=0;e<ia;e++){var f,m;let a=io[e],y=i[a],v=void 0!==l[a]?l[a]:u[a],g=ie(v),b=a===o?y.isActive:null;!1===b&&(d=e);let x=v===u[a]&&v!==l[a]&&g;if(x&&r&&t.manuallyAnimateOnMount&&(x=!1),y.protectedKeys={...p},!y.isActive&&null===b||!v&&!y.prevProp||n(v)||"boolean"==typeof v)continue;let w=(f=y.prevProp,"string"==typeof(m=v)?m!==f:!!Array.isArray(m)&&!it(m,f)),P=w||a===o&&y.isActive&&!x&&g||e>d&&g,T=!1,S=Array.isArray(v)?v:[v],O=S.reduce(s(a),{});!1===b&&(O={});let{prevResolvedValues:E={}}=y,j={...E,...O},M=e=>{P=!0,c.has(e)&&(T=!0,c.delete(e)),y.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in j){let e=O[t],i=E[t];if(p.hasOwnProperty(t))continue;let r=!1;(V(e)&&V(i)?it(e,i):e===i)?void 0!==e&&c.has(t)?M(t):y.protectedKeys[t]=!0:null!=e?M(t):c.add(t)}y.prevProp=v,y.prevResolvedValues=O,y.isActive&&(p={...p,...O}),r&&t.blockInitialAnimation&&(P=!1);let A=!(x&&w)||T;P&&A&&h.push(...S.map(t=>({animation:t,options:{type:a}})))}if(c.size){let e={};if("boolean"!=typeof l.initial){let i=a(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}c.forEach(i=>{let r=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=r??null}),h.push({animation:e})}let y=!!h.length;return r&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(y=!1),r=!1,y?e(h):Promise.resolve()}return{animateChanges:o,setActive:function(e,r){if(i[e].isActive===r)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,r)),i[e].isActive=r;let n=o(e);for(let t in i)i[t].protectedKeys={};return n},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=iu(),r=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();n(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let ip=0;class id extends ih{constructor(){super(...arguments),this.id=ip++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let r=this.node.animationState.setActive("exit",!t);e&&!t&&r.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let im={x:!1,y:!1};function iy(t,e,i,r={passive:!0}){return t.addEventListener(e,i,r),()=>t.removeEventListener(e,i)}let iv=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function ig(t){return{point:{x:t.pageX,y:t.pageY}}}let ib=t=>e=>iv(e)&&t(e,ig(e));function ix(t,e,i,r){return iy(t,e,ib(i),r)}function iw({top:t,left:e,right:i,bottom:r}){return{x:{min:e,max:i},y:{min:t,max:r}}}function iP(t){return t.max-t.min}function iT(t,e,i,r=.5){t.origin=r,t.originPoint=tE(e.min,e.max,t.origin),t.scale=iP(i)/iP(e),t.translate=tE(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iS(t,e,i,r){iT(t.x,e.x,i.x,r?r.originX:void 0),iT(t.y,e.y,i.y,r?r.originY:void 0)}function iO(t,e,i){t.min=i.min+e.min,t.max=t.min+iP(e)}function iE(t,e,i){t.min=e.min-i.min,t.max=t.min+iP(e)}function ij(t,e,i){iE(t.x,e.x,i.x),iE(t.y,e.y,i.y)}let iM=()=>({translate:0,scale:1,origin:0,originPoint:0}),iA=()=>({x:iM(),y:iM()}),iV=()=>({min:0,max:0}),iC=()=>({x:iV(),y:iV()});function ik(t){return[t("x"),t("y")]}function iD(t){return void 0===t||1===t}function iR({scale:t,scaleX:e,scaleY:i}){return!iD(t)||!iD(e)||!iD(i)}function iL(t){return iR(t)||iB(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iB(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iF(t,e,i,r,n){return void 0!==n&&(t=r+n*(t-r)),r+i*(t-r)+e}function iI(t,e=0,i=1,r,n){t.min=iF(t.min,e,i,r,n),t.max=iF(t.max,e,i,r,n)}function iU(t,{x:e,y:i}){iI(t.x,e.translate,e.scale,e.originPoint),iI(t.y,i.translate,i.scale,i.originPoint)}function iN(t,e){t.min=t.min+e,t.max=t.max+e}function i$(t,e,i,r,n=.5){let s=tE(t.min,t.max,n);iI(t,e,i,s,r)}function iW(t,e){i$(t.x,e.x,e.scaleX,e.scale,e.originX),i$(t.y,e.y,e.scaleY,e.scale,e.originY)}function i_(t,e){return iw(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),r=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}(t.getBoundingClientRect(),e))}let iz=({current:t})=>t?t.ownerDocument.defaultView:null;function iY(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iH=(t,e)=>Math.abs(t-e);class iX{constructor(t,e,{transformPagePoint:i,contextWindow:r,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iG(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iH(t.x,e.x)**2+iH(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:r}=t,{timestamp:n}=y;this.history.push({...r,timestamp:n});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iq(e,this.transformPagePoint),f.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=iG("pointercancel"===t.type?this.lastMoveEventInfo:iq(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),r&&r(t,s)},!iv(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.contextWindow=r||window;let s=iq(ig(t),this.transformPagePoint),{point:o}=s,{timestamp:a}=y;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,iG(s,this.history)),this.removeListeners=B(ix(this.contextWindow,"pointermove",this.handlePointerMove),ix(this.contextWindow,"pointerup",this.handlePointerUp),ix(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),m(this.updatePoint)}}function iq(t,e){return e?{point:e(t.point)}:t}function iK(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iG({point:t},e){return{point:t,delta:iK(t,iZ(e)),offset:iK(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,r=null,n=iZ(t);for(;i>=0&&(r=t[i],!(n.timestamp-r.timestamp>I(.1)));)i--;if(!r)return{x:0,y:0};let s=U(n.timestamp-r.timestamp);if(0===s)return{x:0,y:0};let o={x:(n.x-r.x)/s,y:(n.y-r.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function iZ(t){return t[t.length-1]}function iQ(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iJ(t,e){let i=e.min-t.min,r=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,r]=[r,i]),{min:i,max:r}}function i0(t,e,i){return{min:i1(t,e),max:i1(t,i)}}function i1(t,e){return"number"==typeof t?t:t[e]||0}let i2=new WeakMap;class i3{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iC(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new iX(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(ig(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:r,onDragStart:n}=this.getProps();if(i&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(im[t])return null;else return im[t]=!0,()=>{im[t]=!1};return im.x||im.y?null:(im.x=im.y=!0,()=>{im.x=im.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ik(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tl.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[t];r&&(e=iP(r)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&f.postRender(()=>n(t,e)),k(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:n,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(r&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>ik(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:iz(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:r}=e;this.startAnimation(r);let{onDragEnd:n}=this.getProps();n&&f.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:r}=this.getProps();if(!i||!i4(t,r,this.currentDirection))return;let n=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},r){return void 0!==e&&t<e?t=r?tE(e,t,r.min):Math.max(t,e):void 0!==i&&t>i&&(t=r?tE(i,t,r.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),n.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;t&&iY(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:r,right:n}){return{x:iQ(t.x,i,n),y:iQ(t.y,e,r)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:i0(t,"left","right"),y:i0(t,"top","bottom")}}(e),r!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&ik(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iY(e))return!1;let r=e.current;W(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=function(t,e,i){let r=i_(t,i),{scroll:n}=e;return n&&(iN(r.x,n.offset.x),iN(r.y,n.offset.y)),r}(r,n.root,this.visualElement.getTransformPagePoint()),o=(t=n.layout.layoutBox,{x:iJ(t.x,s.x),y:iJ(t.y,s.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=iw(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:r,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(ik(o=>{if(!i4(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return k(this.visualElement,t),i.start(e5(t,i,0,e,this.visualElement,!1))}stopAnimation(){ik(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){ik(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){ik(e=>{let{drag:i}=this.getProps();if(!i4(e,i,this.currentDirection))return;let{projection:r}=this.visualElement,n=this.getAxisMotionValue(e);if(r&&r.layout){let{min:i,max:s}=r.layout.layoutBox[e];n.set(t[e]-tE(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iY(e)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};ik(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();r[t]=function(t,e){let i=.5,r=iP(t),n=iP(e);return n>r?i=es(e.min,e.max-r,t.min):r>n&&(i=es(t.min,t.max-n,e.min)),F(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),ik(e=>{if(!i4(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:s}=this.constraints[e];i.set(tE(n,s,r[e]))})}addListeners(){if(!this.visualElement.current)return;i2.set(this.visualElement,this);let t=ix(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iY(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),f.read(e);let n=iy(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(ik(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),r(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:r,dragConstraints:n,dragElastic:s,dragMomentum:o}}}function i4(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class i9 extends ih{constructor(t){super(t),this.removeGroupControls=u,this.removeListeners=u,this.controls=new i3(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let i5=t=>(e,i)=>{t&&f.postRender(()=>t(e,i))};class i6 extends ih{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(t){this.session=new iX(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iz(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:i5(t),onStart:i5(e),onMove:i,onEnd:(t,e)=>{delete this.session,r&&f.postRender(()=>r(t,e))}}}mount(){this.removePointerDownListener=ix(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var i7=i(60687);let{schedule:i8}=d(queueMicrotask,!1);var rt=i(43210),re=i(86044),ri=i(12157);let rr=(0,rt.createContext)({}),rn={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rs(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let ro={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tu.test(t))return t;else t=parseFloat(t);let i=rs(t,e.target.x),r=rs(t,e.target.y);return`${i}% ${r}%`}},ra={};class rl extends rt.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:r}=this.props,{projection:n}=t;for(let t in rh)ra[t]=rh[t],z(t)&&(ra[t].isCSSVariable=!0);n&&(e.group&&e.group.add(n),i&&i.register&&r&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),rn.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:r,isPresent:n}=this.props,{projection:s}=i;return s&&(s.isPresent=n,r||t.layoutDependency!==e||void 0===e||t.isPresent!==n?s.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?s.promote():s.relegate()||f.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),i8.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:r}=t;r&&(r.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function ru(t){let[e,i]=(0,re.xQ)(),r=(0,rt.useContext)(ri.L);return(0,i7.jsx)(rl,{...t,layoutGroup:r,switchLayoutGroup:(0,rt.useContext)(rr),isPresent:e,safeToRemove:i})}let rh={borderRadius:{...ro,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ro,borderTopRightRadius:ro,borderBottomLeftRadius:ro,borderBottomRightRadius:ro,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let r=tT.parse(t);if(r.length>5)return t;let n=tT.createTransformer(t),s=+("number"!=typeof r[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;r[0+s]/=o,r[1+s]/=a;let l=tE(o,a,.5);return"number"==typeof r[2+s]&&(r[2+s]/=l),"number"==typeof r[3+s]&&(r[3+s]/=l),n(r)}}};var rc=i(74479);function rp(t){return(0,rc.G)(t)&&"ownerSVGElement"in t}let rd=(t,e)=>t.depth-e.depth;class rf{constructor(){this.children=[],this.isDirty=!1}add(t){w(this.children,t),this.isDirty=!0}remove(t){P(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(rd),this.isDirty=!1,this.children.forEach(t)}}function rm(t){return C(t)?t.get():t}let ry=["TopLeft","TopRight","BottomLeft","BottomRight"],rv=ry.length,rg=t=>"string"==typeof t?parseFloat(t):t,rb=t=>"number"==typeof t||tu.test(t);function rx(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let rw=rT(0,.5,t8),rP=rT(.5,.95,u);function rT(t,e,i){return r=>r<t?0:r>e?1:i(es(t,e,r))}function rS(t,e){t.min=e.min,t.max=e.max}function rO(t,e){rS(t.x,e.x),rS(t.y,e.y)}function rE(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function rj(t,e,i,r,n){return t-=e,t=r+1/i*(t-r),void 0!==n&&(t=r+1/n*(t-r)),t}function rM(t,e,[i,r,n],s,o){!function(t,e=0,i=1,r=.5,n,s=t,o=t){if(tl.test(e)&&(e=parseFloat(e),e=tE(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=tE(s.min,s.max,r);t===s&&(a-=e),t.min=rj(t.min,e,i,a,n),t.max=rj(t.max,e,i,a,n)}(t,e[i],e[r],e[n],e.scale,s,o)}let rA=["x","scaleX","originX"],rV=["y","scaleY","originY"];function rC(t,e,i,r){rM(t.x,e,rA,i?i.x:void 0,r?r.x:void 0),rM(t.y,e,rV,i?i.y:void 0,r?r.y:void 0)}function rk(t){return 0===t.translate&&1===t.scale}function rD(t){return rk(t.x)&&rk(t.y)}function rR(t,e){return t.min===e.min&&t.max===e.max}function rL(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function rB(t,e){return rL(t.x,e.x)&&rL(t.y,e.y)}function rF(t){return iP(t.x)/iP(t.y)}function rI(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class rU{constructor(){this.members=[]}add(t){w(this.members,t),t.scheduleRender()}remove(t){if(P(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:r}=t.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rN={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},r$=["","X","Y","Z"],rW={visibility:"hidden"},r_=0;function rz(t,e,i,r){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),r&&(r[t]=0))}function rY({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:r,resetTransform:n}){return class{constructor(t={},i=e?.()){this.id=r_++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,p.value&&(rN.nodes=rN.calculatedTargetDeltas=rN.calculatedProjections=0),this.nodes.forEach(rq),this.nodes.forEach(r1),this.nodes.forEach(r2),this.nodes.forEach(rK),p.addProjectionMetrics&&p.addProjectionMetrics(rN)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new rf)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new T),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=rp(e)&&!(rp(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:r,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||i)&&(this.isLayoutDirty=!0),t){let i,r=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=O.now(),r=({timestamp:n})=>{let s=n-i;s>=250&&(m(r),t(s-e))};return f.setup(r,!0),()=>m(r)}(r,250),rn.hasAnimatedSinceResize&&(rn.hasAnimatedSinceResize=!1,this.nodes.forEach(r0))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||r)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||n.getDefaultTransition()||r7,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=n.getProps(),u=!this.targetLayout||!rB(this.targetLayout,r),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,h);let e={...l(s,"layout"),onPlay:o,onComplete:a};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||r0(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),m(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(r3),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let r=i.props[R];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(r,"transform",f,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rZ);return}this.isUpdating||this.nodes.forEach(rQ),this.isUpdating=!1,this.nodes.forEach(rJ),this.nodes.forEach(rH),this.nodes.forEach(rX),this.clearAllSnapshots();let t=O.now();y.delta=F(0,1e3/60,t-y.timestamp),y.timestamp=t,y.isProcessing=!0,v.update.process(y),v.preRender.process(y),v.render.process(y),y.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,i8.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rG),this.sharedNodes.forEach(r4)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,f.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){f.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||iP(this.snapshot.measuredBox.x)||iP(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iC(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=r(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!rD(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,s=r!==this.prevTransformTemplateValue;t&&this.instance&&(e||iL(this.latestValues)||s)&&(n(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),r=this.removeElementScroll(i);return t&&(r=this.removeTransform(r)),ne((e=r).x),ne(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iC();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(nr))){let{scroll:t}=this.root;t&&(iN(e.x,t.offset.x),iN(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iC();if(rO(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let r=this.path[i],{scroll:n,options:s}=r;r!==this.root&&n&&s.layoutScroll&&(n.wasRoot&&rO(e,t),iN(e.x,n.offset.x),iN(e.y,n.offset.y))}return e}applyTransform(t,e=!1){let i=iC();rO(i,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];!e&&r.options.layoutScroll&&r.scroll&&r!==r.root&&iW(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),iL(r.latestValues)&&iW(i,r.latestValues)}return iL(this.latestValues)&&iW(i,this.latestValues),i}removeTransform(t){let e=iC();rO(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iL(i.latestValues))continue;iR(i.latestValues)&&i.updateSnapshot();let r=iC();rO(r,i.measurePageBox()),rC(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return iL(this.latestValues)&&rC(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==y.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:n}=this.options;if(this.layout&&(r||n)){if(this.resolvedRelativeTargetAt=y.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iC(),this.relativeTargetOrigin=iC(),ij(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),rO(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iC(),this.targetWithTransforms=iC()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,iO(s.x,o.x,a.x),iO(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rO(this.target,this.layout.layoutBox),iU(this.target,this.targetDelta)):rO(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iC(),this.relativeTargetOrigin=iC(),ij(this.relativeTargetOrigin,this.target,t.target),rO(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}p.value&&rN.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iR(this.parent.latestValues)||iB(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===y.timestamp&&(i=!1),i)return;let{layout:r,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||n))return;rO(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,r=!1){let n,s,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(n=i[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(r&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iW(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,iU(t,s)),r&&iL(n.latestValues)&&iW(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iC());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rE(this.prevProjectionDelta.x,this.projectionDelta.x),rE(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iS(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&rI(this.projectionDelta.x,this.prevProjectionDelta.x)&&rI(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),p.value&&rN.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iA(),this.projectionDelta=iA(),this.projectionDeltaWithTransform=iA()}setAnimationOrigin(t,e=!1){let i,r=this.snapshot,n=r?r.latestValues:{},s={...this.latestValues},o=iA();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=iC(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(r6));this.animationProgress=0,this.mixTargetDelta=e=>{let r=e/1e3;if(r9(o.x,t.x,r),r9(o.y,t.y,r),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,p,d,f,m,y;ij(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),d=this.relativeTarget,f=this.relativeTargetOrigin,m=a,y=r,r5(d.x,f.x,m.x,y),r5(d.y,f.y,m.y,y),i&&(u=this.relativeTarget,p=i,rR(u.x,p.x)&&rR(u.y,p.y))&&(this.isProjectionDirty=!1),i||(i=iC()),rO(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,r,n,s){n?(t.opacity=tE(0,i.opacity??1,rw(r)),t.opacityExit=tE(e.opacity??1,0,rP(r))):s&&(t.opacity=tE(e.opacity??1,i.opacity??1,r));for(let n=0;n<rv;n++){let s=`border${ry[n]}Radius`,o=rx(e,s),a=rx(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||rb(o)===rb(a)?(t[s]=Math.max(tE(rg(o),rg(a),r),0),(tl.test(a)||tl.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=tE(e.rotate||0,i.rotate||0,r))}(s,n,this.latestValues,r,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(!1),this.resumingFrom?.currentAnimation?.stop(!1),this.pendingAnimation&&(m(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=f.update(()=>{rn.hasAnimatedSinceResize=!0,N.layout++,this.motionValue||(this.motionValue=A(0)),this.currentAnimation=function(t,e,i){let r=C(t)?t:A(t);return r.start(e5("",r,e,i)),r.animation}(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{N.layout--},onComplete:()=>{N.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop(!1)),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:r,latestValues:n}=t;if(e&&i&&r){if(this!==t&&this.layout&&r&&ni(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||iC();let e=iP(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let r=iP(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+r}rO(e,i),iW(e,n),iS(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new rU),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let r=this.getStack();r&&r.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let r={};i.z&&rz("z",t,r,this.animationValues);for(let e=0;e<r$.length;e++)rz(`rotate${r$[e]}`,t,r,this.animationValues),rz(`skew${r$[e]}`,t,r,this.animationValues);for(let e in t.render(),r)t.setStaticValue(e,r[e]),this.animationValues&&(this.animationValues[e]=r[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return rW;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=rm(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=rm(t?.pointerEvents)||""),this.hasProjected&&!iL(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let n=r.animationValues||r.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let r="",n=t.x.translate/e.x,s=t.y.translate/e.y,o=i?.z||0;if((n||s||o)&&(r=`translate3d(${n}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(r+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:s,skewX:o,skewY:a}=i;t&&(r=`perspective(${t}px) ${r}`),e&&(r+=`rotate(${e}deg) `),n&&(r+=`rotateX(${n}deg) `),s&&(r+=`rotateY(${s}deg) `),o&&(r+=`skewX(${o}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(r+=`scale(${a}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),i&&(e.transform=i(n,e.transform));let{x:s,y:o}=this.projectionDelta;for(let t in e.transformOrigin=`${100*s.origin}% ${100*o.origin}% 0`,r.animationValues?e.opacity=r===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:e.opacity=r===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,ra){if(void 0===n[t])continue;let{correct:i,applyTo:s,isCSSVariable:o}=ra[t],a="none"===e.transform?n[t]:i(n[t],r);if(s){let t=s.length;for(let i=0;i<t;i++)e[s[i]]=a}else o?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=r===this?rm(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop(!1)),this.root.nodes.forEach(rZ),this.root.sharedNodes.clear()}}}function rH(t){t.updateLayout()}function rX(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:r}=t.layout,{animationType:n}=t.options,s=e.source!==t.layout.source;"size"===n?ik(t=>{let r=s?e.measuredBox[t]:e.layoutBox[t],n=iP(r);r.min=i[t].min,r.max=r.min+n}):ni(n,e.layoutBox,i)&&ik(r=>{let n=s?e.measuredBox[r]:e.layoutBox[r],o=iP(i[r]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[r].max=t.relativeTarget[r].min+o)});let o=iA();iS(o,i,e.layoutBox);let a=iA();s?iS(a,t.applyTransform(r,!0),e.measuredBox):iS(a,i,e.layoutBox);let l=!rD(o),u=!1;if(!t.resumeFrom){let r=t.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:n,layout:s}=r;if(n&&s){let o=iC();ij(o,e.layoutBox,n.layoutBox);let a=iC();ij(a,i,s.layoutBox),rB(o,a)||(u=!0),r.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=r)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function rq(t){p.value&&rN.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function rK(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function rG(t){t.clearSnapshot()}function rZ(t){t.clearMeasurements()}function rQ(t){t.isLayoutDirty=!1}function rJ(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function r0(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function r1(t){t.resolveTargetDelta()}function r2(t){t.calcProjection()}function r3(t){t.resetSkewAndRotation()}function r4(t){t.removeLeadSnapshot()}function r9(t,e,i){t.translate=tE(e.translate,0,i),t.scale=tE(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function r5(t,e,i,r){t.min=tE(e.min,i.min,r),t.max=tE(e.max,i.max,r)}function r6(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let r7={duration:.45,ease:[.4,0,.1,1]},r8=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),nt=r8("applewebkit/")&&!r8("chrome/")?Math.round:u;function ne(t){t.min=nt(t.min),t.max=nt(t.max)}function ni(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(rF(e)-rF(i)))}function nr(t){return t!==t.root&&t.scroll?.wasRoot}let nn=rY({attachResizeListener:(t,e)=>iy(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ns={current:void 0},no=rY({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!ns.current){let t=new nn({});t.mount(window),t.setOptions({layoutScroll:!0}),ns.current=t}return ns.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function na(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),r=new AbortController;return[i,{passive:!0,...e,signal:r.signal},()=>r.abort()]}function nl(t){return!("touch"===t.pointerType||im.x||im.y)}function nu(t,e,i){let{props:r}=t;t.animationState&&r.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=r["onHover"+i];n&&f.postRender(()=>n(e,ig(e)))}class nh extends ih{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=na(t,i),o=t=>{if(!nl(t))return;let{target:i}=t,r=e(i,t);if("function"!=typeof r||!i)return;let s=t=>{nl(t)&&(r(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,n)};return r.forEach(t=>{t.addEventListener("pointerenter",o,n)}),s}(t,(t,e)=>(nu(this.node,e,"Start"),t=>nu(this.node,t,"End"))))}unmount(){}}class nc extends ih{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=B(iy(this.node.current,"focus",()=>this.onFocus()),iy(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let np=(t,e)=>!!e&&(t===e||np(t,e.parentElement)),nd=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),nf=new WeakSet;function nm(t){return e=>{"Enter"===e.key&&t(e)}}function ny(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let nv=(t,e)=>{let i=t.currentTarget;if(!i)return;let r=nm(()=>{if(nf.has(i))return;ny(i,"down");let t=nm(()=>{ny(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>ny(i,"cancel"),e)});i.addEventListener("keydown",r,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",r),e)};function ng(t){return iv(t)&&!(im.x||im.y)}function nb(t,e,i){let{props:r}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&r.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=r["onTap"+("End"===i?"":i)];n&&f.postRender(()=>n(e,ig(e)))}class nx extends ih{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=na(t,i),o=t=>{let r=t.currentTarget;if(!ng(t))return;nf.add(r);let s=e(r,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),nf.has(r)&&nf.delete(r),ng(t)&&"function"==typeof s&&s(t,{success:e})},a=t=>{o(t,r===window||r===document||i.useGlobalTarget||np(r,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,n),window.addEventListener("pointercancel",l,n)};return r.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,n),(0,eG.s)(t))&&(t.addEventListener("focus",t=>nv(t,n)),nd.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(nb(this.node,e,"Start"),(t,{success:e})=>nb(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let nw=new WeakMap,nP=new WeakMap,nT=t=>{let e=nw.get(t.target);e&&e(t)},nS=t=>{t.forEach(nT)},nO={some:0,all:1};class nE extends ih{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:r="some",once:n}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:nO[r]};return function(t,e,i){let r=function({root:t,...e}){let i=t||document;nP.has(i)||nP.set(i,{});let r=nP.get(i),n=JSON.stringify(e);return r[n]||(r[n]=new IntersectionObserver(nS,{root:t,...e})),r[n]}(e);return nw.set(t,i),r.observe(t),()=>{nw.delete(t),r.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),s=e?i:r;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let nj=(0,rt.createContext)({strict:!1});var nM=i(32582);let nA=(0,rt.createContext)({});function nV(t){return n(t.animate)||ir.some(e=>ie(t[e]))}function nC(t){return!!(nV(t)||t.variants)}function nk(t){return Array.isArray(t)?t.join(" "):t}var nD=i(7044);let nR={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},nL={};for(let t in nR)nL[t]={isEnabled:e=>nR[t].some(t=>!!e[t])};let nB=Symbol.for("motionComponentSymbol");var nF=i(21279),nI=i(15124);function nU(t,{layout:e,layoutId:i}){return b.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!ra[t]||"opacity"===t)}let nN=(t,e)=>e&&"number"==typeof t?e.transform(t):t,n$={...q,transform:Math.round},nW={borderWidth:tu,borderTopWidth:tu,borderRightWidth:tu,borderBottomWidth:tu,borderLeftWidth:tu,borderRadius:tu,radius:tu,borderTopLeftRadius:tu,borderTopRightRadius:tu,borderBottomRightRadius:tu,borderBottomLeftRadius:tu,width:tu,maxWidth:tu,height:tu,maxHeight:tu,top:tu,right:tu,bottom:tu,left:tu,padding:tu,paddingTop:tu,paddingRight:tu,paddingBottom:tu,paddingLeft:tu,margin:tu,marginTop:tu,marginRight:tu,marginBottom:tu,marginLeft:tu,backgroundPositionX:tu,backgroundPositionY:tu,rotate:ta,rotateX:ta,rotateY:ta,rotateZ:ta,scale:G,scaleX:G,scaleY:G,scaleZ:G,skew:ta,skewX:ta,skewY:ta,distance:tu,translateX:tu,translateY:tu,translateZ:tu,x:tu,y:tu,z:tu,perspective:tu,transformPerspective:tu,opacity:K,originX:tp,originY:tp,originZ:tu,zIndex:n$,fillOpacity:K,strokeOpacity:K,numOctaves:n$},n_={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nz=g.length;function nY(t,e,i){let{style:r,vars:n,transformOrigin:s}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(b.has(t)){o=!0;continue}if(z(t)){n[t]=i;continue}{let e=nN(i,nW[t]);t.startsWith("origin")?(a=!0,s[t]=e):r[t]=e}}if(!e.transform&&(o||i?r.transform=function(t,e,i){let r="",n=!0;for(let s=0;s<nz;s++){let o=g[s],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=nN(a,nW[o]);if(!l){n=!1;let e=n_[o]||o;r+=`${e}(${t}) `}i&&(e[o]=t)}}return r=r.trim(),i?r=i(e,n?"":r):n&&(r="none"),r}(e,t.transform,i):r.transform&&(r.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;r.transformOrigin=`${t} ${e} ${i}`}}let nH=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function nX(t,e,i){for(let r in e)C(e[r])||nU(r,i)||(t[r]=e[r])}let nq={offset:"stroke-dashoffset",array:"stroke-dasharray"},nK={offset:"strokeDashoffset",array:"strokeDasharray"};function nG(t,{attrX:e,attrY:i,attrScale:r,pathLength:n,pathSpacing:s=1,pathOffset:o=0,...a},l,u,h){if(nY(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:p}=t;c.transform&&(p.transform=c.transform,delete c.transform),(p.transform||c.transformOrigin)&&(p.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),p.transform&&(p.transformBox=h?.transformBox??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==r&&(c.scale=r),void 0!==n&&function(t,e,i=1,r=0,n=!0){t.pathLength=1;let s=n?nq:nK;t[s.offset]=tu.transform(-r);let o=tu.transform(e),a=tu.transform(i);t[s.array]=`${o} ${a}`}(c,n,s,o,!1)}let nZ=()=>({...nH(),attrs:{}}),nQ=t=>"string"==typeof t&&"svg"===t.toLowerCase(),nJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function n0(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||nJ.has(t)}let n1=t=>!n0(t);try{!function(t){t&&(n1=e=>e.startsWith("on")?!n0(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let n2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function n3(t){if("string"!=typeof t||t.includes("-"));else if(n2.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var n4=i(72789);let n9=t=>(e,i)=>{let r=(0,rt.useContext)(nA),s=(0,rt.useContext)(nF.t),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,r,s){return{latestValues:function(t,e,i,r){let s={},a=r(t,{});for(let t in a)s[t]=rm(a[t]);let{initial:l,animate:u}=t,h=nV(t),c=nC(t);e&&c&&!h&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let p=!!i&&!1===i.initial,d=(p=p||!1===l)?u:l;if(d&&"boolean"!=typeof d&&!n(d)){let e=Array.isArray(d)?d:[d];for(let i=0;i<e.length;i++){let r=o(t,e[i]);if(r){let{transitionEnd:t,transition:e,...i}=r;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=p?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(i,r,s,t),renderState:e()}})(t,e,r,s);return i?a():(0,n4.M)(a)};function n5(t,e,i){let{style:r}=t,n={};for(let s in r)(C(r[s])||e.style&&C(e.style[s])||nU(s,t)||i?.getValue(s)?.liveStyle!==void 0)&&(n[s]=r[s]);return n}let n6={useVisualState:n9({scrapeMotionValuesFromProps:n5,createRenderState:nH})};function n7(t,e,i){let r=n5(t,e,i);for(let i in t)(C(t[i])||C(e[i]))&&(r[-1!==g.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return r}let n8={useVisualState:n9({scrapeMotionValuesFromProps:n7,createRenderState:nZ})},st=t=>e=>e.test(t),se=[q,tu,tl,ta,tc,th,{test:t=>"auto"===t,parse:t=>t}],si=t=>se.find(st(t)),sr=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),sn=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ss=t=>/^0[^.\s]+$/u.test(t),so=new Set(["brightness","contrast","saturate","opacity"]);function sa(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[r]=i.match(Q)||[];if(!r)return t;let n=i.replace(r,""),s=+!!so.has(e);return r!==i&&(s*=100),e+"("+s+n+")"}let sl=/\b([a-z-]*)\(.*?\)/gu,su={...tT,getAnimatableNone:t=>{let e=t.match(sl);return e?e.map(sa).join(" "):t}},sh={...nW,color:tf,backgroundColor:tf,outlineColor:tf,fill:tf,stroke:tf,borderColor:tf,borderTopColor:tf,borderRightColor:tf,borderBottomColor:tf,borderLeftColor:tf,filter:su,WebkitFilter:su},sc=t=>sh[t];function sp(t,e){let i=sc(t);return i!==su&&(i=tT),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let sd=new Set(["auto","none","0"]);class sf extends eL{constructor(t,e,i,r,n){super(t,e,i,r,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let r=t[i];if("string"==typeof r&&H(r=r.trim())){let n=function t(e,i,r=1){W(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,s]=function(t){let e=sn.exec(t);if(!e)return[,];let[,i,r,n]=e;return[`--${i??r}`,n]}(e);if(!n)return;let o=window.getComputedStyle(i).getPropertyValue(n);if(o){let t=o.trim();return sr(t)?parseFloat(t):t}return H(s)?t(s,i,r+1):s}(r,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!x.has(i)||2!==t.length)return;let[r,n]=t,s=si(r),o=si(n);if(s!==o)if(eO(s)&&eO(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else eM[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var r;(null===t[e]||("number"==typeof(r=t[e])?0===r:null===r||"none"===r||"0"===r||ss(r)))&&i.push(e)}i.length&&function(t,e,i){let r,n=0;for(;n<t.length&&!r;){let e=t[n];"string"==typeof e&&!sd.has(e)&&tb(e).values.length&&(r=t[n]),n++}if(r&&i)for(let n of e)t[n]=sp(i,r)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eM[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let r=e[e.length-1];void 0!==r&&t.getValue(i,r).jump(r,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let r=t.getValue(e);r&&r.jump(this.measuredOrigin,!1);let n=i.length-1,s=i[n];i[n]=eM[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let sm=[...se,tf,tT],sy=t=>sm.find(st(t)),sv={current:null},sg={current:!1},sb=new WeakMap,sx=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sw{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:n,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eL,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=O.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,f.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=o,this.blockInitialAnimation=!!n,this.isControllingVariants=nV(e),this.isVariantNode=nC(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&C(e)&&e.set(a[t],!1)}}mount(t){this.current=t,sb.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),sg.current||function(){if(sg.current=!0,nD.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>sv.current=t.matches;t.addListener(e),e()}else sv.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sv.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),m(this.notifyUpdate),m(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let r=b.has(t);r&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&f.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in nL){let e=nL[t];if(!e)continue;let{isEnabled:i,Feature:r}=e;if(!this.features[t]&&r&&i(this.props)&&(this.features[t]=new r(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iC()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<sx.length;e++){let i=sx[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=t["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(t,e,i){for(let r in e){let n=e[r],s=i[r];if(C(n))t.addValue(r,n);else if(C(s))t.addValue(r,A(n,{owner:t}));else if(s!==n)if(t.hasValue(r)){let e=t.getValue(r);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(r);t.addValue(r,A(void 0!==e?e:n,{owner:t}))}}for(let r in i)void 0===e[r]&&t.removeValue(r);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=A(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(sr(i)||ss(i))?i=parseFloat(i):!sy(i)&&tT.test(e)&&(i=sp(t,e)),this.setBaseTarget(t,C(i)?i.get():i)),C(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let r=o(this.props,i,this.presenceContext?.custom);r&&(e=r[t])}if(i&&void 0!==e)return e;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||C(r)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new T),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class sP extends sw{constructor(){super(...arguments),this.KeyframeResolver=sf}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;C(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function sT(t,{style:e,vars:i},r,n){for(let s in Object.assign(t.style,e,n&&n.getProjectionStyles(r)),i)t.style.setProperty(s,i[s])}class sS extends sP{constructor(){super(...arguments),this.type="html",this.renderInstance=sT}readValueFromInstance(t,e){if(b.has(e))return this.projection?.isProjecting?ew(e):eT(t,e);{let i=window.getComputedStyle(t),r=(z(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:e}){return i_(t,e)}build(t,e,i){nY(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return n5(t,e,i)}}let sO=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class sE extends sP{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iC}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(b.has(e)){let t=sc(e);return t&&t.default||0}return e=sO.has(e)?e:D(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return n7(t,e,i)}build(t,e,i){nG(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,r){for(let i in sT(t,e,void 0,r),e.attrs)t.setAttribute(sO.has(i)?i:D(i),e.attrs[i])}mount(t){this.isSVGTag=nQ(t.tagName),super.mount(t)}}let sj=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,r)=>"create"===r?t:(e.has(r)||e.set(r,t(r)),e.get(r))})}((eq={animation:{Feature:ic},exit:{Feature:id},inView:{Feature:nE},tap:{Feature:nx},focus:{Feature:nc},hover:{Feature:nh},pan:{Feature:i6},drag:{Feature:i9,ProjectionNode:no,MeasureLayout:ru},layout:{ProjectionNode:no,MeasureLayout:ru}},eK=(t,e)=>n3(t)?new sE(e):new sS(e,{allowProjection:t!==rt.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:r,Component:n}){function s(t,s){var o,a,l;let u,h={...(0,rt.useContext)(nM.Q),...t,layoutId:function({layoutId:t}){let e=(0,rt.useContext)(ri.L).id;return e&&void 0!==t?e+"-"+t:t}(t)},{isStatic:c}=h,p=function(t){let{initial:e,animate:i}=function(t,e){if(nV(t)){let{initial:e,animate:i}=t;return{initial:!1===e||ie(e)?e:void 0,animate:ie(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,rt.useContext)(nA));return(0,rt.useMemo)(()=>({initial:e,animate:i}),[nk(e),nk(i)])}(t),d=r(t,c);if(!c&&nD.B){a=0,l=0,(0,rt.useContext)(nj).strict;let t=function(t){let{drag:e,layout:i}=nL;if(!e&&!i)return{};let r={...e,...i};return{MeasureLayout:e?.isEnabled(t)||i?.isEnabled(t)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(h);u=t.MeasureLayout,p.visualElement=function(t,e,i,r,n){let{visualElement:s}=(0,rt.useContext)(nA),o=(0,rt.useContext)(nj),a=(0,rt.useContext)(nF.t),l=(0,rt.useContext)(nM.Q).reducedMotion,u=(0,rt.useRef)(null);r=r||o.renderer,!u.current&&r&&(u.current=r(t,{visualState:e,parent:s,props:i,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let h=u.current,c=(0,rt.useContext)(rr);h&&!h.projection&&n&&("html"===h.type||"svg"===h.type)&&function(t,e,i,r){let{layoutId:n,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:s,alwaysMeasureLayout:!!o||a&&iY(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:r,crossfade:h,layoutScroll:l,layoutRoot:u})}(u.current,i,n,c);let p=(0,rt.useRef)(!1);(0,rt.useInsertionEffect)(()=>{h&&p.current&&h.update(i,a)});let d=i[R],f=(0,rt.useRef)(!!d&&!window.MotionHandoffIsComplete?.(d)&&window.MotionHasOptimisedAnimation?.(d));return(0,nI.E)(()=>{h&&(p.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),i8.render(h.render),f.current&&h.animationState&&h.animationState.animateChanges())}),(0,rt.useEffect)(()=>{h&&(!f.current&&h.animationState&&h.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(d)}),f.current=!1))}),h}(n,d,h,e,t.ProjectionNode)}return(0,i7.jsxs)(nA.Provider,{value:p,children:[u&&p.visualElement?(0,i7.jsx)(u,{visualElement:p.visualElement,...h}):null,i(n,t,(o=p.visualElement,(0,rt.useCallback)(t=>{t&&d.onMount&&d.onMount(t),o&&(t?o.mount(t):o.unmount()),s&&("function"==typeof s?s(t):iY(s)&&(s.current=t))},[o])),d,c,p.visualElement)]})}t&&function(t){for(let e in t)nL[e]={...nL[e],...t[e]}}(t),s.displayName=`motion.${"string"==typeof n?n:`create(${n.displayName??n.name??""})`}`;let o=(0,rt.forwardRef)(s);return o[nB]=n,o}({...n3(t)?n8:n6,preloadedFeatures:eq,useRender:function(t=!1){return(e,i,r,{latestValues:n},s)=>{let o=(n3(e)?function(t,e,i,r){let n=(0,rt.useMemo)(()=>{let i=nZ();return nG(i,e,nQ(r),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};nX(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},r=function(t,e){let i=t.style||{},r={};return nX(r,i,t),Object.assign(r,function({transformTemplate:t},e){return(0,rt.useMemo)(()=>{let i=nH();return nY(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),r}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=r,i})(i,n,s,e),a=function(t,e,i){let r={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(n1(n)||!0===i&&n0(n)||!e&&!n0(n)||t.draggable&&n.startsWith("onDrag"))&&(r[n]=t[n]);return r}(i,"string"==typeof e,t),l=e!==rt.Fragment?{...a,...o,ref:r}:{},{children:u}=i,h=(0,rt.useMemo)(()=>C(u)?u.get():u,[u]);return(0,rt.createElement)(e,{...l,children:h})}}(e),createVisualElement:eK,Component:t})}))},31756:(t,e,i)=>{(()=>{var e={296:(t,e,i)=>{var r=/^\s+|\s+$/g,n=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,o=/^0o[0-7]+$/i,a=parseInt,l="object"==typeof i.g&&i.g&&i.g.Object===Object&&i.g,u="object"==typeof self&&self&&self.Object===Object&&self,h=l||u||Function("return this")(),c=Object.prototype.toString,p=Math.max,d=Math.min,f=function(){return h.Date.now()};function m(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function y(t){if("number"==typeof t)return t;if("symbol"==typeof(e=t)||e&&"object"==typeof e&&"[object Symbol]"==c.call(e))return NaN;if(m(t)){var e,i="function"==typeof t.valueOf?t.valueOf():t;t=m(i)?i+"":i}if("string"!=typeof t)return 0===t?t:+t;t=t.replace(r,"");var l=s.test(t);return l||o.test(t)?a(t.slice(2),l?2:8):n.test(t)?NaN:+t}t.exports=function(t,e,i){var r,n,s,o,a,l,u=0,h=!1,c=!1,v=!0;if("function"!=typeof t)throw TypeError("Expected a function");function g(e){var i=r,s=n;return r=n=void 0,u=e,o=t.apply(s,i)}function b(t){var i=t-l;return void 0===l||i>=e||i<0||c&&t-u>=s}function x(){var t,i=f();if(b(i))return w(i);a=setTimeout(x,(t=e-(i-l),c?d(t,s-(i-u)):t))}function w(t){return a=void 0,v&&r?g(t):(r=n=void 0,o)}function P(){var t,i=f(),s=b(i);if(r=arguments,n=this,l=i,s){if(void 0===a)return u=t=l,a=setTimeout(x,e),h?g(t):o;if(c)return a=setTimeout(x,e),g(l)}return void 0===a&&(a=setTimeout(x,e)),o}return e=y(e)||0,m(i)&&(h=!!i.leading,s=(c="maxWait"in i)?p(y(i.maxWait)||0,e):s,v="trailing"in i?!!i.trailing:v),P.cancel=function(){void 0!==a&&clearTimeout(a),u=0,r=l=n=a=void 0},P.flush=function(){return void 0===a?o:w(f())},P}},96:(t,e,i)=>{var r="Expected a function",n=NaN,s=/^\s+|\s+$/g,o=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,l=/^0o[0-7]+$/i,u=parseInt,h="object"==typeof i.g&&i.g&&i.g.Object===Object&&i.g,c="object"==typeof self&&self&&self.Object===Object&&self,p=h||c||Function("return this")(),d=Object.prototype.toString,f=Math.max,m=Math.min,y=function(){return p.Date.now()};function v(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function g(t){if("number"==typeof t)return t;if("symbol"==typeof(e=t)||e&&"object"==typeof e&&"[object Symbol]"==d.call(e))return n;if(v(t)){var e,i="function"==typeof t.valueOf?t.valueOf():t;t=v(i)?i+"":i}if("string"!=typeof t)return 0===t?t:+t;t=t.replace(s,"");var r=a.test(t);return r||l.test(t)?u(t.slice(2),r?2:8):o.test(t)?n:+t}t.exports=function(t,e,i){var n=!0,s=!0;if("function"!=typeof t)throw TypeError(r);return v(i)&&(n="leading"in i?!!i.leading:n,s="trailing"in i?!!i.trailing:s),function(t,e,i){var n,s,o,a,l,u,h=0,c=!1,p=!1,d=!0;if("function"!=typeof t)throw TypeError(r);function b(e){var i=n,r=s;return n=s=void 0,h=e,a=t.apply(r,i)}function x(t){var i=t-u;return void 0===u||i>=e||i<0||p&&t-h>=o}function w(){var t,i=y();if(x(i))return P(i);l=setTimeout(w,(t=e-(i-u),p?m(t,o-(i-h)):t))}function P(t){return l=void 0,d&&n?b(t):(n=s=void 0,a)}function T(){var t,i=y(),r=x(i);if(n=arguments,s=this,u=i,r){if(void 0===l)return h=t=u,l=setTimeout(w,e),c?b(t):a;if(p)return l=setTimeout(w,e),b(u)}return void 0===l&&(l=setTimeout(w,e)),a}return e=g(e)||0,v(i)&&(c=!!i.leading,o=(p="maxWait"in i)?f(g(i.maxWait)||0,e):o,d="trailing"in i?!!i.trailing:d),T.cancel=function(){void 0!==l&&clearTimeout(l),h=0,n=u=s=l=void 0},T.flush=function(){return void 0===l?a:P(y())},T}(t,e,{leading:n,maxWait:e,trailing:s})}},703:(t,e,i)=>{"use strict";var r=i(414);function n(){}function s(){}s.resetWarningCache=n,t.exports=function(){function t(t,e,i,n,s,o){if(o!==r){var a=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function e(){return t}t.isRequired=t;var i={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:s,resetWarningCache:n};return i.PropTypes=i,i}},697:(t,e,i)=>{t.exports=i(703)()},414:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"}},r={};function n(t){var i=r[t];if(void 0!==i)return i.exports;var s=r[t]={exports:{}};return e[t](s,s.exports,n),s.exports}n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var i in e)n.o(e,i)&&!n.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(t){if("object"==typeof window)return window}}(),n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var s={};(()=>{"use strict";n.r(s),n.d(s,{LazyLoadComponent:()=>N,LazyLoadImage:()=>G,trackWindowScroll:()=>V});let t=i(43210);var e=n.n(t),r=n(697);function o(){return"undefined"!=typeof window&&"IntersectionObserver"in window&&"isIntersecting"in window.IntersectionObserverEntry.prototype}function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,r)}return i}function u(t){var e=function(t,e){if("object"!==a(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,"string");if("object"!==a(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"===a(e)?e:String(e)}function h(t,e){return(h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function c(t){return(c=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var p=function(t){t.forEach(function(t){t.isIntersecting&&t.target.onVisible()})},d={},f=function(t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");s.prototype=Object.create(t&&t.prototype,{constructor:{value:s,writable:!0,configurable:!0}}),Object.defineProperty(s,"prototype",{writable:!1}),t&&h(s,t);var i,r,n=(r=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,e=c(s);t=r?Reflect.construct(e,arguments,c(this).constructor):e.apply(this,arguments);if(t&&("object"===a(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");if(void 0===this)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return this});function s(t){var e,i;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,s),(e=n.call(this,t)).supportsObserver=!t.scrollPosition&&t.useIntersectionObserver&&o(),e.supportsObserver&&(d[i=t.threshold]=d[i]||new IntersectionObserver(p,{rootMargin:i+"px"}),e.observer=d[i]),e}return i=[{key:"componentDidMount",value:function(){this.placeholder&&this.observer&&(this.placeholder.onVisible=this.props.onVisible,this.observer.observe(this.placeholder)),this.supportsObserver||this.updateVisibility()}},{key:"componentWillUnmount",value:function(){this.observer&&this.placeholder&&this.observer.unobserve(this.placeholder)}},{key:"componentDidUpdate",value:function(){this.supportsObserver||this.updateVisibility()}},{key:"getPlaceholderBoundingBox",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props.scrollPosition,e=this.placeholder.getBoundingClientRect(),i=this.placeholder.style,r=parseInt(i.getPropertyValue("margin-left"),10)||0,n=parseInt(i.getPropertyValue("margin-top"),10)||0;return{bottom:t.y+e.bottom+n,left:t.x+e.left+r,right:t.x+e.right+r,top:t.y+e.top+n}}},{key:"isPlaceholderInViewport",value:function(){if("undefined"==typeof window||!this.placeholder)return!1;var t=this.props,e=t.scrollPosition,i=t.threshold,r=this.getPlaceholderBoundingBox(e),n=e.y+window.innerHeight,s=e.x,o=e.x+window.innerWidth;return!!(e.y-i<=r.bottom&&n+i>=r.top&&s-i<=r.right&&o+i>=r.left)}},{key:"updateVisibility",value:function(){this.isPlaceholderInViewport()&&this.props.onVisible()}},{key:"render",value:function(){var t=this,i=this.props,r=i.className,n=i.height,s=i.placeholder,o=i.style,a=i.width;if(s&&"function"!=typeof s.type)return e().cloneElement(s,{ref:function(e){return t.placeholder=e}});var h=function(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?l(Object(i),!0).forEach(function(e){var r,n;r=e,n=i[e],(r=u(r))in t?Object.defineProperty(t,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[r]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):l(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}({display:"inline-block"},o);return void 0!==a&&(h.width=a),void 0!==n&&(h.height=n),e().createElement("span",{className:r,ref:function(e){return t.placeholder=e},style:h},s)}}],function(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,u(r.key),r)}}(s.prototype,i),Object.defineProperty(s,"prototype",{writable:!1}),s}(e().Component);f.propTypes={onVisible:r.PropTypes.func.isRequired,className:r.PropTypes.string,height:r.PropTypes.oneOfType([r.PropTypes.number,r.PropTypes.string]),placeholder:r.PropTypes.element,threshold:r.PropTypes.number,useIntersectionObserver:r.PropTypes.bool,scrollPosition:r.PropTypes.shape({x:r.PropTypes.number.isRequired,y:r.PropTypes.number.isRequired}),width:r.PropTypes.oneOfType([r.PropTypes.number,r.PropTypes.string])},f.defaultProps={className:"",placeholder:null,threshold:100,useIntersectionObserver:!0};var m=n(296),y=n.n(m),v=n(96),g=n.n(v),b=function(t){var e=getComputedStyle(t,null);return e.getPropertyValue("overflow")+e.getPropertyValue("overflow-y")+e.getPropertyValue("overflow-x")};let x=function(t){if(!(t instanceof HTMLElement))return window;for(var e=t;e&&e instanceof HTMLElement;){if(/(scroll|auto)/.test(b(e)))return e;e=e.parentNode}return window};function w(t){return(w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var P=["delayMethod","delayTime"];function T(){return(T=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(t[r]=i[r])}return t}).apply(this,arguments)}function S(t,e){return(S=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function O(t,e){if(e&&("object"===w(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return E(t)}function E(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function j(t){return(j=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var M=function(){return"undefined"==typeof window?0:window.scrollX||window.pageXOffset},A=function(){return"undefined"==typeof window?0:window.scrollY||window.pageYOffset};let V=function(t){var i=function(i){if("function"!=typeof i&&null!==i)throw TypeError("Super expression must either be null or a function");a.prototype=Object.create(i&&i.prototype,{constructor:{value:a,writable:!0,configurable:!0}}),Object.defineProperty(a,"prototype",{writable:!1}),i&&S(a,i);var r,n,s=(n=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,e=j(a);return t=n?Reflect.construct(e,arguments,j(this).constructor):e.apply(this,arguments),O(this,t)});function a(t){if(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,a),(i=s.call(this,t)).useIntersectionObserver=t.useIntersectionObserver&&o(),i.useIntersectionObserver)return O(i);var i,r=i.onChangeScroll.bind(E(i));return"debounce"===t.delayMethod?i.delayedScroll=y()(r,t.delayTime):"throttle"===t.delayMethod&&(i.delayedScroll=g()(r,t.delayTime)),i.state={scrollPosition:{x:M(),y:A()}},i.baseComponentRef=e().createRef(),i}return r=[{key:"componentDidMount",value:function(){this.addListeners()}},{key:"componentWillUnmount",value:function(){this.removeListeners()}},{key:"componentDidUpdate",value:function(){"undefined"==typeof window||this.useIntersectionObserver||x(this.baseComponentRef.current)!==this.scrollElement&&(this.removeListeners(),this.addListeners())}},{key:"addListeners",value:function(){"undefined"==typeof window||this.useIntersectionObserver||(this.scrollElement=x(this.baseComponentRef.current),this.scrollElement.addEventListener("scroll",this.delayedScroll,{passive:!0}),window.addEventListener("resize",this.delayedScroll,{passive:!0}),this.scrollElement!==window&&window.addEventListener("scroll",this.delayedScroll,{passive:!0}))}},{key:"removeListeners",value:function(){"undefined"==typeof window||this.useIntersectionObserver||(this.scrollElement.removeEventListener("scroll",this.delayedScroll),window.removeEventListener("resize",this.delayedScroll),this.scrollElement!==window&&window.removeEventListener("scroll",this.delayedScroll))}},{key:"onChangeScroll",value:function(){this.useIntersectionObserver||this.setState({scrollPosition:{x:M(),y:A()}})}},{key:"render",value:function(){var i=this.props,r=(i.delayMethod,i.delayTime,function(t,e){if(null==t)return{};var i,r,n=function(t,e){if(null==t)return{};var i,r,n={},s=Object.keys(t);for(r=0;r<s.length;r++)i=s[r],e.indexOf(i)>=0||(n[i]=t[i]);return n}(t,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);for(r=0;r<s.length;r++)i=s[r],e.indexOf(i)>=0||Object.prototype.propertyIsEnumerable.call(t,i)&&(n[i]=t[i])}return n}(i,P)),n=this.useIntersectionObserver?null:this.state.scrollPosition;return e().createElement(t,T({forwardRef:this.baseComponentRef,scrollPosition:n},r))}}],function(t,e){for(var i,r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(i=function(t,e){if("object"!==w(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,"string");if("object"!==w(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(n.key),"symbol"===w(i)?i:String(i)),n)}}(a.prototype,r),Object.defineProperty(a,"prototype",{writable:!1}),a}(e().Component);return i.propTypes={delayMethod:r.PropTypes.oneOf(["debounce","throttle"]),delayTime:r.PropTypes.number,useIntersectionObserver:r.PropTypes.bool},i.defaultProps={delayMethod:"throttle",delayTime:300,useIntersectionObserver:!0},i};function C(t){return(C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function k(t,e){return(k=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function D(t){return(D=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}let R=V(function(t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");s.prototype=Object.create(t&&t.prototype,{constructor:{value:s,writable:!0,configurable:!0}}),Object.defineProperty(s,"prototype",{writable:!1}),t&&k(s,t);var i,r,n=(r=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,e=D(s);t=r?Reflect.construct(e,arguments,D(this).constructor):e.apply(this,arguments);if(t&&("object"===C(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");if(void 0===this)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return this});function s(t){return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,s),n.call(this,t)}return i=[{key:"render",value:function(){return e().createElement(f,this.props)}}],function(t,e){for(var i,r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(i=function(t,e){if("object"!==C(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,"string");if("object"!==C(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(n.key),"symbol"===C(i)?i:String(i)),n)}}(s.prototype,i),Object.defineProperty(s,"prototype",{writable:!1}),s}(e().Component));function L(t){return(L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function B(t,e){return(B=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function F(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function I(t){return(I=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var U=function(t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");s.prototype=Object.create(t&&t.prototype,{constructor:{value:s,writable:!0,configurable:!0}}),Object.defineProperty(s,"prototype",{writable:!1}),t&&B(s,t);var i,r,n=(r=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,e=I(s);t=r?Reflect.construct(e,arguments,I(this).constructor):e.apply(this,arguments);if(t&&("object"===L(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return F(this)});function s(t){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,s),e=n.call(this,t);var e,i=t.afterLoad,r=t.beforeLoad,o=t.scrollPosition,a=t.visibleByDefault;return e.state={visible:a},a&&(r(),i()),e.onVisible=e.onVisible.bind(F(e)),e.isScrollTracked=!!(o&&Number.isFinite(o.x)&&o.x>=0&&Number.isFinite(o.y)&&o.y>=0),e}return i=[{key:"componentDidUpdate",value:function(t,e){e.visible!==this.state.visible&&this.props.afterLoad()}},{key:"onVisible",value:function(){this.props.beforeLoad(),this.setState({visible:!0})}},{key:"render",value:function(){if(this.state.visible)return this.props.children;var t=this.props,i=t.className,r=t.delayMethod,n=t.delayTime,s=t.height,a=t.placeholder,l=t.scrollPosition,u=t.style,h=t.threshold,c=t.useIntersectionObserver,p=t.width;return this.isScrollTracked||c&&o()?e().createElement(f,{className:i,height:s,onVisible:this.onVisible,placeholder:a,scrollPosition:l,style:u,threshold:h,useIntersectionObserver:c,width:p}):e().createElement(R,{className:i,delayMethod:r,delayTime:n,height:s,onVisible:this.onVisible,placeholder:a,style:u,threshold:h,width:p})}}],function(t,e){for(var i,r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(i=function(t,e){if("object"!==L(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,"string");if("object"!==L(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(n.key),"symbol"===L(i)?i:String(i)),n)}}(s.prototype,i),Object.defineProperty(s,"prototype",{writable:!1}),s}(e().Component);U.propTypes={afterLoad:r.PropTypes.func,beforeLoad:r.PropTypes.func,useIntersectionObserver:r.PropTypes.bool,visibleByDefault:r.PropTypes.bool},U.defaultProps={afterLoad:function(){return{}},beforeLoad:function(){return{}},useIntersectionObserver:!0,visibleByDefault:!1};let N=U;function $(t){return($="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var W=["afterLoad","beforeLoad","delayMethod","delayTime","effect","placeholder","placeholderSrc","scrollPosition","threshold","useIntersectionObserver","visibleByDefault","wrapperClassName","wrapperProps"];function _(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,r)}return i}function z(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?_(Object(i),!0).forEach(function(e){var r,n,s;r=t,n=e,s=i[e],(n=H(n))in r?Object.defineProperty(r,n,{value:s,enumerable:!0,configurable:!0,writable:!0}):r[n]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):_(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}function Y(){return(Y=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(t[r]=i[r])}return t}).apply(this,arguments)}function H(t){var e=function(t,e){if("object"!==$(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,"string");if("object"!==$(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"===$(e)?e:String(e)}function X(t,e){return(X=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function q(t){return(q=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var K=function(t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");s.prototype=Object.create(t&&t.prototype,{constructor:{value:s,writable:!0,configurable:!0}}),Object.defineProperty(s,"prototype",{writable:!1}),t&&X(s,t);var i,r,n=(r=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,e=q(s);t=r?Reflect.construct(e,arguments,q(this).constructor):e.apply(this,arguments);if(t&&("object"===$(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");if(void 0===this)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return this});function s(t){var e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,s),(e=n.call(this,t)).state={loaded:!1},e}return i=[{key:"onImageLoad",value:function(){var t=this;return this.state.loaded?null:function(e){t.props.onLoad(e),t.props.afterLoad(),t.setState({loaded:!0})}}},{key:"getImg",value:function(){var t=this.props,i=(t.afterLoad,t.beforeLoad,t.delayMethod,t.delayTime,t.effect,t.placeholder,t.placeholderSrc,t.scrollPosition,t.threshold,t.useIntersectionObserver,t.visibleByDefault,t.wrapperClassName,t.wrapperProps,function(t,e){if(null==t)return{};var i,r,n=function(t,e){if(null==t)return{};var i,r,n={},s=Object.keys(t);for(r=0;r<s.length;r++)i=s[r],e.indexOf(i)>=0||(n[i]=t[i]);return n}(t,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);for(r=0;r<s.length;r++)i=s[r],e.indexOf(i)>=0||Object.prototype.propertyIsEnumerable.call(t,i)&&(n[i]=t[i])}return n}(t,W));return e().createElement("img",Y({},i,{onLoad:this.onImageLoad()}))}},{key:"getLazyLoadImage",value:function(){var t=this.props,i=t.beforeLoad,r=t.className,n=t.delayMethod,s=t.delayTime,o=t.height,a=t.placeholder,l=t.scrollPosition,u=t.style,h=t.threshold,c=t.useIntersectionObserver,p=t.visibleByDefault,d=t.width;return e().createElement(N,{beforeLoad:i,className:r,delayMethod:n,delayTime:s,height:o,placeholder:a,scrollPosition:l,style:u,threshold:h,useIntersectionObserver:c,visibleByDefault:p,width:d},this.getImg())}},{key:"getWrappedLazyLoadImage",value:function(t){var i=this.props,r=i.effect,n=i.height,s=i.placeholderSrc,o=i.width,a=i.wrapperClassName,l=i.wrapperProps,u=this.state.loaded;return e().createElement("span",Y({className:a+" lazy-load-image-background "+r+(u?" lazy-load-image-loaded":""),style:z(z({},u||!s?{}:{backgroundImage:"url(".concat(s,")"),backgroundSize:"100% 100%"}),{},{color:"transparent",display:"inline-block",height:n,width:o})},l),t)}},{key:"render",value:function(){var t=this.props,e=t.effect,i=t.placeholderSrc,r=t.visibleByDefault,n=t.wrapperClassName,s=t.wrapperProps,o=this.getLazyLoadImage();return(e||i)&&!r||n||s?this.getWrappedLazyLoadImage(o):o}}],function(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,H(r.key),r)}}(s.prototype,i),Object.defineProperty(s,"prototype",{writable:!1}),s}(e().Component);K.propTypes={onLoad:r.PropTypes.func,afterLoad:r.PropTypes.func,beforeLoad:r.PropTypes.func,delayMethod:r.PropTypes.string,delayTime:r.PropTypes.number,effect:r.PropTypes.string,placeholderSrc:r.PropTypes.string,threshold:r.PropTypes.number,useIntersectionObserver:r.PropTypes.bool,visibleByDefault:r.PropTypes.bool,wrapperClassName:r.PropTypes.string,wrapperProps:r.PropTypes.object},K.defaultProps={onLoad:function(){},afterLoad:function(){return{}},beforeLoad:function(){return{}},delayMethod:"throttle",delayTime:300,effect:"",placeholderSrc:null,threshold:100,useIntersectionObserver:!0,visibleByDefault:!1,wrapperClassName:""};let G=K})(),t.exports=s})()},32582:(t,e,i)=>{"use strict";i.d(e,{Q:()=>r});let r=(0,i(43210).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},69587:(t,e,i)=>{"use strict";i.d(e,{Cab:()=>u,EcP:()=>l,ao$:()=>s,hFS:()=>o,iYk:()=>n,kkU:()=>a});var r=i(90296);function n(t){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M504 256C504 119 393 8 256 8S8 119 8 256c0 123.78 90.69 226.38 209.25 245V327.69h-63V256h63v-54.64c0-62.15 37-96.48 93.67-96.48 27.14 0 55.52 4.84 55.52 4.84v61h-31.28c-30.8 0-40.41 19.12-40.41 38.73V256h68.78l-11 71.69h-57.78V501C413.31 482.38 504 379.78 504 256z"},child:[]}]})(t)}function s(t){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z"},child:[]}]})(t)}function o(t){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 496 512"},child:[{tag:"path",attr:{d:"M248 8C111 8 0 119 0 256s111 248 248 248 248-111 248-248S385 8 248 8zm121.8 169.9l-40.7 191.8c-3 13.6-11.1 16.9-22.4 10.5l-62-45.7-29.9 28.8c-3.3 3.3-6.1 6.1-12.5 6.1l4.4-63.1 114.9-103.8c5-4.4-1.1-6.9-7.7-2.5l-142 89.4-61.2-19.1c-13.3-4.2-13.6-13.3 2.8-19.7l239.1-92.2c11.1-4 20.8 2.7 17.2 19.5z"},child:[]}]})(t)}function a(t){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M448,209.91a210.06,210.06,0,0,1-122.77-39.25V349.38A162.55,162.55,0,1,1,185,188.31V278.2a74.62,74.62,0,1,0,52.23,71.18V0l88,0a121.18,121.18,0,0,0,1.86,22.17h0A122.18,122.18,0,0,0,381,102.39a121.43,121.43,0,0,0,67,20.14Z"},child:[]}]})(t)}function l(t){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 448 512"},child:[{tag:"path",attr:{d:"M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"},child:[]}]})(t)}function u(t){return(0,r.k5)({tag:"svg",attr:{viewBox:"0 0 512 512"},child:[{tag:"path",attr:{d:"M493.4 24.6l-104-24c-11.3-2.6-22.9 3.3-27.5 13.9l-48 112c-4.2 9.8-1.4 21.3 6.9 28l60.6 49.6c-36 76.7-98.9 140.5-177.2 177.2l-49.6-60.6c-6.8-8.3-18.2-11.1-28-6.9l-112 48C3.9 366.5-2 378.1.6 389.4l24 104C27.1 504.2 36.7 512 48 512c256.1 0 464-207.5 464-464 0-11.2-7.7-20.9-18.6-23.4z"},child:[]}]})(t)}},72789:(t,e,i)=>{"use strict";i.d(e,{M:()=>n});var r=i(43210);function n(t){let e=(0,r.useRef)(null);return null===e.current&&(e.current=t()),e.current}},74479:(t,e,i)=>{"use strict";function r(t){return"object"==typeof t&&null!==t}i.d(e,{G:()=>r})},86044:(t,e,i)=>{"use strict";i.d(e,{xQ:()=>s});var r=i(43210),n=i(21279);function s(t=!0){let e=(0,r.useContext)(n.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,r.useId)();(0,r.useEffect)(()=>{if(t)return a(l)},[t]);let u=(0,r.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,u]:[!0]}},88920:(t,e,i)=>{"use strict";i.d(e,{N:()=>g});var r=i(60687),n=i(43210),s=i(12157),o=i(72789),a=i(15124),l=i(21279),u=i(18171),h=i(32582);class c extends n.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,i=(0,u.s)(t)&&t.offsetWidth||0,r=this.props.sizeRef.current;r.height=e.offsetHeight||0,r.width=e.offsetWidth||0,r.top=e.offsetTop,r.left=e.offsetLeft,r.right=i-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function p({children:t,isPresent:e,anchorX:i}){let s=(0,n.useId)(),o=(0,n.useRef)(null),a=(0,n.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=(0,n.useContext)(h.Q);return(0,n.useInsertionEffect)(()=>{let{width:t,height:r,top:n,left:u,right:h}=a.current;if(e||!o.current||!t||!r)return;let c="left"===i?`left: ${u}`:`right: ${h}`;o.current.dataset.motionPopId=s;let p=document.createElement("style");return l&&(p.nonce=l),document.head.appendChild(p),p.sheet&&p.sheet.insertRule(`
          [data-motion-pop-id="${s}"] {
            position: absolute !important;
            width: ${t}px !important;
            height: ${r}px !important;
            ${c}px !important;
            top: ${n}px !important;
          }
        `),()=>{document.head.contains(p)&&document.head.removeChild(p)}},[e]),(0,r.jsx)(c,{isPresent:e,childRef:o,sizeRef:a,children:n.cloneElement(t,{ref:o})})}let d=({children:t,initial:e,isPresent:i,onExitComplete:s,custom:a,presenceAffectsLayout:u,mode:h,anchorX:c})=>{let d=(0,o.M)(f),m=(0,n.useId)(),y=!0,v=(0,n.useMemo)(()=>(y=!1,{id:m,initial:e,isPresent:i,custom:a,onExitComplete:t=>{for(let e of(d.set(t,!0),d.values()))if(!e)return;s&&s()},register:t=>(d.set(t,!1),()=>d.delete(t))}),[i,d,s]);return u&&y&&(v={...v}),(0,n.useMemo)(()=>{d.forEach((t,e)=>d.set(e,!1))},[i]),n.useEffect(()=>{i||d.size||!s||s()},[i]),"popLayout"===h&&(t=(0,r.jsx)(p,{isPresent:i,anchorX:c,children:t})),(0,r.jsx)(l.t.Provider,{value:v,children:t})};function f(){return new Map}var m=i(86044);let y=t=>t.key||"";function v(t){let e=[];return n.Children.forEach(t,t=>{(0,n.isValidElement)(t)&&e.push(t)}),e}let g=({children:t,custom:e,initial:i=!0,onExitComplete:l,presenceAffectsLayout:u=!0,mode:h="sync",propagate:c=!1,anchorX:p="left"})=>{let[f,g]=(0,m.xQ)(c),b=(0,n.useMemo)(()=>v(t),[t]),x=c&&!f?[]:b.map(y),w=(0,n.useRef)(!0),P=(0,n.useRef)(b),T=(0,o.M)(()=>new Map),[S,O]=(0,n.useState)(b),[E,j]=(0,n.useState)(b);(0,a.E)(()=>{w.current=!1,P.current=b;for(let t=0;t<E.length;t++){let e=y(E[t]);x.includes(e)?T.delete(e):!0!==T.get(e)&&T.set(e,!1)}},[E,x.length,x.join("-")]);let M=[];if(b!==S){let t=[...b];for(let e=0;e<E.length;e++){let i=E[e],r=y(i);x.includes(r)||(t.splice(e,0,i),M.push(i))}return"wait"===h&&M.length&&(t=M),j(v(t)),O(b),null}let{forceRender:A}=(0,n.useContext)(s.L);return(0,r.jsx)(r.Fragment,{children:E.map(t=>{let n=y(t),s=(!c||!!f)&&(b===E||x.includes(n));return(0,r.jsx)(d,{isPresent:s,initial:(!w.current||!!i)&&void 0,custom:e,presenceAffectsLayout:u,mode:h,onExitComplete:s?void 0:()=>{if(!T.has(n))return;T.set(n,!0);let t=!0;T.forEach(e=>{e||(t=!1)}),t&&(A?.(),j(P.current),c&&g?.(),l&&l())},anchorX:p,children:t},n)})})}}};