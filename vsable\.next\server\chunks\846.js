exports.id=846,exports.ids=[846],exports.modules={26331:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},29131:(e,r,t)=>{"use strict";t.d(r,{AuthProvider:()=>s});var a=t(12907);let s=(0,a.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\contexts\\AuthContext.tsx","AuthProvider");(0,a.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\contexts\\AuthContext.tsx","useAuth")},29190:(e,r,t)=>{"use strict";t.d(r,{default:()=>d});var a=t(60687),s=t(43210),o=t(85814),n=t.n(o),i=t(63213),l=t(17019);let d=()=>{let{user:e,logout:r,isAuthenticated:t}=(0,i.A)(),[o,d]=(0,s.useState)(!1),c=async()=>{await r()};return(0,a.jsxs)("nav",{className:"bg-white dark:bg-black border-b border-gray-200 dark:border-gray-800",children:[(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,a.jsxs)(n(),{href:"/",className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-black dark:bg-white rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-sm font-bold text-white dark:text-black",children:"V"})}),(0,a.jsx)("span",{className:"text-xl font-bold text-black dark:text-white",children:"Vsable"})]})}),(0,a.jsxs)("div",{className:"hidden sm:ml-6 sm:flex sm:space-x-8",children:[(0,a.jsx)(n(),{href:"/",className:"border-transparent text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white hover:border-black dark:hover:border-white inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors",children:"Home"}),t&&e?.role==="vendor"&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n(),{href:"/vendor/dashboard",className:"border-transparent text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Dashboard"}),(0,a.jsx)(n(),{href:"/vendor/store",className:"border-transparent text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Store Profile"}),(0,a.jsx)(n(),{href:"/vendor/products",className:"border-transparent text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Products"})]}),t&&e?.role==="admin"&&(0,a.jsx)(n(),{href:"/admin/dashboard",className:"border-transparent text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Admin Dashboard"})]})]}),(0,a.jsx)("div",{className:"hidden sm:ml-6 sm:flex sm:items-center",children:t?(0,a.jsxs)("div",{className:"ml-3 relative flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"text-gray-700 dark:text-gray-300",children:["Hello, ",e?.name]}),(0,a.jsxs)("button",{onClick:c,className:"flex items-center text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white",children:[(0,a.jsx)(l.QeK,{className:"mr-1"}),"Logout"]})]}):(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsxs)(n(),{href:"/login",className:"flex items-center text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white",children:[(0,a.jsx)(l.dUr,{className:"mr-1"}),"Login"]}),(0,a.jsxs)(n(),{href:"/register",className:"flex items-center text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white",children:[(0,a.jsx)(l.vq3,{className:"mr-1"}),"Register"]})]})}),(0,a.jsx)("div",{className:"-mr-2 flex items-center sm:hidden",children:(0,a.jsxs)("button",{onClick:()=>{d(!o)},className:"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500",children:[(0,a.jsx)("span",{className:"sr-only",children:"Open main menu"}),o?(0,a.jsx)(l.yGN,{className:"block h-6 w-6"}):(0,a.jsx)(l.ND1,{className:"block h-6 w-6"})]})})]})}),o&&(0,a.jsxs)("div",{className:"sm:hidden",children:[(0,a.jsxs)("div",{className:"pt-2 pb-3 space-y-1",children:[(0,a.jsx)(n(),{href:"/",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 hover:text-gray-800 dark:hover:text-white",children:"Home"}),t&&e?.role==="vendor"&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n(),{href:"/vendor/dashboard",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 hover:text-gray-800 dark:hover:text-white",children:"Dashboard"}),(0,a.jsx)(n(),{href:"/vendor/store",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 hover:text-gray-800 dark:hover:text-white",children:"Store Profile"}),(0,a.jsx)(n(),{href:"/vendor/products",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 hover:text-gray-800 dark:hover:text-white",children:"Products"})]}),t&&e?.role==="admin"&&(0,a.jsx)(n(),{href:"/admin/dashboard",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 hover:text-gray-800 dark:hover:text-white",children:"Admin Dashboard"})]}),(0,a.jsx)("div",{className:"pt-4 pb-3 border-t border-gray-200 dark:border-gray-700",children:t?(0,a.jsxs)("div",{className:"flex items-center px-4",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center",children:(0,a.jsx)(l.JXP,{className:"h-6 w-6 text-gray-600 dark:text-gray-300"})})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("div",{className:"text-base font-medium text-gray-800 dark:text-white",children:e?.name}),(0,a.jsx)("div",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:e?.email})]}),(0,a.jsx)("button",{onClick:c,className:"ml-auto flex-shrink-0 p-1 rounded-full text-gray-400 hover:text-gray-500 dark:hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:(0,a.jsx)(l.QeK,{className:"h-6 w-6"})})]}):(0,a.jsxs)("div",{className:"flex flex-col space-y-2 px-4",children:[(0,a.jsxs)(n(),{href:"/login",className:"flex items-center text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white",children:[(0,a.jsx)(l.dUr,{className:"mr-2"}),"Login"]}),(0,a.jsxs)(n(),{href:"/register",className:"flex items-center text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white",children:[(0,a.jsx)(l.vq3,{className:"mr-2"}),"Register"]})]})})]})]})}},30004:(e,r,t)=>{"use strict";t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\StartUpJourney\\\\Development\\\\Test_dev\\\\vsable\\\\src\\\\components\\\\Navbar.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\components\\Navbar.tsx","default")},30999:(e,r,t)=>{Promise.resolve().then(t.bind(t,54413))},36947:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},54413:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\StartUpJourney\\\\Development\\\\Test_dev\\\\vsable\\\\src\\\\app\\\\not-found.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\StartUpJourney\\Development\\Test_dev\\vsable\\src\\app\\not-found.tsx","default")},57347:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var a=t(60687),s=t(85814),o=t.n(s),n=t(17019);function i(){return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center px-4",children:(0,a.jsxs)("div",{className:"max-w-lg w-full text-center",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)("h1",{className:"text-9xl font-bold text-black dark:text-white opacity-20",children:"404"})}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Page Not Found"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-400 mb-2",children:"The page you're looking for doesn't exist."}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-500",children:"It might have been moved, deleted, or you entered the wrong URL."})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsxs)(o(),{href:"/",className:"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-colors",children:[(0,a.jsx)(n.V5Y,{className:"mr-2 h-5 w-5"}),"Go Home"]}),(0,a.jsxs)("button",{onClick:()=>window.history.back(),className:"inline-flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-base font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-colors",children:[(0,a.jsx)(n.kRp,{className:"mr-2 h-5 w-5"}),"Go Back"]})]}),(0,a.jsxs)("div",{className:"mt-8 pt-8 border-t border-gray-200 dark:border-gray-700",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-4",children:"Looking for something specific?"}),(0,a.jsxs)(o(),{href:"/",className:"inline-flex items-center text-black dark:text-white hover:text-gray-700 dark:hover:text-gray-300 transition-colors",children:[(0,a.jsx)(n.CKj,{className:"mr-2 h-4 w-4"}),"Browse our marketplace"]})]})]})})}},61135:()=>{},63213:(e,r,t)=>{"use strict";t.d(r,{A:()=>c,AuthProvider:()=>d});var a=t(60687),s=t(43210),o=t(16189),n=t(51060);let i=(0,s.createContext)(void 0),l="http://localhost:5000/api",d=({children:e})=>{let[r,t]=(0,s.useState)(null),[d,c]=(0,s.useState)(!0),[h,x]=(0,s.useState)(null),m=(0,o.useRouter)();n.A.defaults.withCredentials=!0,(0,s.useEffect)(()=>{(async()=>{try{let e=await n.A.get(`${l}/auth/me`);t(e.data.user)}catch{t(null)}finally{c(!1)}})()},[]);let g=async(e,r,a)=>{c(!0),x(null);try{let s=await n.A.post(`${l}/auth/login`,{email:e,password:r,remember_me:a});t(s.data.user),"admin"===s.data.user.role?m.replace("/admin/dashboard"):"vendor"===s.data.user.role?m.replace("/vendor/dashboard"):m.replace("/")}catch(e){throw x(n.A.isAxiosError(e)&&e.response?.data?.message||"Login failed"),e}finally{c(!1)}},u=async()=>{c(!0);try{await n.A.post(`${l}/auth/logout`),t(null),m.replace("/login")}catch(e){x(n.A.isAxiosError(e)&&e.response?.data?.message||"Logout failed")}finally{c(!1)}},v=async(e,r,t,a="customer")=>{c(!0),x(null);try{await n.A.post(`${l}/auth/register`,{name:e,email:r,password:t,role:a}),m.replace("/login")}catch(e){throw x(n.A.isAxiosError(e)&&e.response?.data?.message||"Registration failed"),e}finally{c(!1)}},b=async e=>{c(!0),x(null);try{await n.A.post(`${l}/auth/send-otp`,{email:e})}catch(e){throw x(n.A.isAxiosError(e)&&e.response?.data?.message||"Failed to send OTP"),e}finally{c(!1)}},f=async(e,r)=>{c(!0),x(null);try{return(await n.A.post(`${l}/auth/verify-otp`,{email:e,otp:r})).data.user}catch(e){throw x(n.A.isAxiosError(e)&&e.response?.data?.message||"Invalid OTP"),e}finally{c(!1)}},y=async e=>{c(!0),x(null);try{await n.A.post(`${l}/auth/forgot-password`,{email:e})}catch(e){throw x(n.A.isAxiosError(e)&&e.response?.data?.message||"Failed to send reset email"),e}finally{c(!1)}},p=async(e,r)=>{c(!0),x(null);try{await n.A.post(`${l}/auth/reset-password`,{token:e,password:r})}catch(e){throw x(n.A.isAxiosError(e)&&e.response?.data?.message||"Failed to reset password"),e}finally{c(!1)}},k=async e=>{c(!0),x(null);try{let r=await n.A.put(`${l}/auth/profile`,e);t(r.data.user)}catch(e){throw x(n.A.isAxiosError(e)&&e.response?.data?.message||"Failed to update profile"),e}finally{c(!1)}},j=async e=>{c(!0),x(null);try{await n.A.post(`${l}/auth/send-verification-email`,{email:e})}catch(e){throw x(n.A.isAxiosError(e)&&e.response?.data?.message||"Failed to send verification email"),e}finally{c(!1)}},w=async e=>{c(!0),x(null);try{let r=(await n.A.post(`${l}/auth/verify-email`,{token:e})).data.user;return t(r),r}catch(e){throw x(n.A.isAxiosError(e)&&e.response?.data?.message||"Email verification failed"),e}finally{c(!1)}};return(0,a.jsx)(i.Provider,{value:{user:r,loading:d,error:h,login:g,logout:u,register:v,sendOTP:b,verifyOTP:f,forgotPassword:y,resetPassword:p,updateProfile:k,sendVerificationEmail:j,verifyEmail:w,setUser:t,isAuthenticated:!!r},children:e})},c=()=>{let e=(0,s.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},70440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var a=t(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},84143:(e,r,t)=>{Promise.resolve().then(t.bind(t,57347))},87704:(e,r,t)=>{Promise.resolve().then(t.bind(t,30004)),Promise.resolve().then(t.bind(t,29131))},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h,metadata:()=>c});var a=t(37413),s=t(22376),o=t.n(s),n=t(68726),i=t.n(n);t(61135);var l=t(29131),d=t(30004);let c={title:"Multi-Vendor Platform",description:"A multi-vendor platform with storefront, vendor dashboard, and admin panel"};function h({children:e}){return(0,a.jsx)("html",{lang:"en",children:(0,a.jsx)("body",{className:`${o().variable} ${i().variable} antialiased`,children:(0,a.jsxs)(l.AuthProvider,{children:[(0,a.jsx)(d.default,{}),(0,a.jsx)("main",{className:"min-h-screen",children:e})]})})})}},97432:(e,r,t)=>{Promise.resolve().then(t.bind(t,29190)),Promise.resolve().then(t.bind(t,63213))}};