"use strict";exports.id=95,exports.ids=[95],exports.modules={20769:(e,t,a)=>{a.d(t,{A:()=>o});var r=a(60687),s=a(43210),l=a(16189),i=a(63213);let o=({children:e,allowedRoles:t=[]})=>{let{user:a,loading:o,isAuthenticated:d}=(0,i.A)(),n=(0,l.useRouter)();return((0,s.useEffect)(()=>{o||d?!o&&d&&t.length>0&&a&&!t.includes(a.role)&&("admin"===a.role?n.replace("/admin/dashboard"):"vendor"===a.role?n.replace("/vendor/dashboard"):n.replace("/")):n.replace("/login")},[o,d,a,n,t]),o)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):!d||t.length>0&&a&&!t.includes(a.role)?null:(0,r.jsx)(r.Fragment,{children:e})}},99623:(e,t,a)=>{a.d(t,{A:()=>y});var r=a(60687),s=a(43210),l=a(51060),i=a(30474),o=a(17019),d=a(65675),n=a(44056),c=a(49130),m=a(49480),g=a(56070),h=a(25834);let p=[{id:1,name:"Premium Coffee Beans",description:"Freshly roasted coffee beans from the mountains",price:24.99,image_url:"https://images.unsplash.com/photo-1559056199-641a0ac8b55e",imageUrl:"https://images.unsplash.com/photo-1559056199-641a0ac8b55e",category:"Beverages",tags:["coffee","premium","organic"],is_active:!0,store_id:1,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),contact_link:null},{id:2,name:"Artisan Chocolate",description:"Handcrafted chocolate made with finest ingredients",price:18.5,image_url:"https://images.unsplash.com/photo-1549007994-cb92caebd54b",imageUrl:"https://images.unsplash.com/photo-1549007994-cb92caebd54b",category:"Sweets",tags:["chocolate","artisan","handmade"],is_active:!0,store_id:1,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),contact_link:null},{id:3,name:"Organic Honey",description:"Pure organic honey from local beekeepers",price:12.99,image_url:"https://images.unsplash.com/photo-1587049352846-4a222e784d38",imageUrl:"https://images.unsplash.com/photo-1587049352846-4a222e784d38",category:"Natural",tags:["honey","organic","natural"],is_active:!0,store_id:1,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),contact_link:null}],u={name:"Demo Store",description:"This is how your store will look with this template",logo_url:"https://images.unsplash.com/photo-1560472354-b33ff0c44a43",cover_image_url:"https://images.unsplash.com/photo-1441986300917-64674bd600d8",telegram:"@demostore",whatsapp:"+1234567890",phone:"+****************",instagram:"@demostore",view_count:1234},x=({templateId:e,store:t,products:a=p,onClose:l,onSelect:i,isSelected:x=!1})=>{let[b,y]=(0,s.useState)(!1),f={...u,...t,name:t.name||u.name,description:t.description||u.description,logo_url:t.logo_url||u.logo_url,cover_image_url:t.cover_image_url||u.cover_image_url},v=()=>{let t=["All",...Array.from(new Set(a.map(e=>e.category).filter(e=>"string"==typeof e&&""!==e.trim())))],s={store:f,products:a,searchQuery:"",selectedCategory:"All",handleContactClick:()=>{},onSearchQueryChange:()=>{},onSelectedCategoryChange:()=>{},categories:t,isPreview:!0};switch(e){case"default":default:return(0,r.jsx)(d.A,{...s});case"template1":return(0,r.jsx)(n.A,{...s});case"template2":return(0,r.jsx)(c.A,{...s});case"template3":return(0,r.jsx)(m.A,{...s});case"template4":return(0,r.jsx)(g.A,{...s});case"template5":return(0,r.jsx)(h.A,{...s})}};return b?(0,r.jsxs)("div",{className:"fixed inset-0 z-50 bg-white dark:bg-gray-900",children:[(0,r.jsx)("div",{className:"absolute top-4 right-4 z-10 flex space-x-2",children:(0,r.jsx)("button",{onClick:()=>y(!1),className:"p-2 bg-black dark:bg-white text-white dark:text-black rounded-full hover:bg-gray-800 dark:hover:bg-gray-200 transition-colors",children:(0,r.jsx)(o.yGN,{className:"h-5 w-5"})})}),(0,r.jsx)("div",{className:"h-full overflow-auto",children:v()})]}):(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden",children:[(0,r.jsxs)("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:{default:"Classic Template",template1:"Modern Blue",template2:"Elegant Cyan",template3:"Professional Teal",template4:"Vibrant Purple",template5:"Minimal Slate"}[e]||"Unknown Template"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Preview how your store will look"})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>y(!0),className:"p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors",title:"Fullscreen preview",children:(0,r.jsx)(o.HaR,{className:"h-5 w-5"})}),(0,r.jsx)("button",{onClick:l,className:"p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors",children:(0,r.jsx)(o.yGN,{className:"h-5 w-5"})})]})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"h-96 overflow-hidden",children:(0,r.jsx)("div",{className:"transform scale-50 origin-top-left w-[200%] h-[200%]",children:v()})}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-all flex items-center justify-center opacity-0 hover:opacity-100",children:(0,r.jsxs)("button",{onClick:()=>y(!0),className:"bg-white dark:bg-gray-800 text-gray-900 dark:text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,r.jsx)(o.Vap,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Full Preview"})]})})]}),(0,r.jsx)("div",{className:"p-4 border-t border-gray-200 dark:border-gray-700",children:(0,r.jsx)("button",{onClick:()=>i(e),className:`w-full py-2 px-4 rounded-md font-medium transition-colors ${x?"bg-black dark:bg-white text-white dark:text-black":"bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:x?"Selected":"Select Template"})})]})},b=[{id:"template1",name:"Classic Clean",description:"A simple and elegant layout."},{id:"template2",name:"Modern Dark",description:"A sleek, contemporary dark theme."},{id:"template3",name:"Vibrant Grid",description:"A colorful, grid-based product display."},{id:"template4",name:"Minimalist Focus",description:"Emphasizes product imagery with minimal distractions."},{id:"template5",name:"Interactive Showcase",description:"Engaging animations and interactive elements."}],y=({currentStore:e,onTemplateUpdate:t})=>{let[a,d]=(0,s.useState)(e?.selected_template_id),[n,c]=(0,s.useState)(!1),[m,g]=(0,s.useState)(null),[h,p]=(0,s.useState)(null),[u,y]=(0,s.useState)(null);(0,s.useEffect)(()=>{d(e?.selected_template_id||"template1")},[e]);let f=e=>{d(e)},v=async()=>{if(!a)return void g("Please select a template.");if(!e)return void g("Store data is not available.");c(!0),g(null),p(null);try{let e=await l.A.put("http://localhost:5000/api/vendor/store/template",{selected_template_id:a},{withCredentials:!0});e.data&&e.data.store?(t(e.data.store),p(e.data.message||"Template updated successfully!")):g(e.data.message||"Failed to update template. Invalid response.")}catch(e){console.error("Error updating template:",e),g(l.A.isAxiosError(e)&&e.response?.data?.message||"An unknown error occurred.")}finally{c(!1)}};return(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 shadow sm:rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-white",children:"Store Appearance"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-600 dark:text-gray-400",children:"Choose a template that best represents your brand."}),m&&(0,r.jsx)("p",{className:"mt-2 text-sm text-red-600 dark:text-red-400",children:m}),h&&(0,r.jsx)("p",{className:"mt-2 text-sm text-green-600 dark:text-green-400",children:h}),(0,r.jsx)("div",{className:"mt-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:b.map(e=>(0,r.jsxs)("div",{className:`rounded-lg border p-4 transition-all
                        ${a===e.id?"border-black dark:border-white ring-2 ring-black dark:ring-white bg-gray-50 dark:bg-gray-900":"border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"}`,children:[e.previewImageUrl&&(0,r.jsx)("div",{className:"w-full h-32 bg-gray-200 dark:bg-gray-700 rounded-md mb-3 overflow-hidden",children:(0,r.jsx)(i.default,{src:e.previewImageUrl||"",alt:e.name,width:300,height:150,className:"w-full h-full object-cover"})}),(0,r.jsx)("h4",{className:"text-md font-semibold text-gray-800 dark:text-gray-100",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-3",children:e.description}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("button",{onClick:()=>y(e.id),className:"flex-1 flex items-center justify-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,r.jsx)(o.Vap,{className:"mr-2 h-4 w-4"}),"Preview"]}),(0,r.jsx)("button",{onClick:()=>f(e.id),className:`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${a===e.id?"bg-black dark:bg-white text-white dark:text-black":"bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:a===e.id?"Selected":"Select"})]})]},e.id))}),(0,r.jsx)("div",{className:"mt-8 flex justify-end",children:(0,r.jsx)("button",{type:"button",onClick:v,disabled:n||!a||a===e?.selected_template_id,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:n?"Saving...":"Save Template"})}),u&&e&&(0,r.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,r.jsxs)("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:()=>y(null)}),(0,r.jsx)("span",{className:"hidden sm:inline-block sm:align-middle sm:h-screen",children:"​"}),(0,r.jsx)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full",children:(0,r.jsx)(x,{templateId:u,store:e,onClose:()=>y(null),onSelect:e=>{f(e),y(null)},isSelected:a===u})})]})})]})}}};