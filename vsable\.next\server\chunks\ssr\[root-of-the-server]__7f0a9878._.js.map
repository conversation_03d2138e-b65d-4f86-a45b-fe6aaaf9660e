{"version": 3, "sources": [], "sections": [{"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/StartUpJourney/Development/Test_dev/vsable/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { useRouter } from 'next/navigation';\nimport axios from 'axios';\n\n// Define types\nexport interface User {\n  id: number;\n  email: string;\n  name: string;\n  role: 'customer' | 'vendor' | 'admin';\n  created_at: string;\n  last_login: string | null;\n  email_verified: boolean;\n  profile_picture: string | null;\n  google_id: boolean;\n}\n\ninterface AuthContextType {\n  user: User | null;\n  loading: boolean;\n  error: string | null;\n  login: (email: string, password: string, rememberMe: boolean) => Promise<void>;\n  logout: () => Promise<void>;\n  register: (name: string, email: string, password: string, role?: string) => Promise<void>;\n  sendOTP: (email: string) => Promise<void>;\n  verifyOTP: (email: string, otp: string) => Promise<User>;\n  forgotPassword: (email: string) => Promise<void>;\n  resetPassword: (token: string, password: string) => Promise<void>;\n  updateProfile: (data: Partial<User>) => Promise<void>;\n  sendVerificationEmail: (email: string) => Promise<void>;\n  verifyEmail: (token: string) => Promise<User>;\n  setUser: (user: User | null) => void;\n  isAuthenticated: boolean;\n}\n\n// Create context\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\n// API base URL\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\n// Provider component\nexport const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  const router = useRouter();\n\n  // Configure axios to include credentials\n  axios.defaults.withCredentials = true;\n\n  // Check if user is logged in\n  useEffect(() => {\n    const checkAuth = async () => {\n      try {\n        const response = await axios.get(`${API_URL}/auth/me`);\n        setUser(response.data.user);\n      } catch {\n        setUser(null);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    checkAuth();\n  }, []);\n\n  // Login function\n  const login = async (email: string, password: string, rememberMe: boolean) => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      const response = await axios.post(`${API_URL}/auth/login`, {\n        email,\n        password,\n        remember_me: rememberMe\n      });\n\n      setUser(response.data.user);\n\n      // Redirect based on user role\n      if (response.data.user.role === 'admin') {\n        router.replace('/admin/dashboard');\n      } else if (response.data.user.role === 'vendor') {\n        router.replace('/vendor/dashboard');\n      } else {\n        router.replace('/');\n      }\n    } catch (error) {\n      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'Login failed' : 'Login failed');\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Logout function\n  const logout = async () => {\n    setLoading(true);\n\n    try {\n      await axios.post(`${API_URL}/auth/logout`);\n      setUser(null);\n      router.replace('/login');\n    } catch (error) {\n      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'Logout failed' : 'Logout failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Register function\n  const register = async (name: string, email: string, password: string, role: string = 'customer') => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      await axios.post(`${API_URL}/auth/register`, {\n        name,\n        email,\n        password,\n        role\n      });\n\n      // Redirect to login page after successful registration\n      router.replace('/login');\n    } catch (error) {\n      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'Registration failed' : 'Registration failed');\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Send OTP function\n  const sendOTP = async (email: string) => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      await axios.post(`${API_URL}/auth/send-otp`, { email });\n    } catch (error) {\n      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'Failed to send OTP' : 'Failed to send OTP');\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Verify OTP function\n  const verifyOTP = async (email: string, otp: string): Promise<User> => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      const response = await axios.post(`${API_URL}/auth/verify-otp`, { email, otp });\n      return response.data.user;\n    } catch (error) {\n      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'Invalid OTP' : 'Invalid OTP');\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Forgot password function\n  const forgotPassword = async (email: string) => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      await axios.post(`${API_URL}/auth/forgot-password`, { email });\n    } catch (error) {\n      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'Failed to send reset email' : 'Failed to send reset email');\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Reset password function\n  const resetPassword = async (token: string, password: string) => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      await axios.post(`${API_URL}/auth/reset-password`, { token, password });\n    } catch (error) {\n      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'Failed to reset password' : 'Failed to reset password');\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Update profile function\n  const updateProfile = async (data: Partial<User>) => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      const response = await axios.put(`${API_URL}/auth/profile`, data);\n      setUser(response.data.user);\n    } catch (error) {\n      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'Failed to update profile' : 'Failed to update profile');\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Send verification email function\n  const sendVerificationEmail = async (email: string) => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      await axios.post(`${API_URL}/auth/send-verification-email`, { email });\n    } catch (error) {\n      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'Failed to send verification email' : 'Failed to send verification email');\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Verify email function\n  const verifyEmail = async (token: string): Promise<User> => {\n    setLoading(true);\n    setError(null);\n\n    try {\n      const response = await axios.post(`${API_URL}/auth/verify-email`, { token });\n      const verifiedUser = response.data.user;\n      setUser(verifiedUser);\n      return verifiedUser;\n    } catch (error) {\n      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'Email verification failed' : 'Email verification failed');\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const value = {\n    user,\n    loading,\n    error,\n    login,\n    logout,\n    register,\n    sendOTP,\n    verifyOTP,\n    forgotPassword,\n    resetPassword,\n    updateProfile,\n    sendVerificationEmail,\n    verifyEmail,\n    setUser,\n    isAuthenticated: !!user\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n\n// Custom hook to use auth context\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAqCA,iBAAiB;AACjB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAE/D,eAAe;AACf,MAAM,UAAU,iEAAmC;AAG5C,MAAM,eAAkD,CAAC,EAAE,QAAQ,EAAE;IAC1E,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,yCAAyC;IACzC,qIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,eAAe,GAAG;IAEjC,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,QAAQ,CAAC;gBACrD,QAAQ,SAAS,IAAI,CAAC,IAAI;YAC5B,EAAE,OAAM;gBACN,QAAQ;YACV,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,iBAAiB;IACjB,MAAM,QAAQ,OAAO,OAAe,UAAkB;QACpD,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,WAAW,CAAC,EAAE;gBACzD;gBACA;gBACA,aAAa;YACf;YAEA,QAAQ,SAAS,IAAI,CAAC,IAAI;YAE1B,8BAA8B;YAC9B,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,SAAS;gBACvC,OAAO,OAAO,CAAC;YACjB,OAAO,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,UAAU;gBAC/C,OAAO,OAAO,CAAC;YACjB,OAAO;gBACL,OAAO,OAAO,CAAC;YACjB;QACF,EAAE,OAAO,OAAO;YACd,SAAS,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW,iBAAiB;YACvF,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,kBAAkB;IAClB,MAAM,SAAS;QACb,WAAW;QAEX,IAAI;YACF,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,YAAY,CAAC;YACzC,QAAQ;YACR,OAAO,OAAO,CAAC;QACjB,EAAE,OAAO,OAAO;YACd,SAAS,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW,kBAAkB;QAC1F,SAAU;YACR,WAAW;QACb;IACF;IAEA,oBAAoB;IACpB,MAAM,WAAW,OAAO,MAAc,OAAe,UAAkB,OAAe,UAAU;QAC9F,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,cAAc,CAAC,EAAE;gBAC3C;gBACA;gBACA;gBACA;YACF;YAEA,uDAAuD;YACvD,OAAO,OAAO,CAAC;QACjB,EAAE,OAAO,OAAO;YACd,SAAS,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW,wBAAwB;YAC9F,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,oBAAoB;IACpB,MAAM,UAAU,OAAO;QACrB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,cAAc,CAAC,EAAE;gBAAE;YAAM;QACvD,EAAE,OAAO,OAAO;YACd,SAAS,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW,uBAAuB;YAC7F,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,sBAAsB;IACtB,MAAM,YAAY,OAAO,OAAe;QACtC,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,gBAAgB,CAAC,EAAE;gBAAE;gBAAO;YAAI;YAC7E,OAAO,SAAS,IAAI,CAAC,IAAI;QAC3B,EAAE,OAAO,OAAO;YACd,SAAS,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW,gBAAgB;YACtF,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,2BAA2B;IAC3B,MAAM,iBAAiB,OAAO;QAC5B,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,qBAAqB,CAAC,EAAE;gBAAE;YAAM;QAC9D,EAAE,OAAO,OAAO;YACd,SAAS,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW,+BAA+B;YACrG,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,OAAO,OAAe;QAC1C,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,oBAAoB,CAAC,EAAE;gBAAE;gBAAO;YAAS;QACvE,EAAE,OAAO,OAAO;YACd,SAAS,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW,6BAA6B;YACnG,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,OAAO;QAC3B,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,aAAa,CAAC,EAAE;YAC5D,QAAQ,SAAS,IAAI,CAAC,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,SAAS,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW,6BAA6B;YACnG,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,mCAAmC;IACnC,MAAM,wBAAwB,OAAO;QACnC,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,6BAA6B,CAAC,EAAE;gBAAE;YAAM;QACtE,EAAE,OAAO,OAAO;YACd,SAAS,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW,sCAAsC;YAC5G,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,wBAAwB;IACxB,MAAM,cAAc,OAAO;QACzB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,GAAG,QAAQ,kBAAkB,CAAC,EAAE;gBAAE;YAAM;YAC1E,MAAM,eAAe,SAAS,IAAI,CAAC,IAAI;YACvC,QAAQ;YACR,OAAO;QACT,EAAE,OAAO,OAAO;YACd,SAAS,qIAAA,CAAA,UAAK,CAAC,YAAY,CAAC,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW,8BAA8B;YACpG,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,iBAAiB,CAAC,CAAC;IACrB;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;AAGO,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAE3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/StartUpJourney/Development/Test_dev/vsable/src/components/Navbar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { FiMenu, FiX, FiUser, FiLogOut, FiLogIn, FiUserPlus } from 'react-icons/fi';\n\nconst Navbar = () => {\n  const { user, logout, isAuthenticated } = useAuth();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen);\n  };\n\n  const handleLogout = async () => {\n    await logout();\n  };\n\n  return (\n    <nav className=\"bg-white dark:bg-black border-b border-gray-200 dark:border-gray-800\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0 flex items-center\">\n              <Link href=\"/\" className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-black dark:bg-white rounded-full flex items-center justify-center\">\n                  <span className=\"text-sm font-bold text-white dark:text-black\">V</span>\n                </div>\n                <span className=\"text-xl font-bold text-black dark:text-white\">Vsable</span>\n              </Link>\n            </div>\n            <div className=\"hidden sm:ml-6 sm:flex sm:space-x-8\">\n              <Link href=\"/\" className=\"border-transparent text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white hover:border-black dark:hover:border-white inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors\">\n                Home\n              </Link>\n              {isAuthenticated && user?.role === 'vendor' && (\n                <>\n                  <Link href=\"/vendor/dashboard\" className=\"border-transparent text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\">\n                    Dashboard\n                  </Link>\n                  <Link href=\"/vendor/store\" className=\"border-transparent text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\">\n                    Store Profile\n                  </Link>\n                  <Link href=\"/vendor/products\" className=\"border-transparent text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\">\n                    Products\n                  </Link>\n                </>\n              )}\n              {isAuthenticated && user?.role === 'admin' && (\n                <Link href=\"/admin/dashboard\" className=\"border-transparent text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium\">\n                  Admin Dashboard\n                </Link>\n              )}\n            </div>\n          </div>\n          <div className=\"hidden sm:ml-6 sm:flex sm:items-center\">\n            {isAuthenticated ? (\n              <div className=\"ml-3 relative flex items-center space-x-4\">\n                <span className=\"text-gray-700 dark:text-gray-300\">\n                  Hello, {user?.name}\n                </span>\n                <button\n                  onClick={handleLogout}\n                  className=\"flex items-center text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white\"\n                >\n                  <FiLogOut className=\"mr-1\" />\n                  Logout\n                </button>\n              </div>\n            ) : (\n              <div className=\"flex space-x-4\">\n                <Link href=\"/login\" className=\"flex items-center text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white\">\n                  <FiLogIn className=\"mr-1\" />\n                  Login\n                </Link>\n                <Link href=\"/register\" className=\"flex items-center text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white\">\n                  <FiUserPlus className=\"mr-1\" />\n                  Register\n                </Link>\n              </div>\n            )}\n          </div>\n          <div className=\"-mr-2 flex items-center sm:hidden\">\n            <button\n              onClick={toggleMenu}\n              className=\"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\"\n            >\n              <span className=\"sr-only\">Open main menu</span>\n              {isMenuOpen ? <FiX className=\"block h-6 w-6\" /> : <FiMenu className=\"block h-6 w-6\" />}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile menu */}\n      {isMenuOpen && (\n        <div className=\"sm:hidden\">\n          <div className=\"pt-2 pb-3 space-y-1\">\n            <Link href=\"/\" className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 hover:text-gray-800 dark:hover:text-white\">\n              Home\n            </Link>\n            {isAuthenticated && user?.role === 'vendor' && (\n              <>\n                <Link href=\"/vendor/dashboard\" className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 hover:text-gray-800 dark:hover:text-white\">\n                  Dashboard\n                </Link>\n                <Link href=\"/vendor/store\" className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 hover:text-gray-800 dark:hover:text-white\">\n                  Store Profile\n                </Link>\n                <Link href=\"/vendor/products\" className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 hover:text-gray-800 dark:hover:text-white\">\n                  Products\n                </Link>\n              </>\n            )}\n            {isAuthenticated && user?.role === 'admin' && (\n              <Link href=\"/admin/dashboard\" className=\"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 hover:text-gray-800 dark:hover:text-white\">\n                Admin Dashboard\n              </Link>\n            )}\n          </div>\n          <div className=\"pt-4 pb-3 border-t border-gray-200 dark:border-gray-700\">\n            {isAuthenticated ? (\n              <div className=\"flex items-center px-4\">\n                <div className=\"flex-shrink-0\">\n                  <div className=\"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center\">\n                    <FiUser className=\"h-6 w-6 text-gray-600 dark:text-gray-300\" />\n                  </div>\n                </div>\n                <div className=\"ml-3\">\n                  <div className=\"text-base font-medium text-gray-800 dark:text-white\">{user?.name}</div>\n                  <div className=\"text-sm font-medium text-gray-500 dark:text-gray-400\">{user?.email}</div>\n                </div>\n                <button\n                  onClick={handleLogout}\n                  className=\"ml-auto flex-shrink-0 p-1 rounded-full text-gray-400 hover:text-gray-500 dark:hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n                >\n                  <FiLogOut className=\"h-6 w-6\" />\n                </button>\n              </div>\n            ) : (\n              <div className=\"flex flex-col space-y-2 px-4\">\n                <Link href=\"/login\" className=\"flex items-center text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white\">\n                  <FiLogIn className=\"mr-2\" />\n                  Login\n                </Link>\n                <Link href=\"/register\" className=\"flex items-center text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white\">\n                  <FiUserPlus className=\"mr-2\" />\n                  Register\n                </Link>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,MAAM,SAAS;IACb,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,aAAa;QACjB,cAAc,CAAC;IACjB;IAEA,MAAM,eAAe;QACnB,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA+C;;;;;;;;;;;0DAEjE,8OAAC;gDAAK,WAAU;0DAA+C;;;;;;;;;;;;;;;;;8CAGnE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDAA4N;;;;;;wCAGpP,mBAAmB,MAAM,SAAS,0BACjC;;8DACE,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAoB,WAAU;8DAAwL;;;;;;8DAGjO,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAgB,WAAU;8DAAwL;;;;;;8DAG7N,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAmB,WAAU;8DAAwL;;;;;;;;wCAKnO,mBAAmB,MAAM,SAAS,yBACjC,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAmB,WAAU;sDAAwL;;;;;;;;;;;;;;;;;;sCAMtO,8OAAC;4BAAI,WAAU;sCACZ,gCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAAmC;4CACzC,MAAM;;;;;;;kDAEhB,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,8IAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAS;;;;;;;;;;;;qDAKjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;;0DAC5B,8OAAC,8IAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAS;;;;;;;kDAG9B,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;;0DAC/B,8OAAC,8IAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAS;;;;;;;;;;;;;;;;;;sCAMvC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAU;;;;;;oCACzB,2BAAa,8OAAC,8IAAA,CAAA,MAAG;wCAAC,WAAU;;;;;6DAAqB,8OAAC,8IAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO3E,4BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CAAoN;;;;;;4BAG5O,mBAAmB,MAAM,SAAS,0BACjC;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAoB,WAAU;kDAAoN;;;;;;kDAG7P,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAgB,WAAU;kDAAoN;;;;;;kDAGzP,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAmB,WAAU;kDAAoN;;;;;;;;4BAK/P,mBAAmB,MAAM,SAAS,yBACjC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAmB,WAAU;0CAAoN;;;;;;;;;;;;kCAKhQ,8OAAC;wBAAI,WAAU;kCACZ,gCACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8IAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAGtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAuD,MAAM;;;;;;sDAC5E,8OAAC;4CAAI,WAAU;sDAAwD,MAAM;;;;;;;;;;;;8CAE/E,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,8IAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;iDAIxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;;sDAC5B,8OAAC,8IAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAS;;;;;;;8CAG9B,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;;sDAC/B,8OAAC,8IAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjD;uCAEe", "debugId": null}}]}