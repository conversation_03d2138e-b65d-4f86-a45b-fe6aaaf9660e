(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[108],{760:(t,e,i)=>{"use strict";i.d(e,{N:()=>g});var n=i(5155),r=i(2115),s=i(869),o=i(2885),a=i(7494),l=i(845),u=i(7351),h=i(1508);class c extends r.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,i=(0,u.s)(t)&&t.offsetWidth||0,n=this.props.sizeRef.current;n.height=e.offsetHeight||0,n.width=e.offsetWidth||0,n.top=e.offsetTop,n.left=e.offsetLeft,n.right=i-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function p(t){let{children:e,isPresent:i,anchorX:s}=t,o=(0,r.useId)(),a=(0,r.useRef)(null),l=(0,r.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:u}=(0,r.useContext)(h.Q);return(0,r.useInsertionEffect)(()=>{let{width:t,height:e,top:n,left:r,right:h}=l.current;if(i||!a.current||!t||!e)return;a.current.dataset.motionPopId=o;let c=document.createElement("style");return u&&(c.nonce=u),document.head.appendChild(c),c.sheet&&c.sheet.insertRule('\n          [data-motion-pop-id="'.concat(o,'"] {\n            position: absolute !important;\n            width: ').concat(t,"px !important;\n            height: ").concat(e,"px !important;\n            ").concat("left"===s?"left: ".concat(r):"right: ".concat(h),"px !important;\n            top: ").concat(n,"px !important;\n          }\n        ")),()=>{document.head.contains(c)&&document.head.removeChild(c)}},[i]),(0,n.jsx)(c,{isPresent:i,childRef:a,sizeRef:l,children:r.cloneElement(e,{ref:a})})}let d=t=>{let{children:e,initial:i,isPresent:s,onExitComplete:a,custom:u,presenceAffectsLayout:h,mode:c,anchorX:d}=t,m=(0,o.M)(f),y=(0,r.useId)(),v=!0,g=(0,r.useMemo)(()=>(v=!1,{id:y,initial:i,isPresent:s,custom:u,onExitComplete:t=>{for(let e of(m.set(t,!0),m.values()))if(!e)return;a&&a()},register:t=>(m.set(t,!1),()=>m.delete(t))}),[s,m,a]);return h&&v&&(g={...g}),(0,r.useMemo)(()=>{m.forEach((t,e)=>m.set(e,!1))},[s]),r.useEffect(()=>{s||m.size||!a||a()},[s]),"popLayout"===c&&(e=(0,n.jsx)(p,{isPresent:s,anchorX:d,children:e})),(0,n.jsx)(l.t.Provider,{value:g,children:e})};function f(){return new Map}var m=i(2082);let y=t=>t.key||"";function v(t){let e=[];return r.Children.forEach(t,t=>{(0,r.isValidElement)(t)&&e.push(t)}),e}let g=t=>{let{children:e,custom:i,initial:l=!0,onExitComplete:u,presenceAffectsLayout:h=!0,mode:c="sync",propagate:p=!1,anchorX:f="left"}=t,[g,b]=(0,m.xQ)(p),x=(0,r.useMemo)(()=>v(e),[e]),w=p&&!g?[]:x.map(y),P=(0,r.useRef)(!0),T=(0,r.useRef)(x),S=(0,o.M)(()=>new Map),[O,E]=(0,r.useState)(x),[j,M]=(0,r.useState)(x);(0,a.E)(()=>{P.current=!1,T.current=x;for(let t=0;t<j.length;t++){let e=y(j[t]);w.includes(e)?S.delete(e):!0!==S.get(e)&&S.set(e,!1)}},[j,w.length,w.join("-")]);let A=[];if(x!==O){let t=[...x];for(let e=0;e<j.length;e++){let i=j[e],n=y(i);w.includes(n)||(t.splice(e,0,i),A.push(i))}return"wait"===c&&A.length&&(t=A),M(v(t)),E(x),null}let{forceRender:V}=(0,r.useContext)(s.L);return(0,n.jsx)(n.Fragment,{children:j.map(t=>{let e=y(t),r=(!p||!!g)&&(x===j||w.includes(e));return(0,n.jsx)(d,{isPresent:r,initial:(!P.current||!!l)&&void 0,custom:i,presenceAffectsLayout:h,mode:c,onExitComplete:r?void 0:()=>{if(!S.has(e))return;S.set(e,!0);let t=!0;S.forEach(e=>{e||(t=!1)}),t&&(null==V||V(),M(T.current),p&&(null==b||b()),u&&u())},anchorX:f,children:t},e)})})}},845:(t,e,i)=>{"use strict";i.d(e,{t:()=>n});let n=(0,i(2115).createContext)(null)},869:(t,e,i)=>{"use strict";i.d(e,{L:()=>n});let n=(0,i(2115).createContext)({})},1508:(t,e,i)=>{"use strict";i.d(e,{Q:()=>n});let n=(0,i(2115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},2082:(t,e,i)=>{"use strict";i.d(e,{xQ:()=>s});var n=i(2115),r=i(845);function s(t=!0){let e=(0,n.useContext)(r.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,n.useId)();(0,n.useEffect)(()=>{if(t)return a(l)},[t]);let u=(0,n.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,u]:[!0]}},2885:(t,e,i)=>{"use strict";i.d(e,{M:()=>r});var n=i(2115);function r(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}},5695:(t,e,i)=>{"use strict";var n=i(8999);i.o(n,"useParams")&&i.d(e,{useParams:function(){return n.useParams}}),i.o(n,"useRouter")&&i.d(e,{useRouter:function(){return n.useRouter}}),i.o(n,"useSearchParams")&&i.d(e,{useSearchParams:function(){return n.useSearchParams}})},6408:(t,e,i)=>{"use strict";let n;function r(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function s(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function o(t,e,i,n){if("function"==typeof e){let[r,o]=s(n);e=e(void 0!==i?i:t.custom,r,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,o]=s(n);e=e(void 0!==i?i:t.custom,r,o)}return e}function a(t,e,i){let n=t.getProps();return o(n,e,void 0!==i?i:n.custom,t)}function l(t,e){return t?.[e]??t?.default??t}i.d(e,{P:()=>sj});let u=t=>t,h={},c=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],p={value:null,addProjectionMetrics:null};function d(t,e){let i=!1,n=!0,r={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,o=c.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,r=!1,s=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(h.schedule(e),t()),l++,e(a)}let h={schedule:(t,e=!1,s=!1)=>{let a=s&&r?i:n;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),o.delete(t)},process:t=>{if(a=t,r){s=!0;return}r=!0,[i,n]=[n,i],i.forEach(u),e&&p.value&&p.value.frameloop[e].push(l),l=0,i.clear(),r=!1,s&&(s=!1,h.process(t))}};return h}(s,e?i:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:d,update:f,preRender:m,render:y,postRender:v}=o,g=()=>{let s=h.useManualTiming?r.timestamp:performance.now();i=!1,h.useManualTiming||(r.delta=n?1e3/60:Math.max(Math.min(s-r.timestamp,40),1)),r.timestamp=s,r.isProcessing=!0,a.process(r),l.process(r),u.process(r),d.process(r),f.process(r),m.process(r),y.process(r),v.process(r),r.isProcessing=!1,i&&e&&(n=!1,t(g))},b=()=>{i=!0,n=!0,r.isProcessing||t(g)};return{schedule:c.reduce((t,e)=>{let n=o[e];return t[e]=(t,e=!1,r=!1)=>(i||b(),n.schedule(t,e,r)),t},{}),cancel:t=>{for(let e=0;e<c.length;e++)o[c[e]].cancel(t)},state:r,steps:o}}let{schedule:f,cancel:m,state:y,steps:v}=d("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),g=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],b=new Set(g),x=new Set(["width","height","top","left","right","bottom",...g]);function w(t,e){-1===t.indexOf(e)&&t.push(e)}function P(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class T{constructor(){this.subscriptions=[]}add(t){return w(this.subscriptions,t),()=>P(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function S(){n=void 0}let O={now:()=>(void 0===n&&O.set(y.isProcessing||h.useManualTiming?y.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(S)}},E=t=>!isNaN(parseFloat(t)),j={current:void 0};class M{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=O.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=O.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=E(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new T);let i=this.events[t].add(e);return"change"===t?()=>{i(),f.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return j.current&&j.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=O.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function A(t,e){return new M(t,e)}let V=t=>Array.isArray(t),D=t=>!!(t&&t.getVelocity);function C(t,e){let i=t.getValue("willChange");if(D(i)&&i.add)return i.add(e);if(!i&&h.WillChange){let i=new h.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let k=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),R="data-"+k("framerAppearId"),L=(t,e)=>i=>e(t(i)),B=(...t)=>t.reduce(L),I=(t,e,i)=>i>e?e:i<t?t:i,F=t=>1e3*t,N=t=>t/1e3,U={layout:0,mainThread:0,waapi:0},_=()=>{},W=()=>{},$=t=>e=>"string"==typeof e&&e.startsWith(t),z=$("--"),Y=$("var(--"),H=t=>!!Y(t)&&X.test(t.split("/*")[0].trim()),X=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,q={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},K={...q,transform:t=>I(0,1,t)},G={...q,default:1},Z=t=>Math.round(1e5*t)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tt=(t,e)=>i=>!!("string"==typeof i&&J.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),te=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[r,s,o,a]=n.match(Q);return{[t]:parseFloat(r),[e]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},ti=t=>I(0,255,t),tn={...q,transform:t=>Math.round(ti(t))},tr={test:tt("rgb","red"),parse:te("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+tn.transform(t)+", "+tn.transform(e)+", "+tn.transform(i)+", "+Z(K.transform(n))+")"},ts={test:tt("#"),parse:function(t){let e="",i="",n="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:tr.transform},to=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),ta=to("deg"),tl=to("%"),tu=to("px"),th=to("vh"),tc=to("vw"),tp={...tl,parse:t=>tl.parse(t)/100,transform:t=>tl.transform(100*t)},td={test:tt("hsl","hue"),parse:te("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+tl.transform(Z(e))+", "+tl.transform(Z(i))+", "+Z(K.transform(n))+")"},tf={test:t=>tr.test(t)||ts.test(t)||td.test(t),parse:t=>tr.test(t)?tr.parse(t):td.test(t)?td.parse(t):ts.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tr.transform(t):td.transform(t)},tm=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,ty="number",tv="color",tg=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tb(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},r=[],s=0,o=e.replace(tg,t=>(tf.test(t)?(n.color.push(s),r.push(tv),i.push(tf.parse(t))):t.startsWith("var(")?(n.var.push(s),r.push("var"),i.push(t)):(n.number.push(s),r.push(ty),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:o,indexes:n,types:r}}function tx(t){return tb(t).values}function tw(t){let{split:e,types:i}=tb(t),n=e.length;return t=>{let r="";for(let s=0;s<n;s++)if(r+=e[s],void 0!==t[s]){let e=i[s];e===ty?r+=Z(t[s]):e===tv?r+=tf.transform(t[s]):r+=t[s]}return r}}let tP=t=>"number"==typeof t?0:t,tT={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(Q)?.length||0)+(t.match(tm)?.length||0)>0},parse:tx,createTransformer:tw,getAnimatableNone:function(t){let e=tx(t);return tw(t)(e.map(tP))}};function tS(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tO(t,e){return i=>i>0?e:t}let tE=(t,e,i)=>t+(e-t)*i,tj=(t,e,i)=>{let n=t*t,r=i*(e*e-n)+n;return r<0?0:Math.sqrt(r)},tM=[ts,tr,td],tA=t=>tM.find(e=>e.test(t));function tV(t){let e=tA(t);if(_(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===td&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let r=0,s=0,o=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,a=2*i-n;r=tS(a,n,t+1/3),s=tS(a,n,t),o=tS(a,n,t-1/3)}else r=s=o=i;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*o),alpha:n}}(i)),i}let tD=(t,e)=>{let i=tV(t),n=tV(e);if(!i||!n)return tO(t,e);let r={...i};return t=>(r.red=tj(i.red,n.red,t),r.green=tj(i.green,n.green,t),r.blue=tj(i.blue,n.blue,t),r.alpha=tE(i.alpha,n.alpha,t),tr.transform(r))},tC=new Set(["none","hidden"]);function tk(t,e){return i=>tE(t,e,i)}function tR(t){return"number"==typeof t?tk:"string"==typeof t?H(t)?tO:tf.test(t)?tD:tI:Array.isArray(t)?tL:"object"==typeof t?tf.test(t)?tD:tB:tO}function tL(t,e){let i=[...t],n=i.length,r=t.map((t,i)=>tR(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=r[e](t);return i}}function tB(t,e){let i={...t,...e},n={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(n[r]=tR(t[r])(t[r],e[r]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let tI=(t,e)=>{let i=tT.createTransformer(e),n=tb(t),r=tb(e);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?tC.has(t)&&!r.values.length||tC.has(e)&&!n.values.length?function(t,e){return tC.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):B(tL(function(t,e){let i=[],n={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let s=e.types[r],o=t.indexes[s][n[s]],a=t.values[o]??0;i[r]=a,n[s]++}return i}(n,r),r.values),i):(_(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tO(t,e))};function tF(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tE(t,e,i):tR(t)(t,e)}let tN=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>f.update(e,t),stop:()=>m(e),now:()=>y.isProcessing?y.timestamp:O.now()}},tU=(t,e,i=10)=>{let n="",r=Math.max(Math.round(e/i),2);for(let e=0;e<r;e++)n+=t(e/(r-1))+", ";return`linear(${n.substring(0,n.length-2)})`};function t_(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function tW(t,e,i){var n,r;let s=Math.max(e-5,0);return n=i-t(s),(r=e-s)?1e3/r*n:0}let t$={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tz(t,e){return t*Math.sqrt(1-e*e)}let tY=["duration","bounce"],tH=["stiffness","damping","mass"];function tX(t,e){return e.some(e=>void 0!==t[e])}function tq(t=t$.visualDuration,e=t$.bounce){let i,n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:r,restDelta:s}=n,o=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:c,duration:p,velocity:d,isResolvedFromDuration:f}=function(t){let e={velocity:t$.velocity,stiffness:t$.stiffness,damping:t$.damping,mass:t$.mass,isResolvedFromDuration:!1,...t};if(!tX(t,tH)&&tX(t,tY))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,r=2*I(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:t$.mass,stiffness:n,damping:r}}else{let i=function({duration:t=t$.duration,bounce:e=t$.bounce,velocity:i=t$.velocity,mass:n=t$.mass}){let r,s;_(t<=F(t$.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=I(t$.minDamping,t$.maxDamping,o),t=I(t$.minDuration,t$.maxDuration,N(t)),o<1?(r=e=>{let n=e*o,r=n*t;return .001-(n-i)/tz(e,o)*Math.exp(-r)},s=e=>{let n=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-n),l=tz(Math.pow(e,2),o);return(n*i+i-s)*a*(-r(e)+.001>0?-1:1)/l}):(r=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(r,s,5/t);if(t=F(t),isNaN(a))return{stiffness:t$.stiffness,damping:t$.damping,duration:t};{let e=Math.pow(a,2)*n;return{stiffness:e,damping:2*o*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:t$.mass}).isResolvedFromDuration=!0}return e}({...n,velocity:-N(n.velocity||0)}),m=d||0,y=h/(2*Math.sqrt(u*c)),v=a-o,g=N(Math.sqrt(u/c)),b=5>Math.abs(v);if(r||(r=b?t$.restSpeed.granular:t$.restSpeed.default),s||(s=b?t$.restDelta.granular:t$.restDelta.default),y<1){let t=tz(g,y);i=e=>a-Math.exp(-y*g*e)*((m+y*g*v)/t*Math.sin(t*e)+v*Math.cos(t*e))}else if(1===y)i=t=>a-Math.exp(-g*t)*(v+(m+g*v)*t);else{let t=g*Math.sqrt(y*y-1);i=e=>{let i=Math.exp(-y*g*e),n=Math.min(t*e,300);return a-i*((m+y*g*v)*Math.sinh(n)+t*v*Math.cosh(n))/t}}let x={calculatedDuration:f&&p||null,next:t=>{let e=i(t);if(f)l.done=t>=p;else{let n=0===t?m:0;y<1&&(n=0===t?F(m):tW(i,t,e));let o=Math.abs(a-e)<=s;l.done=Math.abs(n)<=r&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(t_(x),2e4),e=tU(e=>x.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return x}function tK({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:r=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let c,p,d=t[0],f={done:!1,value:d},m=t=>void 0!==a&&t<a||void 0!==l&&t>l,y=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l,v=i*e,g=d+v,b=void 0===o?g:o(g);b!==g&&(v=b-d);let x=t=>-v*Math.exp(-t/n),w=t=>b+x(t),P=t=>{let e=x(t),i=w(t);f.done=Math.abs(e)<=u,f.value=f.done?b:i},T=t=>{m(f.value)&&(c=t,p=tq({keyframes:[f.value,y(f.value)],velocity:tW(w,t,f.value),damping:r,stiffness:s,restDelta:u,restSpeed:h}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return(p||void 0!==c||(e=!0,P(t),T(t)),void 0!==c&&t>=c)?p.next(t-c):(e||P(t),f)}}}tq.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),r=Math.min(t_(n),2e4);return{type:"keyframes",ease:t=>n.next(r*t).value/e,duration:N(r)}}(t,100,tq);return t.ease=e.ease,t.duration=F(e.duration),t.type="keyframes",t};let tG=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tZ(t,e,i,n){if(t===e&&i===n)return u;let r=e=>(function(t,e,i,n,r){let s,o,a=0;do(s=tG(o=e+(i-e)/2,n,r)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:tG(r(t),e,n)}let tQ=tZ(.42,0,1,1),tJ=tZ(0,0,.58,1),t0=tZ(.42,0,.58,1),t1=t=>Array.isArray(t)&&"number"!=typeof t[0],t5=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t2=t=>e=>1-t(1-e),t3=tZ(.33,1.53,.69,.99),t9=t2(t3),t4=t5(t9),t8=t=>(t*=2)<1?.5*t9(t):.5*(2-Math.pow(2,-10*(t-1))),t6=t=>1-Math.sin(Math.acos(t)),t7=t2(t6),et=t5(t6),ee=t=>Array.isArray(t)&&"number"==typeof t[0],ei={linear:u,easeIn:tQ,easeInOut:t0,easeOut:tJ,circIn:t6,circInOut:et,circOut:t7,backIn:t9,backInOut:t4,backOut:t3,anticipate:t8},en=t=>"string"==typeof t,er=t=>{if(ee(t)){W(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,n,r]=t;return tZ(e,i,n,r)}return en(t)?(W(void 0!==ei[t],`Invalid easing type '${t}'`),ei[t]):t},es=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n};function eo({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){var r;let s=t1(n)?n.map(er):er(n),o={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:n,mixer:r}={}){let s=t.length;if(W(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let n=[],r=i||h.mix||tF,s=t.length-1;for(let i=0;i<s;i++){let s=r(t[i],t[i+1]);e&&(s=B(Array.isArray(e)?e[i]||u:e,s)),n.push(s)}return n}(e,n,r),l=a.length,c=i=>{if(o&&i<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let r=es(t[n],t[n+1],i);return a[n](r)};return i?e=>c(I(t[0],t[s-1],e)):c}((r=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let r=es(0,e,n);t.push(tE(i,1,r))}}(e,t.length-1),e}(e),r.map(e=>e*t)),e,{ease:Array.isArray(s)?s:e.map(()=>s||t0).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let ea=t=>null!==t;function el(t,{repeat:e,repeatType:i="loop"},n,r=1){let s=t.filter(ea),o=r<0||e&&"loop"!==i&&e%2==1?0:s.length-1;return o&&void 0!==n?n:s[o]}let eu={decay:tK,inertia:tK,tween:eo,keyframes:eo,spring:tq};function eh(t){"string"==typeof t.type&&(t.type=eu[t.type])}class ec{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let ep=t=>t/100;class ed extends ec{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=(t=!0)=>{if(t){let{motionValue:t}=this.options;t&&t.updatedAt!==O.now()&&this.tick(O.now())}this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},U.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;eh(t);let{type:e=eo,repeat:i=0,repeatDelay:n=0,repeatType:r,velocity:s=0}=t,{keyframes:o}=t,a=e||eo;a!==eo&&"number"!=typeof o[0]&&(this.mixKeyframes=B(ep,tF(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===r&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=t_(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:r,mirroredGenerator:s,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:c,repeatDelay:p,type:d,onUpdate:f,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let y=this.currentTime-l*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?y<0:y>n;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let g=this.currentTime,b=i;if(h){let t=Math.min(this.currentTime,n)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,h+1))%2&&("reverse"===c?(i=1-i,p&&(i-=p/o)):"mirror"===c&&(b=s)),g=I(0,1,i)*o}let x=v?{done:!1,value:u[0]}:b.next(g);r&&(x.value=r(x.value));let{done:w}=x;v||null===a||(w=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let P=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return P&&d!==tK&&(x.value=el(u,this.options,m,this.speed)),f&&f(x.value),P&&this.finish(),x}then(t,e){return this.finished.then(t,e)}get duration(){return N(this.calculatedDuration)}get time(){return N(this.currentTime)}set time(t){t=F(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(O.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=N(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tN,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(O.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,U.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let ef=t=>180*t/Math.PI,em=t=>ev(ef(Math.atan2(t[1],t[0]))),ey={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:em,rotateZ:em,skewX:t=>ef(Math.atan(t[1])),skewY:t=>ef(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},ev=t=>((t%=360)<0&&(t+=360),t),eg=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),eb=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),ex={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:eg,scaleY:eb,scale:t=>(eg(t)+eb(t))/2,rotateX:t=>ev(ef(Math.atan2(t[6],t[5]))),rotateY:t=>ev(ef(Math.atan2(-t[2],t[0]))),rotateZ:em,rotate:em,skewX:t=>ef(Math.atan(t[4])),skewY:t=>ef(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function ew(t){return+!!t.includes("scale")}function eP(t,e){let i,n;if(!t||"none"===t)return ew(e);let r=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=ex,n=r;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=ey,n=e}if(!n)return ew(e);let s=i[e],o=n[1].split(",").map(eS);return"function"==typeof s?s(o):o[s]}let eT=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return eP(i,e)};function eS(t){return parseFloat(t.trim())}let eO=t=>t===q||t===tu,eE=new Set(["x","y","z"]),ej=g.filter(t=>!eE.has(t)),eM={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>eP(e,"x"),y:(t,{transform:e})=>eP(e,"y")};eM.translateX=eM.x,eM.translateY=eM.y;let eA=new Set,eV=!1,eD=!1,eC=!1;function ek(){if(eD){let t=Array.from(eA).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return ej.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eD=!1,eV=!1,eA.forEach(t=>t.complete(eC)),eA.clear()}function eR(){eA.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eD=!0)})}class eL{constructor(t,e,i,n,r,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=r,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(eA.add(this),eV||(eV=!0,f.read(eR),f.resolveKeyframes(ek))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let r=n?.get(),s=t[t.length-1];if(void 0!==r)t[0]=r;else if(i&&e){let n=i.readValue(e,s);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=s),n&&void 0===r&&n.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eA.delete(this)}cancel(){"scheduled"===this.state&&(eA.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eB=t=>t.startsWith("--");function eI(t){let e;return()=>(void 0===e&&(e=t()),e)}let eF=eI(()=>void 0!==window.ScrollTimeline),eN={},eU=function(t,e){let i=eI(t);return()=>eN[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),e_=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,eW={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:e_([0,.65,.55,1]),circOut:e_([.55,0,1,.45]),backIn:e_([.31,.01,.66,-.59]),backOut:e_([.33,1.53,.69,.99])};function e$(t){return"function"==typeof t&&"applyToOptions"in t}class ez extends ec{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:r,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!r,this.allowFlatten=s,this.options=t,W("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return e$(t)&&eU()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:r=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let c=function t(e,i){if(e)return"function"==typeof e?eU()?tU(e,i):"ease-out":ee(e)?e_(e):Array.isArray(e)?e.map(e=>t(e,i)||eW.easeOut):eW[e]}(a,r);Array.isArray(c)&&(h.easing=c),p.value&&U.waapi++;let d={delay:n,duration:r,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};u&&(d.pseudoElement=u);let f=t.animate(h,d);return p.value&&f.finished.finally(()=>{U.waapi--}),f}(e,i,n,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let t=el(n,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){eB(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return N(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return N(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=F(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eF())?(this.animation.timeline=t,u):e(this)}}let eY={anticipate:t8,backInOut:t4,circInOut:et};class eH extends ez{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eY&&(t.ease=eY[t.ease])}(t),eh(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:r,...s}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new ed({...s,autoplay:!1}),a=F(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let eX=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tT.test(t)||"0"===t)&&!t.startsWith("url("));var eq,eK,eG=i(7351);let eZ=new Set(["opacity","clipPath","filter","transform"]),eQ=eI(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eJ extends ec{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=O.now();let c={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:r,repeatType:s,name:a,motionValue:l,element:u,...h},p=u?.KeyframeResolver||eL;this.keyframeResolver=new p(o,(t,e,i)=>this.onKeyframesResolved(t,e,c,!i),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:r,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:c}=i;this.resolvedAt=O.now(),!function(t,e,i,n){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=eX(r,e),a=eX(s,e);return _(o===a,`You are trying to animate ${e} from "${r}" to "${s}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${s} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||e$(i))&&n)}(t,r,s,o)&&((h.instantAnimations||!a)&&c?.(el(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let p={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},d=!l&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:r,damping:s,type:o}=t;if(!(0,eG.s)(e?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return eQ()&&i&&eZ.has(i)&&("transform"!==i||!l)&&!a&&!n&&"mirror"!==r&&0!==s&&"inertia"!==o}(p)?new eH({...p,element:p.motionValue.owner.current}):new ed(p);d.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eC=!0,eR(),ek(),eC=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e0=t=>null!==t,e1={type:"spring",stiffness:500,damping:25,restSpeed:10},e5=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),e2={type:"keyframes",duration:.8},e3={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e9=(t,{keyframes:e})=>e.length>2?e2:b.has(t)?t.startsWith("scale")?e5(e[1]):e1:e3,e4=(t,e,i,n={},r,s)=>o=>{let a=l(n,t)||{},u=a.delay||n.delay||0,{elapsed:c=0}=n;c-=F(u);let p={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:s?void 0:r};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:r,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&Object.assign(p,e9(t,p)),p.duration&&(p.duration=F(p.duration)),p.repeatDelay&&(p.repeatDelay=F(p.repeatDelay)),void 0!==p.from&&(p.keyframes[0]=p.from);let d=!1;if(!1!==p.type&&(0!==p.duration||p.repeatDelay)||(p.duration=0,0===p.delay&&(d=!0)),(h.instantAnimations||h.skipAnimations)&&(d=!0,p.duration=0,p.delay=0),p.allowFlatten=!a.type&&!a.ease,d&&!s&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},n){let r=t.filter(e0),s=e&&"loop"!==i&&e%2==1?0:r.length-1;return r[s]}(p.keyframes,a);if(void 0!==t)return void f.update(()=>{p.onUpdate(t),p.onComplete()})}return a.isSync?new ed(p):new eJ(p)};function e8(t,e,{delay:i=0,transitionOverride:n,type:r}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:o,...u}=e;n&&(s=n);let h=[],c=r&&t.animationState&&t.animationState.getState()[r];for(let e in u){let n=t.getValue(e,t.latestValues[e]??null),r=u[e];if(void 0===r||c&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(c,e))continue;let o={delay:i,...l(s||{},e)},a=n.get();if(void 0!==a&&!n.isAnimating&&!Array.isArray(r)&&r===a&&!o.velocity)continue;let p=!1;if(window.MotionHandoffAnimation){let i=t.props[R];if(i){let t=window.MotionHandoffAnimation(i,e,f);null!==t&&(o.startTime=t,p=!0)}}C(t,e),n.start(e4(e,n,r,t.shouldReduceMotion&&x.has(e)?{type:!1}:o,t,p));let d=n.animation;d&&h.push(d)}return o&&Promise.all(h).then(()=>{f.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:n={},...r}=a(t,e)||{};for(let e in r={...r,...i}){var s;let i=V(s=r[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,A(i))}}(t,o)})}),h}function e6(t,e,i={}){let n=a(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let s=n?()=>Promise.all(e8(t,n,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,i=0,n=0,r=1,s){let o=[],a=(t.variantChildren.size-1)*n,l=1===r?(t=0)=>t*n:(t=0)=>a-t*n;return Array.from(t.variantChildren).sort(e7).forEach((t,n)=>{t.notify("AnimationStart",e),o.push(e6(t,e,{...s,delay:i+l(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,s+n,o,a,i)}:()=>Promise.resolve(),{when:l}=r;if(!l)return Promise.all([s(),o(i.delay)]);{let[t,e]="beforeChildren"===l?[s,o]:[o,s];return t().then(()=>e())}}function e7(t,e){return t.sortNodePosition(e)}function it(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}function ie(t){return"string"==typeof t||Array.isArray(t)}let ii=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ir=["initial",...ii],is=ir.length,io=[...ii].reverse(),ia=ii.length;function il(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iu(){return{animate:il(!0),whileInView:il(),whileHover:il(),whileTap:il(),whileDrag:il(),whileFocus:il(),exit:il()}}class ih{constructor(t){this.isMounted=!1,this.node=t}update(){}}class ic extends ih{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>e6(t,e,i)));else if("string"==typeof e)n=e6(t,e,i);else{let r="function"==typeof e?a(t,e,i.custom):e;n=Promise.all(e8(t,r,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=iu(),n=!0,s=e=>(i,n)=>{let r=a(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(r){let{transition:t,transitionEnd:e,...n}=r;i={...i,...n,...e}}return i};function o(o){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<is;t++){let n=ir[t],r=e.props[n];(ie(r)||!1===r)&&(i[n]=r)}return i}(t.parent)||{},h=[],c=new Set,p={},d=1/0;for(let e=0;e<ia;e++){var f,m;let a=io[e],y=i[a],v=void 0!==l[a]?l[a]:u[a],g=ie(v),b=a===o?y.isActive:null;!1===b&&(d=e);let x=v===u[a]&&v!==l[a]&&g;if(x&&n&&t.manuallyAnimateOnMount&&(x=!1),y.protectedKeys={...p},!y.isActive&&null===b||!v&&!y.prevProp||r(v)||"boolean"==typeof v)continue;let w=(f=y.prevProp,"string"==typeof(m=v)?m!==f:!!Array.isArray(m)&&!it(m,f)),P=w||a===o&&y.isActive&&!x&&g||e>d&&g,T=!1,S=Array.isArray(v)?v:[v],O=S.reduce(s(a),{});!1===b&&(O={});let{prevResolvedValues:E={}}=y,j={...E,...O},M=e=>{P=!0,c.has(e)&&(T=!0,c.delete(e)),y.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in j){let e=O[t],i=E[t];if(p.hasOwnProperty(t))continue;let n=!1;(V(e)&&V(i)?it(e,i):e===i)?void 0!==e&&c.has(t)?M(t):y.protectedKeys[t]=!0:null!=e?M(t):c.add(t)}y.prevProp=v,y.prevResolvedValues=O,y.isActive&&(p={...p,...O}),n&&t.blockInitialAnimation&&(P=!1);let A=!(x&&w)||T;P&&A&&h.push(...S.map(t=>({animation:t,options:{type:a}})))}if(c.size){let e={};if("boolean"!=typeof l.initial){let i=a(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}c.forEach(i=>{let n=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=n??null}),h.push({animation:e})}let y=!!h.length;return n&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(y=!1),n=!1,y?e(h):Promise.resolve()}return{animateChanges:o,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let r=o(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=iu(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();r(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let ip=0;class id extends ih{constructor(){super(...arguments),this.id=ip++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let im={x:!1,y:!1};function iy(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let iv=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function ig(t){return{point:{x:t.pageX,y:t.pageY}}}let ib=t=>e=>iv(e)&&t(e,ig(e));function ix(t,e,i,n){return iy(t,e,ib(i),n)}function iw({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}function iP(t){return t.max-t.min}function iT(t,e,i,n=.5){t.origin=n,t.originPoint=tE(e.min,e.max,t.origin),t.scale=iP(i)/iP(e),t.translate=tE(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iS(t,e,i,n){iT(t.x,e.x,i.x,n?n.originX:void 0),iT(t.y,e.y,i.y,n?n.originY:void 0)}function iO(t,e,i){t.min=i.min+e.min,t.max=t.min+iP(e)}function iE(t,e,i){t.min=e.min-i.min,t.max=t.min+iP(e)}function ij(t,e,i){iE(t.x,e.x,i.x),iE(t.y,e.y,i.y)}let iM=()=>({translate:0,scale:1,origin:0,originPoint:0}),iA=()=>({x:iM(),y:iM()}),iV=()=>({min:0,max:0}),iD=()=>({x:iV(),y:iV()});function iC(t){return[t("x"),t("y")]}function ik(t){return void 0===t||1===t}function iR({scale:t,scaleX:e,scaleY:i}){return!ik(t)||!ik(e)||!ik(i)}function iL(t){return iR(t)||iB(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iB(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iI(t,e,i,n,r){return void 0!==r&&(t=n+r*(t-n)),n+i*(t-n)+e}function iF(t,e=0,i=1,n,r){t.min=iI(t.min,e,i,n,r),t.max=iI(t.max,e,i,n,r)}function iN(t,{x:e,y:i}){iF(t.x,e.translate,e.scale,e.originPoint),iF(t.y,i.translate,i.scale,i.originPoint)}function iU(t,e){t.min=t.min+e,t.max=t.max+e}function i_(t,e,i,n,r=.5){let s=tE(t.min,t.max,r);iF(t,e,i,s,n)}function iW(t,e){i_(t.x,e.x,e.scaleX,e.scale,e.originX),i_(t.y,e.y,e.scaleY,e.scale,e.originY)}function i$(t,e){return iw(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let iz=({current:t})=>t?t.ownerDocument.defaultView:null;function iY(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iH=(t,e)=>Math.abs(t-e);class iX{constructor(t,e,{transformPagePoint:i,contextWindow:n,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iG(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iH(t.x,e.x)**2+iH(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:n}=t,{timestamp:r}=y;this.history.push({...n,timestamp:r});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iq(e,this.transformPagePoint),f.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=iG("pointercancel"===t.type?this.lastMoveEventInfo:iq(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),n&&n(t,s)},!iv(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.contextWindow=n||window;let s=iq(ig(t),this.transformPagePoint),{point:o}=s,{timestamp:a}=y;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,iG(s,this.history)),this.removeListeners=B(ix(this.contextWindow,"pointermove",this.handlePointerMove),ix(this.contextWindow,"pointerup",this.handlePointerUp),ix(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),m(this.updatePoint)}}function iq(t,e){return e?{point:e(t.point)}:t}function iK(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iG({point:t},e){return{point:t,delta:iK(t,iZ(e)),offset:iK(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,r=iZ(t);for(;i>=0&&(n=t[i],!(r.timestamp-n.timestamp>F(.1)));)i--;if(!n)return{x:0,y:0};let s=N(r.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let o={x:(r.x-n.x)/s,y:(r.y-n.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function iZ(t){return t[t.length-1]}function iQ(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iJ(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function i0(t,e,i){return{min:i1(t,e),max:i1(t,i)}}function i1(t,e){return"number"==typeof t?t:t[e]||0}let i5=new WeakMap;class i2{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iD(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new iX(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(ig(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:r}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(im[t])return null;else return im[t]=!0,()=>{im[t]=!1};return im.x||im.y?null:(im.x=im.y=!0,()=>{im.x=im.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iC(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tl.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=iP(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&f.postRender(()=>r(t,e)),C(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iC(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:iz(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:n}=e;this.startAnimation(n);let{onDragEnd:r}=this.getProps();r&&f.postRender(()=>r(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!i3(t,n,this.currentDirection))return;let r=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?tE(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?tE(i,t,n.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),r.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&iY(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:n,right:r}){return{x:iQ(t.x,i,r),y:iQ(t.y,e,n)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:i0(t,"left","right"),y:i0(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iC(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iY(e))return!1;let n=e.current;W(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(t,e,i){let n=i$(t,i),{scroll:r}=e;return r&&(iU(n.x,r.offset.x),iU(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),o=(t=r.layout.layoutBox,{x:iJ(t.x,s.x),y:iJ(t.y,s.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=iw(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iC(o=>{if(!i3(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return C(this.visualElement,t),i.start(e4(t,i,0,e,this.visualElement,!1))}stopAnimation(){iC(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iC(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iC(e=>{let{drag:i}=this.getProps();if(!i3(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[e];r.set(t[e]-tE(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iY(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};iC(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=iP(t),r=iP(e);return r>n?i=es(e.min,e.max-n,t.min):n>r&&(i=es(t.min,t.max-r,e.min)),I(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iC(e=>{if(!i3(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:s}=this.constraints[e];i.set(tE(r,s,n[e]))})}addListeners(){if(!this.visualElement.current)return;i5.set(this.visualElement,this);let t=ix(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iY(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),f.read(e);let r=iy(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iC(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),n(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:o}}}function i3(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class i9 extends ih{constructor(t){super(t),this.removeGroupControls=u,this.removeListeners=u,this.controls=new i2(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let i4=t=>(e,i)=>{t&&f.postRender(()=>t(e,i))};class i8 extends ih{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(t){this.session=new iX(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iz(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:i4(t),onStart:i4(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&f.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=ix(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var i6=i(5155);let{schedule:i7}=d(queueMicrotask,!1);var nt=i(2115),ne=i(2082),ni=i(869);let nn=(0,nt.createContext)({}),nr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ns(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let no={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tu.test(t))return t;else t=parseFloat(t);let i=ns(t,e.target.x),n=ns(t,e.target.y);return`${i}% ${n}%`}},na={};class nl extends nt.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=t;for(let t in nh)na[t]=nh[t],z(t)&&(na[t].isCSSVariable=!0);r&&(e.group&&e.group.add(r),i&&i.register&&n&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),nr.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:r}=this.props,{projection:s}=i;return s&&(s.isPresent=r,n||t.layoutDependency!==e||void 0===e||t.isPresent!==r?s.willUpdate():this.safeToRemove(),t.isPresent!==r&&(r?s.promote():s.relegate()||f.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),i7.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function nu(t){let[e,i]=(0,ne.xQ)(),n=(0,nt.useContext)(ni.L);return(0,i6.jsx)(nl,{...t,layoutGroup:n,switchLayoutGroup:(0,nt.useContext)(nn),isPresent:e,safeToRemove:i})}let nh={borderRadius:{...no,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:no,borderTopRightRadius:no,borderBottomLeftRadius:no,borderBottomRightRadius:no,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=tT.parse(t);if(n.length>5)return t;let r=tT.createTransformer(t),s=+("number"!=typeof n[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;n[0+s]/=o,n[1+s]/=a;let l=tE(o,a,.5);return"number"==typeof n[2+s]&&(n[2+s]/=l),"number"==typeof n[3+s]&&(n[3+s]/=l),r(n)}}};var nc=i(6983);function np(t){return(0,nc.G)(t)&&"ownerSVGElement"in t}let nd=(t,e)=>t.depth-e.depth;class nf{constructor(){this.children=[],this.isDirty=!1}add(t){w(this.children,t),this.isDirty=!0}remove(t){P(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(nd),this.isDirty=!1,this.children.forEach(t)}}function nm(t){return D(t)?t.get():t}let ny=["TopLeft","TopRight","BottomLeft","BottomRight"],nv=ny.length,ng=t=>"string"==typeof t?parseFloat(t):t,nb=t=>"number"==typeof t||tu.test(t);function nx(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nw=nT(0,.5,t7),nP=nT(.5,.95,u);function nT(t,e,i){return n=>n<t?0:n>e?1:i(es(t,e,n))}function nS(t,e){t.min=e.min,t.max=e.max}function nO(t,e){nS(t.x,e.x),nS(t.y,e.y)}function nE(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nj(t,e,i,n,r){return t-=e,t=n+1/i*(t-n),void 0!==r&&(t=n+1/r*(t-n)),t}function nM(t,e,[i,n,r],s,o){!function(t,e=0,i=1,n=.5,r,s=t,o=t){if(tl.test(e)&&(e=parseFloat(e),e=tE(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=tE(s.min,s.max,n);t===s&&(a-=e),t.min=nj(t.min,e,i,a,r),t.max=nj(t.max,e,i,a,r)}(t,e[i],e[n],e[r],e.scale,s,o)}let nA=["x","scaleX","originX"],nV=["y","scaleY","originY"];function nD(t,e,i,n){nM(t.x,e,nA,i?i.x:void 0,n?n.x:void 0),nM(t.y,e,nV,i?i.y:void 0,n?n.y:void 0)}function nC(t){return 0===t.translate&&1===t.scale}function nk(t){return nC(t.x)&&nC(t.y)}function nR(t,e){return t.min===e.min&&t.max===e.max}function nL(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nB(t,e){return nL(t.x,e.x)&&nL(t.y,e.y)}function nI(t){return iP(t.x)/iP(t.y)}function nF(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class nN{constructor(){this.members=[]}add(t){w(this.members,t),t.scheduleRender()}remove(t){if(P(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nU={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},n_=["","X","Y","Z"],nW={visibility:"hidden"},n$=0;function nz(t,e,i,n){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),n&&(n[t]=0))}function nY({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(t={},i=e?.()){this.id=n$++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,p.value&&(nU.nodes=nU.calculatedTargetDeltas=nU.calculatedProjections=0),this.nodes.forEach(nq),this.nodes.forEach(n1),this.nodes.forEach(n5),this.nodes.forEach(nK),p.addProjectionMetrics&&p.addProjectionMetrics(nU)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new nf)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new T),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=np(e)&&!(np(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),t){let i,n=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=O.now(),n=({timestamp:r})=>{let s=r-i;s>=250&&(m(n),t(s-e))};return f.setup(n,!0),()=>m(n)}(n,250),nr.hasAnimatedSinceResize&&(nr.hasAnimatedSinceResize=!1,this.nodes.forEach(n0))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||r.getDefaultTransition()||n6,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),u=!this.targetLayout||!nB(this.targetLayout,n),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,h);let e={...l(s,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||n0(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),m(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(n2),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[R];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",f,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nZ);return}this.isUpdating||this.nodes.forEach(nQ),this.isUpdating=!1,this.nodes.forEach(nJ),this.nodes.forEach(nH),this.nodes.forEach(nX),this.clearAllSnapshots();let t=O.now();y.delta=I(0,1e3/60,t-y.timestamp),y.timestamp=t,y.isProcessing=!0,v.update.process(y),v.preRender.process(y),v.render.process(y),y.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,i7.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nG),this.sharedNodes.forEach(n3)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,f.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){f.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||iP(this.snapshot.measuredBox.x)||iP(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iD(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nk(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||iL(this.latestValues)||s)&&(r(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),re((e=n).x),re(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iD();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(rn))){let{scroll:t}=this.root;t&&(iU(e.x,t.offset.x),iU(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iD();if(nO(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:r,options:s}=n;n!==this.root&&r&&s.layoutScroll&&(r.wasRoot&&nO(e,t),iU(e.x,r.offset.x),iU(e.y,r.offset.y))}return e}applyTransform(t,e=!1){let i=iD();nO(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iW(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),iL(n.latestValues)&&iW(i,n.latestValues)}return iL(this.latestValues)&&iW(i,this.latestValues),i}removeTransform(t){let e=iD();nO(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iL(i.latestValues))continue;iR(i.latestValues)&&i.updateSnapshot();let n=iD();nO(n,i.measurePageBox()),nD(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return iL(this.latestValues)&&nD(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==y.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:r}=this.options;if(this.layout&&(n||r)){if(this.resolvedRelativeTargetAt=y.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iD(),this.relativeTargetOrigin=iD(),ij(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),nO(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iD(),this.targetWithTransforms=iD()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,iO(s.x,o.x,a.x),iO(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nO(this.target,this.layout.layoutBox),iN(this.target,this.targetDelta)):nO(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iD(),this.relativeTargetOrigin=iD(),ij(this.relativeTargetOrigin,this.target,t.target),nO(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}p.value&&nU.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iR(this.parent.latestValues)||iB(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===y.timestamp&&(i=!1),i)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;nO(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,n=!1){let r,s,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(r=i[a]).projectionDelta;let{visualElement:o}=r.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&iW(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,iN(t,s)),n&&iL(r.latestValues)&&iW(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iD());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nE(this.prevProjectionDelta.x,this.projectionDelta.x),nE(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iS(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&nF(this.projectionDelta.x,this.prevProjectionDelta.x)&&nF(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),p.value&&nU.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iA(),this.projectionDelta=iA(),this.projectionDeltaWithTransform=iA()}setAnimationOrigin(t,e=!1){let i,n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},o=iA();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=iD(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(n8));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(n9(o.x,t.x,n),n9(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,p,d,f,m,y;ij(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),d=this.relativeTarget,f=this.relativeTargetOrigin,m=a,y=n,n4(d.x,f.x,m.x,y),n4(d.y,f.y,m.y,y),i&&(u=this.relativeTarget,p=i,nR(u.x,p.x)&&nR(u.y,p.y))&&(this.isProjectionDirty=!1),i||(i=iD()),nO(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,n,r,s){r?(t.opacity=tE(0,i.opacity??1,nw(n)),t.opacityExit=tE(e.opacity??1,0,nP(n))):s&&(t.opacity=tE(e.opacity??1,i.opacity??1,n));for(let r=0;r<nv;r++){let s=`border${ny[r]}Radius`,o=nx(e,s),a=nx(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||nb(o)===nb(a)?(t[s]=Math.max(tE(ng(o),ng(a),n),0),(tl.test(a)||tl.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=tE(e.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(!1),this.resumingFrom?.currentAnimation?.stop(!1),this.pendingAnimation&&(m(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=f.update(()=>{nr.hasAnimatedSinceResize=!0,U.layout++,this.motionValue||(this.motionValue=A(0)),this.currentAnimation=function(t,e,i){let n=D(t)?t:A(t);return n.start(e4("",n,e,i)),n.animation}(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{U.layout--},onComplete:()=>{U.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop(!1)),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:r}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&ri(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||iD();let e=iP(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=iP(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}nO(e,i),iW(e,r),iS(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nN),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&nz("z",t,n,this.animationValues);for(let e=0;e<n_.length;e++)nz(`rotate${n_[e]}`,t,n,this.animationValues),nz(`skew${n_[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return nW;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=nm(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=nm(t?.pointerEvents)||""),this.hasProjected&&!iL(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let r=n.animationValues||n.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let n="",r=t.x.translate/e.x,s=t.y.translate/e.y,o=i?.z||0;if((r||s||o)&&(n=`translate3d(${r}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:s,skewX:o,skewY:a}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),r&&(n+=`rotateX(${r}deg) `),s&&(n+=`rotateY(${s}deg) `),o&&(n+=`skewX(${o}deg) `),a&&(n+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(n+=`scale(${a}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),i&&(e.transform=i(r,e.transform));let{x:s,y:o}=this.projectionDelta;for(let t in e.transformOrigin=`${100*s.origin}% ${100*o.origin}% 0`,n.animationValues?e.opacity=n===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:e.opacity=n===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,na){if(void 0===r[t])continue;let{correct:i,applyTo:s,isCSSVariable:o}=na[t],a="none"===e.transform?r[t]:i(r[t],n);if(s){let t=s.length;for(let i=0;i<t;i++)e[s[i]]=a}else o?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=n===this?nm(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop(!1)),this.root.nodes.forEach(nZ),this.root.sharedNodes.clear()}}}function nH(t){t.updateLayout()}function nX(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=t.layout,{animationType:r}=t.options,s=e.source!==t.layout.source;"size"===r?iC(t=>{let n=s?e.measuredBox[t]:e.layoutBox[t],r=iP(n);n.min=i[t].min,n.max=n.min+r}):ri(r,e.layoutBox,i)&&iC(n=>{let r=s?e.measuredBox[n]:e.layoutBox[n],o=iP(i[n]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+o)});let o=iA();iS(o,i,e.layoutBox);let a=iA();s?iS(a,t.applyTransform(n,!0),e.measuredBox):iS(a,i,e.layoutBox);let l=!nk(o),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let o=iD();ij(o,e.layoutBox,r.layoutBox);let a=iD();ij(a,i,s.layoutBox),nB(o,a)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function nq(t){p.value&&nU.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function nK(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function nG(t){t.clearSnapshot()}function nZ(t){t.clearMeasurements()}function nQ(t){t.isLayoutDirty=!1}function nJ(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function n0(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function n1(t){t.resolveTargetDelta()}function n5(t){t.calcProjection()}function n2(t){t.resetSkewAndRotation()}function n3(t){t.removeLeadSnapshot()}function n9(t,e,i){t.translate=tE(e.translate,0,i),t.scale=tE(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function n4(t,e,i,n){t.min=tE(e.min,i.min,n),t.max=tE(e.max,i.max,n)}function n8(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let n6={duration:.45,ease:[.4,0,.1,1]},n7=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),rt=n7("applewebkit/")&&!n7("chrome/")?Math.round:u;function re(t){t.min=rt(t.min),t.max=rt(t.max)}function ri(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nI(e)-nI(i)))}function rn(t){return t!==t.root&&t.scroll?.wasRoot}let rr=nY({attachResizeListener:(t,e)=>iy(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rs={current:void 0},ro=nY({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!rs.current){let t=new rr({});t.mount(window),t.setOptions({layoutScroll:!0}),rs.current=t}return rs.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function ra(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function rl(t){return!("touch"===t.pointerType||im.x||im.y)}function ru(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let r=n["onHover"+i];r&&f.postRender(()=>r(e,ig(e)))}class rh extends ih{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=ra(t,i),o=t=>{if(!rl(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let s=t=>{rl(t)&&(n(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,r)};return n.forEach(t=>{t.addEventListener("pointerenter",o,r)}),s}(t,(t,e)=>(ru(this.node,e,"Start"),t=>ru(this.node,t,"End"))))}unmount(){}}class rc extends ih{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=B(iy(this.node.current,"focus",()=>this.onFocus()),iy(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let rp=(t,e)=>!!e&&(t===e||rp(t,e.parentElement)),rd=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rf=new WeakSet;function rm(t){return e=>{"Enter"===e.key&&t(e)}}function ry(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let rv=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=rm(()=>{if(rf.has(i))return;ry(i,"down");let t=rm(()=>{ry(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>ry(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function rg(t){return iv(t)&&!(im.x||im.y)}function rb(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let r=n["onTap"+("End"===i?"":i)];r&&f.postRender(()=>r(e,ig(e)))}class rx extends ih{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=ra(t,i),o=t=>{let n=t.currentTarget;if(!rg(t))return;rf.add(n);let s=e(n,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),rf.has(n)&&rf.delete(n),rg(t)&&"function"==typeof s&&s(t,{success:e})},a=t=>{o(t,n===window||n===document||i.useGlobalTarget||rp(n,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return n.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,r),(0,eG.s)(t))&&(t.addEventListener("focus",t=>rv(t,r)),rd.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(rb(this.node,e,"Start"),(t,{success:e})=>rb(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let rw=new WeakMap,rP=new WeakMap,rT=t=>{let e=rw.get(t.target);e&&e(t)},rS=t=>{t.forEach(rT)},rO={some:0,all:1};class rE extends ih{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:r}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:rO[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;rP.has(i)||rP.set(i,{});let n=rP.get(i),r=JSON.stringify(e);return n[r]||(n[r]=new IntersectionObserver(rS,{root:t,...e})),n[r]}(e);return rw.set(t,i),n.observe(t),()=>{rw.delete(t),n.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=e?i:n;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let rj=(0,nt.createContext)({strict:!1});var rM=i(1508);let rA=(0,nt.createContext)({});function rV(t){return r(t.animate)||ir.some(e=>ie(t[e]))}function rD(t){return!!(rV(t)||t.variants)}function rC(t){return Array.isArray(t)?t.join(" "):t}var rk=i(8972);let rR={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},rL={};for(let t in rR)rL[t]={isEnabled:e=>rR[t].some(t=>!!e[t])};let rB=Symbol.for("motionComponentSymbol");var rI=i(845),rF=i(7494);function rN(t,{layout:e,layoutId:i}){return b.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!na[t]||"opacity"===t)}let rU=(t,e)=>e&&"number"==typeof t?e.transform(t):t,r_={...q,transform:Math.round},rW={borderWidth:tu,borderTopWidth:tu,borderRightWidth:tu,borderBottomWidth:tu,borderLeftWidth:tu,borderRadius:tu,radius:tu,borderTopLeftRadius:tu,borderTopRightRadius:tu,borderBottomRightRadius:tu,borderBottomLeftRadius:tu,width:tu,maxWidth:tu,height:tu,maxHeight:tu,top:tu,right:tu,bottom:tu,left:tu,padding:tu,paddingTop:tu,paddingRight:tu,paddingBottom:tu,paddingLeft:tu,margin:tu,marginTop:tu,marginRight:tu,marginBottom:tu,marginLeft:tu,backgroundPositionX:tu,backgroundPositionY:tu,rotate:ta,rotateX:ta,rotateY:ta,rotateZ:ta,scale:G,scaleX:G,scaleY:G,scaleZ:G,skew:ta,skewX:ta,skewY:ta,distance:tu,translateX:tu,translateY:tu,translateZ:tu,x:tu,y:tu,z:tu,perspective:tu,transformPerspective:tu,opacity:K,originX:tp,originY:tp,originZ:tu,zIndex:r_,fillOpacity:K,strokeOpacity:K,numOctaves:r_},r$={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},rz=g.length;function rY(t,e,i){let{style:n,vars:r,transformOrigin:s}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(b.has(t)){o=!0;continue}if(z(t)){r[t]=i;continue}{let e=rU(i,rW[t]);t.startsWith("origin")?(a=!0,s[t]=e):n[t]=e}}if(!e.transform&&(o||i?n.transform=function(t,e,i){let n="",r=!0;for(let s=0;s<rz;s++){let o=g[s],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=rU(a,rW[o]);if(!l){r=!1;let e=r$[o]||o;n+=`${e}(${t}) `}i&&(e[o]=t)}}return n=n.trim(),i?n=i(e,r?"":n):r&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;n.transformOrigin=`${t} ${e} ${i}`}}let rH=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function rX(t,e,i){for(let n in e)D(e[n])||rN(n,i)||(t[n]=e[n])}let rq={offset:"stroke-dashoffset",array:"stroke-dasharray"},rK={offset:"strokeDashoffset",array:"strokeDasharray"};function rG(t,{attrX:e,attrY:i,attrScale:n,pathLength:r,pathSpacing:s=1,pathOffset:o=0,...a},l,u,h){if(rY(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:p}=t;c.transform&&(p.transform=c.transform,delete c.transform),(p.transform||c.transformOrigin)&&(p.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),p.transform&&(p.transformBox=h?.transformBox??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==n&&(c.scale=n),void 0!==r&&function(t,e,i=1,n=0,r=!0){t.pathLength=1;let s=r?rq:rK;t[s.offset]=tu.transform(-n);let o=tu.transform(e),a=tu.transform(i);t[s.array]=`${o} ${a}`}(c,r,s,o,!1)}let rZ=()=>({...rH(),attrs:{}}),rQ=t=>"string"==typeof t&&"svg"===t.toLowerCase(),rJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function r0(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||rJ.has(t)}let r1=t=>!r0(t);try{!function(t){t&&(r1=e=>e.startsWith("on")?!r0(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let r5=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function r2(t){if("string"!=typeof t||t.includes("-"));else if(r5.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var r3=i(2885);let r9=t=>(e,i)=>{let n=(0,nt.useContext)(rA),s=(0,nt.useContext)(rI.t),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,n,s){return{latestValues:function(t,e,i,n){let s={},a=n(t,{});for(let t in a)s[t]=nm(a[t]);let{initial:l,animate:u}=t,h=rV(t),c=rD(t);e&&c&&!h&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let p=!!i&&!1===i.initial,d=(p=p||!1===l)?u:l;if(d&&"boolean"!=typeof d&&!r(d)){let e=Array.isArray(d)?d:[d];for(let i=0;i<e.length;i++){let n=o(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=p?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(i,n,s,t),renderState:e()}})(t,e,n,s);return i?a():(0,r3.M)(a)};function r4(t,e,i){let{style:n}=t,r={};for(let s in n)(D(n[s])||e.style&&D(e.style[s])||rN(s,t)||i?.getValue(s)?.liveStyle!==void 0)&&(r[s]=n[s]);return r}let r8={useVisualState:r9({scrapeMotionValuesFromProps:r4,createRenderState:rH})};function r6(t,e,i){let n=r4(t,e,i);for(let i in t)(D(t[i])||D(e[i]))&&(n[-1!==g.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}let r7={useVisualState:r9({scrapeMotionValuesFromProps:r6,createRenderState:rZ})},st=t=>e=>e.test(t),se=[q,tu,tl,ta,tc,th,{test:t=>"auto"===t,parse:t=>t}],si=t=>se.find(st(t)),sn=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),sr=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ss=t=>/^0[^.\s]+$/u.test(t),so=new Set(["brightness","contrast","saturate","opacity"]);function sa(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(Q)||[];if(!n)return t;let r=i.replace(n,""),s=+!!so.has(e);return n!==i&&(s*=100),e+"("+s+r+")"}let sl=/\b([a-z-]*)\(.*?\)/gu,su={...tT,getAnimatableNone:t=>{let e=t.match(sl);return e?e.map(sa).join(" "):t}},sh={...rW,color:tf,backgroundColor:tf,outlineColor:tf,fill:tf,stroke:tf,borderColor:tf,borderTopColor:tf,borderRightColor:tf,borderBottomColor:tf,borderLeftColor:tf,filter:su,WebkitFilter:su},sc=t=>sh[t];function sp(t,e){let i=sc(t);return i!==su&&(i=tT),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let sd=new Set(["auto","none","0"]);class sf extends eL{constructor(t,e,i,n,r){super(t,e,i,n,r,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&H(n=n.trim())){let r=function t(e,i,n=1){W(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[r,s]=function(t){let e=sr.exec(t);if(!e)return[,];let[,i,n,r]=e;return[`--${i??n}`,r]}(e);if(!r)return;let o=window.getComputedStyle(i).getPropertyValue(r);if(o){let t=o.trim();return sn(t)?parseFloat(t):t}return H(s)?t(s,i,n+1):s}(n,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!x.has(i)||2!==t.length)return;let[n,r]=t,s=si(n),o=si(r);if(s!==o)if(eO(s)&&eO(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else eM[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||ss(n)))&&i.push(e)}i.length&&function(t,e,i){let n,r=0;for(;r<t.length&&!n;){let e=t[r];"string"==typeof e&&!sd.has(e)&&tb(e).values.length&&(n=t[r]),r++}if(n&&i)for(let r of e)t[r]=sp(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eM[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let r=i.length-1,s=i[r];i[r]=eM[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let sm=[...se,tf,tT],sy=t=>sm.find(st(t)),sv={current:null},sg={current:!1},sb=new WeakMap,sx=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sw{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:r,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eL,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=O.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,f.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=rV(e),this.isVariantNode=rD(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&D(e)&&e.set(a[t],!1)}}mount(t){this.current=t,sb.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),sg.current||function(){if(sg.current=!0,rk.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>sv.current=t.matches;t.addListener(e),e()}else sv.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sv.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),m(this.notifyUpdate),m(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=b.has(t);n&&this.onBindTransform&&this.onBindTransform();let r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&f.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in rL){let e=rL[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iD()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<sx.length;e++){let i=sx[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let r=e[n],s=i[n];if(D(r))t.addValue(n,r);else if(D(s))t.addValue(n,A(r,{owner:t}));else if(s!==r)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(n);t.addValue(n,A(void 0!==e?e:r,{owner:t}))}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=A(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(sn(i)||ss(i))?i=parseFloat(i):!sy(i)&&tT.test(e)&&(i=sp(t,e)),this.setBaseTarget(t,D(i)?i.get():i)),D(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=o(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||D(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new T),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class sP extends sw{constructor(){super(...arguments),this.KeyframeResolver=sf}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;D(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function sT(t,{style:e,vars:i},n,r){for(let s in Object.assign(t.style,e,r&&r.getProjectionStyles(n)),i)t.style.setProperty(s,i[s])}class sS extends sP{constructor(){super(...arguments),this.type="html",this.renderInstance=sT}readValueFromInstance(t,e){if(b.has(e))return this.projection?.isProjecting?ew(e):eT(t,e);{let i=window.getComputedStyle(t),n=(z(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return i$(t,e)}build(t,e,i){rY(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return r4(t,e,i)}}let sO=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class sE extends sP{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iD}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(b.has(e)){let t=sc(e);return t&&t.default||0}return e=sO.has(e)?e:k(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return r6(t,e,i)}build(t,e,i){rG(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){for(let i in sT(t,e,void 0,n),e.attrs)t.setAttribute(sO.has(i)?i:k(i),e.attrs[i])}mount(t){this.isSVGTag=rQ(t.tagName),super.mount(t)}}let sj=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((eq={animation:{Feature:ic},exit:{Feature:id},inView:{Feature:rE},tap:{Feature:rx},focus:{Feature:rc},hover:{Feature:rh},pan:{Feature:i8},drag:{Feature:i9,ProjectionNode:ro,MeasureLayout:nu},layout:{ProjectionNode:ro,MeasureLayout:nu}},eK=(t,e)=>r2(t)?new sE(e):new sS(e,{allowProjection:t!==nt.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:n,createVisualElement:r,useRender:s,useVisualState:o,Component:a}=t;function l(t,e){var i,n,l;let u,h={...(0,nt.useContext)(rM.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,nt.useContext)(ni.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:c}=h,p=function(t){let{initial:e,animate:i}=function(t,e){if(rV(t)){let{initial:e,animate:i}=t;return{initial:!1===e||ie(e)?e:void 0,animate:ie(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,nt.useContext)(rA));return(0,nt.useMemo)(()=>({initial:e,animate:i}),[rC(e),rC(i)])}(t),d=o(t,c);if(!c&&rk.B){n=0,l=0,(0,nt.useContext)(rj).strict;let t=function(t){let{drag:e,layout:i}=rL;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(h);u=t.MeasureLayout,p.visualElement=function(t,e,i,n,r){let{visualElement:s}=(0,nt.useContext)(rA),o=(0,nt.useContext)(rj),a=(0,nt.useContext)(rI.t),l=(0,nt.useContext)(rM.Q).reducedMotion,u=(0,nt.useRef)(null);n=n||o.renderer,!u.current&&n&&(u.current=n(t,{visualState:e,parent:s,props:i,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let h=u.current,c=(0,nt.useContext)(nn);h&&!h.projection&&r&&("html"===h.type||"svg"===h.type)&&function(t,e,i,n){let{layoutId:r,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:s,alwaysMeasureLayout:!!o||a&&iY(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,crossfade:h,layoutScroll:l,layoutRoot:u})}(u.current,i,r,c);let p=(0,nt.useRef)(!1);(0,nt.useInsertionEffect)(()=>{h&&p.current&&h.update(i,a)});let d=i[R],f=(0,nt.useRef)(!!d&&!window.MotionHandoffIsComplete?.(d)&&window.MotionHasOptimisedAnimation?.(d));return(0,rF.E)(()=>{h&&(p.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),i7.render(h.render),f.current&&h.animationState&&h.animationState.animateChanges())}),(0,nt.useEffect)(()=>{h&&(!f.current&&h.animationState&&h.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(d)}),f.current=!1))}),h}(a,d,h,r,t.ProjectionNode)}return(0,i6.jsxs)(rA.Provider,{value:p,children:[u&&p.visualElement?(0,i6.jsx)(u,{visualElement:p.visualElement,...h}):null,s(a,t,(i=p.visualElement,(0,nt.useCallback)(t=>{t&&d.onMount&&d.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):iY(e)&&(e.current=t))},[i])),d,c,p.visualElement)]})}n&&function(t){for(let e in t)rL[e]={...rL[e],...t[e]}}(n),l.displayName="motion.".concat("string"==typeof a?a:"create(".concat(null!=(i=null!=(e=a.displayName)?e:a.name)?i:"",")"));let u=(0,nt.forwardRef)(l);return u[rB]=a,u}({...r2(t)?r7:r8,preloadedFeatures:eq,useRender:function(t=!1){return(e,i,n,{latestValues:r},s)=>{let o=(r2(e)?function(t,e,i,n){let r=(0,nt.useMemo)(()=>{let i=rZ();return rG(i,e,rQ(n),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};rX(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return rX(n,i,t),Object.assign(n,function({transformTemplate:t},e){return(0,nt.useMemo)(()=>{let i=rH();return rY(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(i,r,s,e),a=function(t,e,i){let n={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(r1(r)||!0===i&&r0(r)||!e&&!r0(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}(i,"string"==typeof e,t),l=e!==nt.Fragment?{...a,...o,ref:n}:{},{children:u}=i,h=(0,nt.useMemo)(()=>D(u)?u.get():u,[u]);return(0,nt.createElement)(e,{...l,children:h})}}(e),createVisualElement:eK,Component:t})}))},6983:(t,e,i)=>{"use strict";function n(t){return"object"==typeof t&&null!==t}i.d(e,{G:()=>n})},7104:(t,e,i)=>{(()=>{var e={296:(t,e,i)=>{var n=/^\s+|\s+$/g,r=/^[-+]0x[0-9a-f]+$/i,s=/^0b[01]+$/i,o=/^0o[0-7]+$/i,a=parseInt,l="object"==typeof i.g&&i.g&&i.g.Object===Object&&i.g,u="object"==typeof self&&self&&self.Object===Object&&self,h=l||u||Function("return this")(),c=Object.prototype.toString,p=Math.max,d=Math.min,f=function(){return h.Date.now()};function m(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function y(t){if("number"==typeof t)return t;if("symbol"==typeof(e=t)||e&&"object"==typeof e&&"[object Symbol]"==c.call(e))return NaN;if(m(t)){var e,i="function"==typeof t.valueOf?t.valueOf():t;t=m(i)?i+"":i}if("string"!=typeof t)return 0===t?t:+t;t=t.replace(n,"");var l=s.test(t);return l||o.test(t)?a(t.slice(2),l?2:8):r.test(t)?NaN:+t}t.exports=function(t,e,i){var n,r,s,o,a,l,u=0,h=!1,c=!1,v=!0;if("function"!=typeof t)throw TypeError("Expected a function");function g(e){var i=n,s=r;return n=r=void 0,u=e,o=t.apply(s,i)}function b(t){var i=t-l;return void 0===l||i>=e||i<0||c&&t-u>=s}function x(){var t,i=f();if(b(i))return w(i);a=setTimeout(x,(t=e-(i-l),c?d(t,s-(i-u)):t))}function w(t){return a=void 0,v&&n?g(t):(n=r=void 0,o)}function P(){var t,i=f(),s=b(i);if(n=arguments,r=this,l=i,s){if(void 0===a)return u=t=l,a=setTimeout(x,e),h?g(t):o;if(c)return a=setTimeout(x,e),g(l)}return void 0===a&&(a=setTimeout(x,e)),o}return e=y(e)||0,m(i)&&(h=!!i.leading,s=(c="maxWait"in i)?p(y(i.maxWait)||0,e):s,v="trailing"in i?!!i.trailing:v),P.cancel=function(){void 0!==a&&clearTimeout(a),u=0,n=l=r=a=void 0},P.flush=function(){return void 0===a?o:w(f())},P}},96:(t,e,i)=>{var n="Expected a function",r=NaN,s=/^\s+|\s+$/g,o=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,l=/^0o[0-7]+$/i,u=parseInt,h="object"==typeof i.g&&i.g&&i.g.Object===Object&&i.g,c="object"==typeof self&&self&&self.Object===Object&&self,p=h||c||Function("return this")(),d=Object.prototype.toString,f=Math.max,m=Math.min,y=function(){return p.Date.now()};function v(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function g(t){if("number"==typeof t)return t;if("symbol"==typeof(e=t)||e&&"object"==typeof e&&"[object Symbol]"==d.call(e))return r;if(v(t)){var e,i="function"==typeof t.valueOf?t.valueOf():t;t=v(i)?i+"":i}if("string"!=typeof t)return 0===t?t:+t;t=t.replace(s,"");var n=a.test(t);return n||l.test(t)?u(t.slice(2),n?2:8):o.test(t)?r:+t}t.exports=function(t,e,i){var r=!0,s=!0;if("function"!=typeof t)throw TypeError(n);return v(i)&&(r="leading"in i?!!i.leading:r,s="trailing"in i?!!i.trailing:s),function(t,e,i){var r,s,o,a,l,u,h=0,c=!1,p=!1,d=!0;if("function"!=typeof t)throw TypeError(n);function b(e){var i=r,n=s;return r=s=void 0,h=e,a=t.apply(n,i)}function x(t){var i=t-u;return void 0===u||i>=e||i<0||p&&t-h>=o}function w(){var t,i=y();if(x(i))return P(i);l=setTimeout(w,(t=e-(i-u),p?m(t,o-(i-h)):t))}function P(t){return l=void 0,d&&r?b(t):(r=s=void 0,a)}function T(){var t,i=y(),n=x(i);if(r=arguments,s=this,u=i,n){if(void 0===l)return h=t=u,l=setTimeout(w,e),c?b(t):a;if(p)return l=setTimeout(w,e),b(u)}return void 0===l&&(l=setTimeout(w,e)),a}return e=g(e)||0,v(i)&&(c=!!i.leading,o=(p="maxWait"in i)?f(g(i.maxWait)||0,e):o,d="trailing"in i?!!i.trailing:d),T.cancel=function(){void 0!==l&&clearTimeout(l),h=0,r=u=s=l=void 0},T.flush=function(){return void 0===l?a:P(y())},T}(t,e,{leading:r,maxWait:e,trailing:s})}},703:(t,e,i)=>{"use strict";var n=i(414);function r(){}function s(){}s.resetWarningCache=r,t.exports=function(){function t(t,e,i,r,s,o){if(o!==n){var a=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function e(){return t}t.isRequired=t;var i={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:s,resetWarningCache:r};return i.PropTypes=i,i}},697:(t,e,i)=>{t.exports=i(703)()},414:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"}},n={};function r(t){var i=n[t];if(void 0!==i)return i.exports;var s=n[t]={exports:{}};return e[t](s,s.exports,r),s.exports}r.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return r.d(e,{a:e}),e},r.d=(t,e)=>{for(var i in e)r.o(e,i)&&!r.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(t){if("object"==typeof window)return window}}(),r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var s={};(()=>{"use strict";r.r(s),r.d(s,{LazyLoadComponent:()=>U,LazyLoadImage:()=>G,trackWindowScroll:()=>V});let t=i(2115);var e=r.n(t),n=r(697);function o(){return"undefined"!=typeof window&&"IntersectionObserver"in window&&"isIntersecting"in window.IntersectionObserverEntry.prototype}function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,n)}return i}function u(t){var e=function(t,e){if("object"!==a(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,"string");if("object"!==a(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"===a(e)?e:String(e)}function h(t,e){return(h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function c(t){return(c=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var p=function(t){t.forEach(function(t){t.isIntersecting&&t.target.onVisible()})},d={},f=function(t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");s.prototype=Object.create(t&&t.prototype,{constructor:{value:s,writable:!0,configurable:!0}}),Object.defineProperty(s,"prototype",{writable:!1}),t&&h(s,t);var i,n,r=(n=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,e=c(s);t=n?Reflect.construct(e,arguments,c(this).constructor):e.apply(this,arguments);if(t&&("object"===a(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");if(void 0===this)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return this});function s(t){var e,i;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,s),(e=r.call(this,t)).supportsObserver=!t.scrollPosition&&t.useIntersectionObserver&&o(),e.supportsObserver&&(d[i=t.threshold]=d[i]||new IntersectionObserver(p,{rootMargin:i+"px"}),e.observer=d[i]),e}return i=[{key:"componentDidMount",value:function(){this.placeholder&&this.observer&&(this.placeholder.onVisible=this.props.onVisible,this.observer.observe(this.placeholder)),this.supportsObserver||this.updateVisibility()}},{key:"componentWillUnmount",value:function(){this.observer&&this.placeholder&&this.observer.unobserve(this.placeholder)}},{key:"componentDidUpdate",value:function(){this.supportsObserver||this.updateVisibility()}},{key:"getPlaceholderBoundingBox",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.props.scrollPosition,e=this.placeholder.getBoundingClientRect(),i=this.placeholder.style,n=parseInt(i.getPropertyValue("margin-left"),10)||0,r=parseInt(i.getPropertyValue("margin-top"),10)||0;return{bottom:t.y+e.bottom+r,left:t.x+e.left+n,right:t.x+e.right+n,top:t.y+e.top+r}}},{key:"isPlaceholderInViewport",value:function(){if("undefined"==typeof window||!this.placeholder)return!1;var t=this.props,e=t.scrollPosition,i=t.threshold,n=this.getPlaceholderBoundingBox(e),r=e.y+window.innerHeight,s=e.x,o=e.x+window.innerWidth;return!!(e.y-i<=n.bottom&&r+i>=n.top&&s-i<=n.right&&o+i>=n.left)}},{key:"updateVisibility",value:function(){this.isPlaceholderInViewport()&&this.props.onVisible()}},{key:"render",value:function(){var t=this,i=this.props,n=i.className,r=i.height,s=i.placeholder,o=i.style,a=i.width;if(s&&"function"!=typeof s.type)return e().cloneElement(s,{ref:function(e){return t.placeholder=e}});var h=function(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?l(Object(i),!0).forEach(function(e){var n,r;n=e,r=i[e],(n=u(n))in t?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):l(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}({display:"inline-block"},o);return void 0!==a&&(h.width=a),void 0!==r&&(h.height=r),e().createElement("span",{className:n,ref:function(e){return t.placeholder=e},style:h},s)}}],function(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,u(n.key),n)}}(s.prototype,i),Object.defineProperty(s,"prototype",{writable:!1}),s}(e().Component);f.propTypes={onVisible:n.PropTypes.func.isRequired,className:n.PropTypes.string,height:n.PropTypes.oneOfType([n.PropTypes.number,n.PropTypes.string]),placeholder:n.PropTypes.element,threshold:n.PropTypes.number,useIntersectionObserver:n.PropTypes.bool,scrollPosition:n.PropTypes.shape({x:n.PropTypes.number.isRequired,y:n.PropTypes.number.isRequired}),width:n.PropTypes.oneOfType([n.PropTypes.number,n.PropTypes.string])},f.defaultProps={className:"",placeholder:null,threshold:100,useIntersectionObserver:!0};var m=r(296),y=r.n(m),v=r(96),g=r.n(v),b=function(t){var e=getComputedStyle(t,null);return e.getPropertyValue("overflow")+e.getPropertyValue("overflow-y")+e.getPropertyValue("overflow-x")};let x=function(t){if(!(t instanceof HTMLElement))return window;for(var e=t;e&&e instanceof HTMLElement;){if(/(scroll|auto)/.test(b(e)))return e;e=e.parentNode}return window};function w(t){return(w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var P=["delayMethod","delayTime"];function T(){return(T=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t}).apply(this,arguments)}function S(t,e){return(S=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function O(t,e){if(e&&("object"===w(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return E(t)}function E(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function j(t){return(j=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var M=function(){return"undefined"==typeof window?0:window.scrollX||window.pageXOffset},A=function(){return"undefined"==typeof window?0:window.scrollY||window.pageYOffset};let V=function(t){var i=function(i){if("function"!=typeof i&&null!==i)throw TypeError("Super expression must either be null or a function");a.prototype=Object.create(i&&i.prototype,{constructor:{value:a,writable:!0,configurable:!0}}),Object.defineProperty(a,"prototype",{writable:!1}),i&&S(a,i);var n,r,s=(r=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,e=j(a);return t=r?Reflect.construct(e,arguments,j(this).constructor):e.apply(this,arguments),O(this,t)});function a(t){if(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,a),(i=s.call(this,t)).useIntersectionObserver=t.useIntersectionObserver&&o(),i.useIntersectionObserver)return O(i);var i,n=i.onChangeScroll.bind(E(i));return"debounce"===t.delayMethod?i.delayedScroll=y()(n,t.delayTime):"throttle"===t.delayMethod&&(i.delayedScroll=g()(n,t.delayTime)),i.state={scrollPosition:{x:M(),y:A()}},i.baseComponentRef=e().createRef(),i}return n=[{key:"componentDidMount",value:function(){this.addListeners()}},{key:"componentWillUnmount",value:function(){this.removeListeners()}},{key:"componentDidUpdate",value:function(){"undefined"==typeof window||this.useIntersectionObserver||x(this.baseComponentRef.current)!==this.scrollElement&&(this.removeListeners(),this.addListeners())}},{key:"addListeners",value:function(){"undefined"==typeof window||this.useIntersectionObserver||(this.scrollElement=x(this.baseComponentRef.current),this.scrollElement.addEventListener("scroll",this.delayedScroll,{passive:!0}),window.addEventListener("resize",this.delayedScroll,{passive:!0}),this.scrollElement!==window&&window.addEventListener("scroll",this.delayedScroll,{passive:!0}))}},{key:"removeListeners",value:function(){"undefined"==typeof window||this.useIntersectionObserver||(this.scrollElement.removeEventListener("scroll",this.delayedScroll),window.removeEventListener("resize",this.delayedScroll),this.scrollElement!==window&&window.removeEventListener("scroll",this.delayedScroll))}},{key:"onChangeScroll",value:function(){this.useIntersectionObserver||this.setState({scrollPosition:{x:M(),y:A()}})}},{key:"render",value:function(){var i=this.props,n=(i.delayMethod,i.delayTime,function(t,e){if(null==t)return{};var i,n,r=function(t,e){if(null==t)return{};var i,n,r={},s=Object.keys(t);for(n=0;n<s.length;n++)i=s[n],e.indexOf(i)>=0||(r[i]=t[i]);return r}(t,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);for(n=0;n<s.length;n++)i=s[n],e.indexOf(i)>=0||Object.prototype.propertyIsEnumerable.call(t,i)&&(r[i]=t[i])}return r}(i,P)),r=this.useIntersectionObserver?null:this.state.scrollPosition;return e().createElement(t,T({forwardRef:this.baseComponentRef,scrollPosition:r},n))}}],function(t,e){for(var i,n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,(i=function(t,e){if("object"!==w(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,"string");if("object"!==w(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(r.key),"symbol"===w(i)?i:String(i)),r)}}(a.prototype,n),Object.defineProperty(a,"prototype",{writable:!1}),a}(e().Component);return i.propTypes={delayMethod:n.PropTypes.oneOf(["debounce","throttle"]),delayTime:n.PropTypes.number,useIntersectionObserver:n.PropTypes.bool},i.defaultProps={delayMethod:"throttle",delayTime:300,useIntersectionObserver:!0},i};function D(t){return(D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function C(t,e){return(C=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function k(t){return(k=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}let R=V(function(t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");s.prototype=Object.create(t&&t.prototype,{constructor:{value:s,writable:!0,configurable:!0}}),Object.defineProperty(s,"prototype",{writable:!1}),t&&C(s,t);var i,n,r=(n=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,e=k(s);t=n?Reflect.construct(e,arguments,k(this).constructor):e.apply(this,arguments);if(t&&("object"===D(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");if(void 0===this)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return this});function s(t){return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,s),r.call(this,t)}return i=[{key:"render",value:function(){return e().createElement(f,this.props)}}],function(t,e){for(var i,n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,(i=function(t,e){if("object"!==D(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,"string");if("object"!==D(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(r.key),"symbol"===D(i)?i:String(i)),r)}}(s.prototype,i),Object.defineProperty(s,"prototype",{writable:!1}),s}(e().Component));function L(t){return(L="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function B(t,e){return(B=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function I(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function F(t){return(F=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var N=function(t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");s.prototype=Object.create(t&&t.prototype,{constructor:{value:s,writable:!0,configurable:!0}}),Object.defineProperty(s,"prototype",{writable:!1}),t&&B(s,t);var i,n,r=(n=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,e=F(s);t=n?Reflect.construct(e,arguments,F(this).constructor):e.apply(this,arguments);if(t&&("object"===L(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return I(this)});function s(t){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,s),e=r.call(this,t);var e,i=t.afterLoad,n=t.beforeLoad,o=t.scrollPosition,a=t.visibleByDefault;return e.state={visible:a},a&&(n(),i()),e.onVisible=e.onVisible.bind(I(e)),e.isScrollTracked=!!(o&&Number.isFinite(o.x)&&o.x>=0&&Number.isFinite(o.y)&&o.y>=0),e}return i=[{key:"componentDidUpdate",value:function(t,e){e.visible!==this.state.visible&&this.props.afterLoad()}},{key:"onVisible",value:function(){this.props.beforeLoad(),this.setState({visible:!0})}},{key:"render",value:function(){if(this.state.visible)return this.props.children;var t=this.props,i=t.className,n=t.delayMethod,r=t.delayTime,s=t.height,a=t.placeholder,l=t.scrollPosition,u=t.style,h=t.threshold,c=t.useIntersectionObserver,p=t.width;return this.isScrollTracked||c&&o()?e().createElement(f,{className:i,height:s,onVisible:this.onVisible,placeholder:a,scrollPosition:l,style:u,threshold:h,useIntersectionObserver:c,width:p}):e().createElement(R,{className:i,delayMethod:n,delayTime:r,height:s,onVisible:this.onVisible,placeholder:a,style:u,threshold:h,width:p})}}],function(t,e){for(var i,n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,(i=function(t,e){if("object"!==L(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,"string");if("object"!==L(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(r.key),"symbol"===L(i)?i:String(i)),r)}}(s.prototype,i),Object.defineProperty(s,"prototype",{writable:!1}),s}(e().Component);N.propTypes={afterLoad:n.PropTypes.func,beforeLoad:n.PropTypes.func,useIntersectionObserver:n.PropTypes.bool,visibleByDefault:n.PropTypes.bool},N.defaultProps={afterLoad:function(){return{}},beforeLoad:function(){return{}},useIntersectionObserver:!0,visibleByDefault:!1};let U=N;function _(t){return(_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var W=["afterLoad","beforeLoad","delayMethod","delayTime","effect","placeholder","placeholderSrc","scrollPosition","threshold","useIntersectionObserver","visibleByDefault","wrapperClassName","wrapperProps"];function $(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,n)}return i}function z(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?$(Object(i),!0).forEach(function(e){var n,r,s;n=t,r=e,s=i[e],(r=H(r))in n?Object.defineProperty(n,r,{value:s,enumerable:!0,configurable:!0,writable:!0}):n[r]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):$(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}function Y(){return(Y=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t}).apply(this,arguments)}function H(t){var e=function(t,e){if("object"!==_(t)||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,"string");if("object"!==_(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"===_(e)?e:String(e)}function X(t,e){return(X=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function q(t){return(q=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var K=function(t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");s.prototype=Object.create(t&&t.prototype,{constructor:{value:s,writable:!0,configurable:!0}}),Object.defineProperty(s,"prototype",{writable:!1}),t&&X(s,t);var i,n,r=(n=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,e=q(s);t=n?Reflect.construct(e,arguments,q(this).constructor):e.apply(this,arguments);if(t&&("object"===_(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");if(void 0===this)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return this});function s(t){var e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,s),(e=r.call(this,t)).state={loaded:!1},e}return i=[{key:"onImageLoad",value:function(){var t=this;return this.state.loaded?null:function(e){t.props.onLoad(e),t.props.afterLoad(),t.setState({loaded:!0})}}},{key:"getImg",value:function(){var t=this.props,i=(t.afterLoad,t.beforeLoad,t.delayMethod,t.delayTime,t.effect,t.placeholder,t.placeholderSrc,t.scrollPosition,t.threshold,t.useIntersectionObserver,t.visibleByDefault,t.wrapperClassName,t.wrapperProps,function(t,e){if(null==t)return{};var i,n,r=function(t,e){if(null==t)return{};var i,n,r={},s=Object.keys(t);for(n=0;n<s.length;n++)i=s[n],e.indexOf(i)>=0||(r[i]=t[i]);return r}(t,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);for(n=0;n<s.length;n++)i=s[n],e.indexOf(i)>=0||Object.prototype.propertyIsEnumerable.call(t,i)&&(r[i]=t[i])}return r}(t,W));return e().createElement("img",Y({},i,{onLoad:this.onImageLoad()}))}},{key:"getLazyLoadImage",value:function(){var t=this.props,i=t.beforeLoad,n=t.className,r=t.delayMethod,s=t.delayTime,o=t.height,a=t.placeholder,l=t.scrollPosition,u=t.style,h=t.threshold,c=t.useIntersectionObserver,p=t.visibleByDefault,d=t.width;return e().createElement(U,{beforeLoad:i,className:n,delayMethod:r,delayTime:s,height:o,placeholder:a,scrollPosition:l,style:u,threshold:h,useIntersectionObserver:c,visibleByDefault:p,width:d},this.getImg())}},{key:"getWrappedLazyLoadImage",value:function(t){var i=this.props,n=i.effect,r=i.height,s=i.placeholderSrc,o=i.width,a=i.wrapperClassName,l=i.wrapperProps,u=this.state.loaded;return e().createElement("span",Y({className:a+" lazy-load-image-background "+n+(u?" lazy-load-image-loaded":""),style:z(z({},u||!s?{}:{backgroundImage:"url(".concat(s,")"),backgroundSize:"100% 100%"}),{},{color:"transparent",display:"inline-block",height:r,width:o})},l),t)}},{key:"render",value:function(){var t=this.props,e=t.effect,i=t.placeholderSrc,n=t.visibleByDefault,r=t.wrapperClassName,s=t.wrapperProps,o=this.getLazyLoadImage();return(e||i)&&!n||r||s?this.getWrappedLazyLoadImage(o):o}}],function(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,H(n.key),n)}}(s.prototype,i),Object.defineProperty(s,"prototype",{writable:!1}),s}(e().Component);K.propTypes={onLoad:n.PropTypes.func,afterLoad:n.PropTypes.func,beforeLoad:n.PropTypes.func,delayMethod:n.PropTypes.string,delayTime:n.PropTypes.number,effect:n.PropTypes.string,placeholderSrc:n.PropTypes.string,threshold:n.PropTypes.number,useIntersectionObserver:n.PropTypes.bool,visibleByDefault:n.PropTypes.bool,wrapperClassName:n.PropTypes.string,wrapperProps:n.PropTypes.object},K.defaultProps={onLoad:function(){},afterLoad:function(){return{}},beforeLoad:function(){return{}},delayMethod:"throttle",delayTime:300,effect:"",placeholderSrc:null,threshold:100,useIntersectionObserver:!0,visibleByDefault:!1,wrapperClassName:""};let G=K})(),t.exports=s})()},7351:(t,e,i)=>{"use strict";i.d(e,{s:()=>r});var n=i(6983);function r(t){return(0,n.G)(t)&&"offsetHeight"in t}},7494:(t,e,i)=>{"use strict";i.d(e,{E:()=>r});var n=i(2115);let r=i(8972).B?n.useLayoutEffect:n.useEffect},8972:(t,e,i)=>{"use strict";i.d(e,{B:()=>n});let n="undefined"!=typeof window}}]);