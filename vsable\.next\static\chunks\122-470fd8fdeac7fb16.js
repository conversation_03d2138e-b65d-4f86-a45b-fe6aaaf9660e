"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[122],{283:(e,a,t)=>{t.d(a,{A:()=>c,AuthProvider:()=>d});var r=t(5155),s=t(2115),l=t(5695),i=t(3464);let o=(0,s.createContext)(void 0),n="http://localhost:5000/api",d=e=>{let{children:a}=e,[t,d]=(0,s.useState)(null),[c,m]=(0,s.useState)(!0),[u,g]=(0,s.useState)(null),h=(0,l.useRouter)();i.A.defaults.withCredentials=!0,(0,s.useEffect)(()=>{(async()=>{try{let e=await i.A.get("".concat(n,"/auth/me"));d(e.data.user)}catch(e){d(null)}finally{m(!1)}})()},[]);let p=async(e,a,t)=>{m(!0),g(null);try{let r=await i.A.post("".concat(n,"/auth/login"),{email:e,password:a,remember_me:t});d(r.data.user),"admin"===r.data.user.role?h.replace("/admin/dashboard"):"vendor"===r.data.user.role?h.replace("/vendor/dashboard"):h.replace("/")}catch(e){var r,s;throw g(i.A.isAxiosError(e)&&(null==(s=e.response)||null==(r=s.data)?void 0:r.message)||"Login failed"),e}finally{m(!1)}},x=async()=>{m(!0);try{await i.A.post("".concat(n,"/auth/logout")),d(null),h.replace("/login")}catch(t){var e,a;g(i.A.isAxiosError(t)&&(null==(a=t.response)||null==(e=a.data)?void 0:e.message)||"Logout failed")}finally{m(!1)}},y=async function(e,a,t){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"customer";m(!0),g(null);try{await i.A.post("".concat(n,"/auth/register"),{name:e,email:a,password:t,role:r}),h.replace("/login")}catch(e){var s,l;throw g(i.A.isAxiosError(e)&&(null==(l=e.response)||null==(s=l.data)?void 0:s.message)||"Registration failed"),e}finally{m(!1)}},b=async e=>{m(!0),g(null);try{await i.A.post("".concat(n,"/auth/send-otp"),{email:e})}catch(e){var a,t;throw g(i.A.isAxiosError(e)&&(null==(t=e.response)||null==(a=t.data)?void 0:a.message)||"Failed to send OTP"),e}finally{m(!1)}},v=async(e,a)=>{m(!0),g(null);try{return(await i.A.post("".concat(n,"/auth/verify-otp"),{email:e,otp:a})).data.user}catch(e){var t,r;throw g(i.A.isAxiosError(e)&&(null==(r=e.response)||null==(t=r.data)?void 0:t.message)||"Invalid OTP"),e}finally{m(!1)}},f=async e=>{m(!0),g(null);try{await i.A.post("".concat(n,"/auth/forgot-password"),{email:e})}catch(e){var a,t;throw g(i.A.isAxiosError(e)&&(null==(t=e.response)||null==(a=t.data)?void 0:a.message)||"Failed to send reset email"),e}finally{m(!1)}},w=async(e,a)=>{m(!0),g(null);try{await i.A.post("".concat(n,"/auth/reset-password"),{token:e,password:a})}catch(e){var t,r;throw g(i.A.isAxiosError(e)&&(null==(r=e.response)||null==(t=r.data)?void 0:t.message)||"Failed to reset password"),e}finally{m(!1)}},k=async e=>{m(!0),g(null);try{let a=await i.A.put("".concat(n,"/auth/profile"),e);d(a.data.user)}catch(e){var a,t;throw g(i.A.isAxiosError(e)&&(null==(t=e.response)||null==(a=t.data)?void 0:a.message)||"Failed to update profile"),e}finally{m(!1)}},j=async e=>{m(!0),g(null);try{await i.A.post("".concat(n,"/auth/send-verification-email"),{email:e})}catch(e){var a,t;throw g(i.A.isAxiosError(e)&&(null==(t=e.response)||null==(a=t.data)?void 0:a.message)||"Failed to send verification email"),e}finally{m(!1)}},A=async e=>{m(!0),g(null);try{let a=(await i.A.post("".concat(n,"/auth/verify-email"),{token:e})).data.user;return d(a),a}catch(e){var a,t;throw g(i.A.isAxiosError(e)&&(null==(t=e.response)||null==(a=t.data)?void 0:a.message)||"Email verification failed"),e}finally{m(!1)}};return(0,r.jsx)(o.Provider,{value:{user:t,loading:c,error:u,login:p,logout:x,register:y,sendOTP:b,verifyOTP:v,forgotPassword:f,resetPassword:w,updateProfile:k,sendVerificationEmail:j,verifyEmail:A,setUser:d,isAuthenticated:!!t},children:a})},c=()=>{let e=(0,s.useContext)(o);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},7398:(e,a,t)=>{t.d(a,{A:()=>b});var r=t(5155),s=t(2115),l=t(3464),i=t(6766),o=t(351),n=t(1821),d=t(7222),c=t(5280),m=t(9730),u=t(486),g=t(9714);let h=[{id:1,name:"Premium Coffee Beans",description:"Freshly roasted coffee beans from the mountains",price:24.99,image_url:"https://images.unsplash.com/photo-**********-641a0ac8b55e",imageUrl:"https://images.unsplash.com/photo-**********-641a0ac8b55e",category:"Beverages",tags:["coffee","premium","organic"],is_active:!0,store_id:1,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),contact_link:null},{id:2,name:"Artisan Chocolate",description:"Handcrafted chocolate made with finest ingredients",price:18.5,image_url:"https://images.unsplash.com/photo-**********-cb92caebd54b",imageUrl:"https://images.unsplash.com/photo-**********-cb92caebd54b",category:"Sweets",tags:["chocolate","artisan","handmade"],is_active:!0,store_id:1,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),contact_link:null},{id:3,name:"Organic Honey",description:"Pure organic honey from local beekeepers",price:12.99,image_url:"https://images.unsplash.com/photo-1587049352846-4a222e784d38",imageUrl:"https://images.unsplash.com/photo-1587049352846-4a222e784d38",category:"Natural",tags:["honey","organic","natural"],is_active:!0,store_id:1,created_at:new Date().toISOString(),updated_at:new Date().toISOString(),contact_link:null}],p={name:"Demo Store",description:"This is how your store will look with this template",logo_url:"https://images.unsplash.com/photo-1560472354-b33ff0c44a43",cover_image_url:"https://images.unsplash.com/photo-1441986300917-64674bd600d8",telegram:"@demostore",whatsapp:"+1234567890",phone:"+****************",instagram:"@demostore",view_count:1234},x=e=>{let{templateId:a,store:t,products:l=h,onClose:i,onSelect:x,isSelected:y=!1}=e,[b,v]=(0,s.useState)(!1),f={...p,...t,name:t.name||p.name,description:t.description||p.description,logo_url:t.logo_url||p.logo_url,cover_image_url:t.cover_image_url||p.cover_image_url},w=()=>{let e=["All",...Array.from(new Set(l.map(e=>e.category).filter(e=>"string"==typeof e&&""!==e.trim())))],t={store:f,products:l,searchQuery:"",selectedCategory:"All",handleContactClick:()=>{},onSearchQueryChange:()=>{},onSelectedCategoryChange:()=>{},categories:e,isPreview:!0};switch(a){case"default":default:return(0,r.jsx)(n.A,{...t});case"template1":return(0,r.jsx)(d.A,{...t});case"template2":return(0,r.jsx)(c.A,{...t});case"template3":return(0,r.jsx)(m.A,{...t});case"template4":return(0,r.jsx)(u.A,{...t});case"template5":return(0,r.jsx)(g.A,{...t})}};return b?(0,r.jsxs)("div",{className:"fixed inset-0 z-50 bg-white dark:bg-gray-900",children:[(0,r.jsx)("div",{className:"absolute top-4 right-4 z-10 flex space-x-2",children:(0,r.jsx)("button",{onClick:()=>v(!1),className:"p-2 bg-black dark:bg-white text-white dark:text-black rounded-full hover:bg-gray-800 dark:hover:bg-gray-200 transition-colors",children:(0,r.jsx)(o.yGN,{className:"h-5 w-5"})})}),(0,r.jsx)("div",{className:"h-full overflow-auto",children:w()})]}):(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden",children:[(0,r.jsxs)("div",{className:"p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:{default:"Classic Template",template1:"Modern Blue",template2:"Elegant Cyan",template3:"Professional Teal",template4:"Vibrant Purple",template5:"Minimal Slate"}[a]||"Unknown Template"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Preview how your store will look"})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>v(!0),className:"p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors",title:"Fullscreen preview",children:(0,r.jsx)(o.HaR,{className:"h-5 w-5"})}),(0,r.jsx)("button",{onClick:i,className:"p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors",children:(0,r.jsx)(o.yGN,{className:"h-5 w-5"})})]})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"h-96 overflow-hidden",children:(0,r.jsx)("div",{className:"transform scale-50 origin-top-left w-[200%] h-[200%]",children:w()})}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-all flex items-center justify-center opacity-0 hover:opacity-100",children:(0,r.jsxs)("button",{onClick:()=>v(!0),className:"bg-white dark:bg-gray-800 text-gray-900 dark:text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,r.jsx)(o.Vap,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Full Preview"})]})})]}),(0,r.jsx)("div",{className:"p-4 border-t border-gray-200 dark:border-gray-700",children:(0,r.jsx)("button",{onClick:()=>x(a),className:"w-full py-2 px-4 rounded-md font-medium transition-colors ".concat(y?"bg-black dark:bg-white text-white dark:text-black":"bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-600"),children:y?"Selected":"Select Template"})})]})},y=[{id:"template1",name:"Classic Clean",description:"A simple and elegant layout."},{id:"template2",name:"Modern Dark",description:"A sleek, contemporary dark theme."},{id:"template3",name:"Vibrant Grid",description:"A colorful, grid-based product display."},{id:"template4",name:"Minimalist Focus",description:"Emphasizes product imagery with minimal distractions."},{id:"template5",name:"Interactive Showcase",description:"Engaging animations and interactive elements."}],b=e=>{let{currentStore:a,onTemplateUpdate:t}=e,[n,d]=(0,s.useState)(null==a?void 0:a.selected_template_id),[c,m]=(0,s.useState)(!1),[u,g]=(0,s.useState)(null),[h,p]=(0,s.useState)(null),[b,v]=(0,s.useState)(null);(0,s.useEffect)(()=>{d((null==a?void 0:a.selected_template_id)||"template1")},[a]);let f=e=>{d(e)},w=async()=>{if(!n)return void g("Please select a template.");if(!a)return void g("Store data is not available.");m(!0),g(null),p(null);try{let e=await l.A.put("".concat("http://localhost:5000/api","/vendor/store/template"),{selected_template_id:n},{withCredentials:!0});e.data&&e.data.store?(t(e.data.store),p(e.data.message||"Template updated successfully!")):g(e.data.message||"Failed to update template. Invalid response.")}catch(a){var e,r;console.error("Error updating template:",a),g(l.A.isAxiosError(a)&&(null==(r=a.response)||null==(e=r.data)?void 0:e.message)||"An unknown error occurred.")}finally{m(!1)}};return(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 shadow sm:rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-white",children:"Store Appearance"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-600 dark:text-gray-400",children:"Choose a template that best represents your brand."}),u&&(0,r.jsx)("p",{className:"mt-2 text-sm text-red-600 dark:text-red-400",children:u}),h&&(0,r.jsx)("p",{className:"mt-2 text-sm text-green-600 dark:text-green-400",children:h}),(0,r.jsx)("div",{className:"mt-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6",children:y.map(e=>(0,r.jsxs)("div",{className:"rounded-lg border p-4 transition-all\n                        ".concat(n===e.id?"border-black dark:border-white ring-2 ring-black dark:ring-white bg-gray-50 dark:bg-gray-900":"border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500"),children:[e.previewImageUrl&&(0,r.jsx)("div",{className:"w-full h-32 bg-gray-200 dark:bg-gray-700 rounded-md mb-3 overflow-hidden",children:(0,r.jsx)(i.default,{src:e.previewImageUrl||"",alt:e.name,width:300,height:150,className:"w-full h-full object-cover"})}),(0,r.jsx)("h4",{className:"text-md font-semibold text-gray-800 dark:text-gray-100",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-3",children:e.description}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("button",{onClick:()=>v(e.id),className:"flex-1 flex items-center justify-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:[(0,r.jsx)(o.Vap,{className:"mr-2 h-4 w-4"}),"Preview"]}),(0,r.jsx)("button",{onClick:()=>f(e.id),className:"flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ".concat(n===e.id?"bg-black dark:bg-white text-white dark:text-black":"bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-600"),children:n===e.id?"Selected":"Select"})]})]},e.id))}),(0,r.jsx)("div",{className:"mt-8 flex justify-end",children:(0,r.jsx)("button",{type:"button",onClick:w,disabled:c||!n||n===(null==a?void 0:a.selected_template_id),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:c?"Saving...":"Save Template"})}),b&&a&&(0,r.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,r.jsxs)("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity",onClick:()=>v(null)}),(0,r.jsx)("span",{className:"hidden sm:inline-block sm:align-middle sm:h-screen",children:"​"}),(0,r.jsx)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full",children:(0,r.jsx)(x,{templateId:b,store:a,onClose:()=>v(null),onSelect:e=>{f(e),v(null)},isSelected:n===b})})]})})]})}},9053:(e,a,t)=>{t.d(a,{A:()=>o});var r=t(5155),s=t(2115),l=t(5695),i=t(283);let o=e=>{let{children:a,allowedRoles:t=[]}=e,{user:o,loading:n,isAuthenticated:d}=(0,i.A)(),c=(0,l.useRouter)();return((0,s.useEffect)(()=>{n||d?!n&&d&&t.length>0&&o&&!t.includes(o.role)&&("admin"===o.role?c.replace("/admin/dashboard"):"vendor"===o.role?c.replace("/vendor/dashboard"):c.replace("/")):c.replace("/login")},[n,d,o,c,t]),n)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):!d||t.length>0&&o&&!t.includes(o.role)?null:(0,r.jsx)(r.Fragment,{children:a})}}}]);