"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[462],{486:(e,t,s)=>{s.d(t,{A:()=>d});var a=s(5155),l=s(2115),r=s(6408),c=s(760),i=s(351),n=s(9911),o=s(7104),x=s(8125);let d=e=>{let{store:t,products:s,searchQuery:d,selectedCategory:m,handleContactClick:h,onSearchQueryChange:g,onSelectedCategoryChange:p}=e,[f,u]=(0,l.useState)(!1),[j,b]=(0,l.useState)(d),[N,w]=(0,l.useState)(m||"All");(0,l.useEffect)(()=>{b(d)},[d]),(0,l.useEffect)(()=>{w(m||"All")},[m]),(0,l.useEffect)(()=>{let e=()=>{u(window.scrollY>300)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);let v=["All",...Array.from(new Set(s.map(e=>e.category).filter(e=>"string"==typeof e&&""!==e.trim())))],y=e=>{b(e),g&&g(e)},k=e=>{w(e),p&&p(e)},_=s.filter(e=>{let t=e.name.toLowerCase().includes(j.toLowerCase())||e.description&&e.description.toLowerCase().includes(j.toLowerCase()),s="All"===N||e.category===N;return t&&s});return(0,a.jsxs)("div",{className:"font-sans bg-gray-50",children:[(0,a.jsx)("div",{className:"relative h-screen bg-cover bg-center",style:{backgroundImage:t.cover_image_url?"url('".concat((0,x.Dw)(t.cover_image_url),"')"):"url('https://images.unsplash.com/photo-1576426863848-c21f53c60b19')"},children:(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center text-center text-white",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-5xl md:text-7xl font-serif mb-4",children:t.name}),(0,a.jsx)("p",{className:"text-xl md:text-2xl font-light",children:t.description||"Discover our exclusive collection"})]})})}),(0,a.jsx)("section",{className:"py-20 bg-white text-center",children:(0,a.jsxs)("div",{className:"max-w-3xl mx-auto px-4",children:[(0,a.jsx)("h2",{className:"text-4xl font-serif mb-6",children:"About Us"}),(0,a.jsx)("p",{className:"text-lg text-gray-700",children:"We offer premium products that blend style and substance. Crafted with passion and precision, each piece reflects our commitment to excellence."})]})}),(0,a.jsx)("section",{className:"py-10 bg-gray-100",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto px-4",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"relative w-full md:w-1/2",children:[(0,a.jsx)(i.CKj,{className:"absolute left-3 top-3 text-gray-400"}),(0,a.jsx)("input",{type:"text",value:j,onChange:e=>y(e.target.value),placeholder:"Search products...",className:"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-gray-300"})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 justify-center md:justify-start",children:v.map(e=>(0,a.jsx)("button",{onClick:()=>k(e),className:"px-5 py-2 rounded-full text-sm capitalize ".concat(N===e?"bg-black text-white":"bg-white text-gray-700 hover:bg-gray-200"),children:e},e))})]}),(0,a.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:_.map(e=>(0,a.jsxs)(r.P.div,{layout:!0,initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"bg-white rounded-lg shadow hover:shadow-lg overflow-hidden",children:[(0,a.jsx)("div",{className:"relative pb-[100%]",children:(0,a.jsx)(o.LazyLoadImage,{src:e.image_url?(0,x.Dw)(e.image_url):"https://via.placeholder.com/300",alt:e.name,className:"absolute inset-0 w-full h-full object-cover transition-transform duration-300 hover:scale-105"})}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-1",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:e.description||"No description available."}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("span",{className:"text-black font-bold",children:["$",e.price.toFixed(2)]}),(0,a.jsx)("button",{onClick:()=>h(e.contact_link),className:"p-2 bg-gray-100 rounded-full hover:bg-gray-200",disabled:!e.contact_link,children:(0,a.jsx)(i.iHs,{className:"w-5 h-5"})})]})]})]},e.id))})]})}),(0,a.jsx)(c.N,{children:f&&(0,a.jsx)(r.P.button,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},onClick:()=>window.scrollTo({top:0,behavior:"smooth"}),className:"fixed bottom-8 right-8 bg-black text-white p-3 rounded-full shadow-lg z-50",children:(0,a.jsx)(i.wAb,{size:24})})}),(0,a.jsx)("footer",{className:"bg-black text-white py-10",children:(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold",children:t.name}),(0,a.jsxs)("div",{className:"flex justify-center space-x-4",children:[t.phone&&(0,a.jsx)("a",{href:"tel:".concat(t.phone),children:(0,a.jsx)(n.Cab,{})}),t.whatsapp&&(0,a.jsx)("a",{href:"https://wa.me/".concat(t.whatsapp.replace(/[^0-9]/g,"")),children:(0,a.jsx)(n.EcP,{})}),t.telegram&&(0,a.jsx)("a",{href:"https://t.me/".concat(t.telegram.replace("@","")),children:(0,a.jsx)(n.hFS,{})}),t.instagram&&(0,a.jsx)("a",{href:"https://instagram.com/".concat(t.instagram.replace("@","")),children:(0,a.jsx)(n.ao$,{})}),t.facebook&&(0,a.jsx)("a",{href:t.facebook.startsWith("https://")?t.facebook:"https://facebook.com/".concat(t.facebook.replace("@","")),children:(0,a.jsx)(n.iYk,{})}),t.tiktok&&(0,a.jsx)("a",{href:"https://tiktok.com/@".concat(t.tiktok.replace("@","")),children:(0,a.jsx)(n.kkU,{})})]}),(0,a.jsxs)("p",{className:"text-sm",children:["\xa9 ",new Date().getFullYear()," ",t.name,". All rights reserved."]})]})})]})}},1821:(e,t,s)=>{s.d(t,{A:()=>n});var a=s(5155),l=s(6766),r=s(351),c=s(9911),i=s(8125);let n=e=>{let{store:t,products:s,categories:n,searchQuery:o,selectedCategory:x,onSearchQueryChange:d,onSelectedCategoryChange:m,handleContactClick:h}=e;return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)("div",{className:"relative h-64 md:h-80 w-full bg-blue-600 dark:bg-blue-800",children:(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"h-24 w-24 relative rounded-full overflow-hidden border-4 border-white bg-white",children:t.logo_url?(0,a.jsx)(l.default,{src:(0,i.Dw)(t.logo_url),alt:"".concat(t.name," logo"),fill:!0,style:{objectFit:"cover"}}):(0,a.jsx)("div",{className:"h-full w-full flex items-center justify-center bg-gray-200",children:(0,a.jsx)(r.fZZ,{className:"h-12 w-12 text-gray-400"})})}),(0,a.jsxs)("div",{className:"ml-6",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white",children:t.name}),(0,a.jsxs)("p",{className:"text-sm text-white opacity-80",children:[t.view_count||0," views"]}),(0,a.jsxs)("div",{className:"flex items-center mt-2 space-x-4",children:[t.telegram&&(0,a.jsxs)("a",{href:t.telegram.startsWith("https://")?t.telegram:"https://t.me/".concat(t.telegram.replace("@","")),target:"_blank",rel:"noopener noreferrer",className:"text-white hover:text-blue-200 flex items-center",children:[(0,a.jsx)(c.hFS,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Telegram"})]}),t.whatsapp&&(0,a.jsxs)("a",{href:"https://wa.me/".concat(t.whatsapp.replace(/[^0-9]/g,"")),target:"_blank",rel:"noopener noreferrer",className:"text-white hover:text-blue-200 flex items-center",children:[(0,a.jsx)(c.EcP,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"WhatsApp"})]}),t.phone&&(0,a.jsxs)("a",{href:"tel:".concat(t.phone),className:"text-white hover:text-blue-200 flex items-center",children:[(0,a.jsx)(c.Cab,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Call"})]}),t.instagram&&(0,a.jsxs)("a",{href:"https://instagram.com/".concat(t.instagram.replace("@","")),target:"_blank",rel:"noopener noreferrer",className:"text-white hover:text-blue-200 flex items-center",children:[(0,a.jsx)(c.ao$,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Instagram"})]}),t.facebook&&(0,a.jsxs)("a",{href:t.facebook.startsWith("https://")?t.facebook:"https://facebook.com/".concat(t.facebook.replace("@","")),target:"_blank",rel:"noopener noreferrer",className:"text-white hover:text-blue-200 flex items-center",children:[(0,a.jsx)(c.iYk,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Facebook"})]}),t.tiktok&&(0,a.jsxs)("a",{href:"https://tiktok.com/@".concat(t.tiktok.replace("@","")),target:"_blank",rel:"noopener noreferrer",className:"text-white hover:text-blue-200 flex items-center",children:[(0,a.jsx)(c.kkU,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"TikTok"})]})]})]})]})})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[t.description&&(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-8",children:t.description}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between mb-8",children:[(0,a.jsxs)("div",{className:"relative mb-4 md:mb-0 md:w-1/2",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(r.CKj,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{type:"text",className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",placeholder:"Search products...",value:o,onChange:e=>d(e.target.value)})]}),(0,a.jsx)("div",{className:"flex overflow-x-auto space-x-2 pb-2",children:n.map(e=>(0,a.jsx)("button",{className:"px-4 py-2 rounded-full text-sm font-medium ".concat(x===e?"bg-blue-600 text-white":"bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600"),onClick:()=>m(e),children:e},e))})]}),0===s.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(r.CKj,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("h3",{className:"mt-2 text-lg font-medium text-gray-900 dark:text-white",children:"No products found"}),(0,a.jsx)("p",{className:"mt-1 text-gray-500 dark:text-gray-400",children:"Try adjusting your search or filter to find what you're looking for."})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:s.map(e=>(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden",children:[(0,a.jsx)("div",{className:"h-48 w-full relative",children:e.image_url?(0,a.jsx)(l.default,{src:(0,i.Dw)(e.image_url),alt:e.name,fill:!0,sizes:"(max-width: 640px) 100vw, (max-width: 1024px) 50vw, (max-width: 1280px) 33vw, 25vw",style:{objectFit:"cover"}}):(0,a.jsx)("div",{className:"h-full w-full flex items-center justify-center bg-gray-200 dark:bg-gray-700",children:(0,a.jsx)(r.fZZ,{className:"h-12 w-12 text-gray-400"})})}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e.name}),(0,a.jsxs)("span",{className:"text-blue-600 dark:text-blue-400 font-bold",children:["$",e.price.toFixed(2)]})]}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:e.description||"No description"}),e.category&&(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",children:e.category})}),e.tags&&e.tags.length>0&&(0,a.jsx)("div",{className:"mt-2 flex flex-wrap gap-1",children:e.tags.map((e,t)=>(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",children:[(0,a.jsx)(r.cnX,{className:"mr-1 h-3 w-3"}),e]},t))}),(0,a.jsxs)("button",{onClick:()=>h(e.contact_link),disabled:!e.contact_link,className:"mt-4 w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)(c.hFS,{className:"mr-2 h-4 w-4"})," ","Contact Vendor"]})]})]},e.id))})]})]})}},5280:(e,t,s)=>{s.d(t,{A:()=>x});var a=s(5155),l=s(6766),r=s(351),c=s(9911),i=s(2115),n=s(6408),o=s(8125);let x=e=>{let{store:t,products:s,searchQuery:x,selectedCategory:d,handleContactClick:m,onSearchQueryChange:h,onSelectedCategoryChange:g}=e,[p,f]=(0,i.useState)(!0),[u,j]=(0,i.useState)(x),[b,N]=(0,i.useState)(d||"All");(0,i.useEffect)(()=>{j(x)},[x]),(0,i.useEffect)(()=>{N(d||"All")},[d]);let w=["All",...Array.from(new Set(s.map(e=>e.category).filter(e=>"string"==typeof e&&""!==e.trim())))],v=e=>{j(e),h&&h(e)},y=e=>{N(e),g&&g(e)},k=s.filter(e=>{let t=e.name.toLowerCase().includes(u.toLowerCase())||e.description&&e.description.toLowerCase().includes(u.toLowerCase()),s="All"===b||e.category===b;return t&&s});return(0,a.jsx)("div",{className:"font-sans",children:p?(0,a.jsx)(()=>(0,a.jsxs)("div",{className:"min-h-screen bg-gray-900 text-white font-sans",children:[(0,a.jsx)("div",{className:"relative h-80 md:h-96 w-full bg-cover bg-center",style:{backgroundImage:t.cover_image_url?"url('".concat((0,o.Dw)(t.cover_image_url),"')"):void 0},children:(0,a.jsx)("div",{className:"absolute inset-0 bg-black/40 flex items-center",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"h-20 w-20 relative rounded-full overflow-hidden border-4 border-white bg-white",children:t.logo_url?(0,a.jsx)(l.default,{src:t.logo_url?(0,o.Dw)(t.logo_url):"https://via.placeholder.com/300",alt:"".concat(t.name," logo"),fill:!0,sizes:"80px",style:{objectFit:"cover"}}):(0,a.jsx)("div",{className:"h-full w-full flex items-center justify-center bg-gray-700",children:(0,a.jsx)(r.fZZ,{className:"h-10 w-10 text-gray-400"})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("h1",{className:"text-4xl md:text-5xl font-light",children:t.name}),(0,a.jsx)("p",{className:"mt-1 text-lg text-gray-300",children:t.description||"Discover the latest fashion trends"})]})]}),(0,a.jsxs)("div",{className:"flex items-center mt-4 md:mt-0 space-x-4",children:[t.telegram&&(0,a.jsxs)("a",{href:t.telegram.startsWith("https://")?t.telegram:"https://t.me/".concat(t.telegram.replace("@","")),target:"_blank",rel:"noopener noreferrer",className:"text-gray-300 hover:text-white flex items-center",children:[(0,a.jsx)(c.hFS,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Telegram"})]}),t.whatsapp&&(0,a.jsxs)("a",{href:"https://wa.me/".concat(t.whatsapp.replace(/[^0-9]/g,"")),target:"_blank",rel:"noopener noreferrer",className:"text-gray-300 hover:text-white flex items-center",children:[(0,a.jsx)(c.EcP,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"WhatsApp"})]}),t.phone&&(0,a.jsxs)("a",{href:"tel:".concat(t.phone),className:"text-gray-300 hover:text-white flex items-center",children:[(0,a.jsx)(c.Cab,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Call"})]}),t.instagram&&(0,a.jsxs)("a",{href:"https://instagram.com/".concat(t.instagram.replace("@","")),target:"_blank",rel:"noopener noreferrer",className:"text-gray-300 hover:text-white flex items-center",children:[(0,a.jsx)(c.ao$,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Instagram"})]}),t.facebook&&(0,a.jsxs)("a",{href:t.facebook.startsWith("https://")?t.facebook:"https://facebook.com/".concat(t.facebook.replace("@","")),target:"_blank",rel:"noopener noreferrer",className:"text-gray-300 hover:text-white flex items-center",children:[(0,a.jsx)(c.iYk,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Facebook"})]}),t.tiktok&&(0,a.jsxs)("a",{href:"https://tiktok.com/@".concat(t.tiktok.replace("@","")),target:"_blank",rel:"noopener noreferrer",className:"text-gray-300 hover:text-white flex items-center",children:[(0,a.jsx)(c.kkU,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"TikTok"})]})]})]})})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center mb-12 gap-4",children:[(0,a.jsx)("h2",{className:"text-3xl font-light text-white mb-4 md:mb-0",children:"Our Collection"}),(0,a.jsxs)("div",{className:"relative w-full md:w-1/2",children:[(0,a.jsx)(r.CKj,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,a.jsx)("input",{type:"text",className:"w-full pl-10 pr-4 py-2 border border-gray-700 rounded-full bg-gray-800 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500",placeholder:"Search clothing items...",value:u,onChange:e=>v(e.target.value)})]})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-4 mb-8",children:w.map(e=>(0,a.jsx)("button",{className:"px-6 py-2 rounded-full text-sm font-medium transition-colors capitalize ".concat(b===e?"bg-cyan-500 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"),onClick:()=>y(e),children:e},e))}),0===k.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(r.CKj,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("h3",{className:"mt-2 text-lg font-medium text-white",children:"No items found"}),(0,a.jsx)("p",{className:"mt-1 text-gray-400",children:"Try adjusting your search or filter to find what you're looking for."})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:k.map(e=>(0,a.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:"bg-gray-800 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow",children:[(0,a.jsx)("div",{className:"relative pb-[100%]",children:e.image_url?(0,a.jsx)(l.default,{src:e.image_url?(0,o.Dw)(e.image_url):"https://via.placeholder.com/300",alt:e.name,fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw",style:{objectFit:"cover"}}):(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-700",children:(0,a.jsx)(r.fZZ,{className:"h-16 w-16 text-gray-500"})})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-white mb-2 truncate",children:e.name}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-4 line-clamp-2",children:e.description||"No description"}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("span",{className:"text-white font-medium",children:["$",e.price.toFixed(2)]}),(0,a.jsx)("button",{onClick:()=>m(e.contact_link),disabled:!e.contact_link,className:"p-2 rounded-full bg-gray-700 hover:bg-gray-600 text-white transition-colors disabled:opacity-50",children:(0,a.jsx)(r.iHs,{className:"h-5 w-5"})})]}),e.category&&(0,a.jsxs)("span",{className:"mt-3 inline-flex px-2 py-1 text-xs rounded-full bg-cyan-700 text-cyan-200",children:[(0,a.jsx)(r.cnX,{className:"mr-1 h-3 w-3"})," ",e.category]}),e.tags&&e.tags.length>0&&(0,a.jsx)("div",{className:"mt-3 flex flex-wrap gap-1",children:e.tags.map((e,t)=>(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-700 text-gray-300",children:[(0,a.jsx)(r.cnX,{className:"mr-1 h-3 w-3"}),e]},t))})]})]},e.id))})]}),(0,a.jsx)("footer",{className:"bg-gray-800 py-8 mt-12 border-t border-gray-700",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-gray-300",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold",children:t.name}),(0,a.jsxs)("div",{className:"mt-4 flex flex-wrap justify-center gap-4",children:[t.phone&&(0,a.jsxs)("p",{children:["Phone: ",(0,a.jsx)("a",{href:"tel:".concat(t.phone),className:"hover:text-cyan-400",children:t.phone})]}),t.whatsapp&&(0,a.jsxs)("p",{children:["WhatsApp: ",(0,a.jsx)("a",{href:"https://wa.me/".concat(t.whatsapp.replace(/[^0-9]/g,"")),className:"hover:text-cyan-400",children:"Message Us"})]}),t.instagram&&(0,a.jsxs)("p",{children:["Instagram: ",(0,a.jsxs)("a",{href:"https://instagram.com/".concat(t.instagram.replace("@","")),className:"hover:text-cyan-400",children:["@",t.instagram.replace("@","")]})]}),t.telegram&&(0,a.jsxs)("p",{children:["Telegram: ",(0,a.jsx)("a",{href:t.telegram.startsWith("https://")?t.telegram:"https://t.me/".concat(t.telegram.replace("@","")),className:"hover:text-cyan-400",children:"Contact Us"})]}),t.facebook&&(0,a.jsxs)("p",{children:["Facebook: ",(0,a.jsx)("a",{href:t.facebook.startsWith("https://")?t.facebook:"https://facebook.com/".concat(t.facebook.replace("@","")),className:"hover:text-cyan-400",children:"Follow Us"})]}),t.tiktok&&(0,a.jsxs)("p",{children:["TikTok: ",(0,a.jsxs)("a",{href:"https://tiktok.com/@".concat(t.tiktok.replace("@","")),className:"hover:text-cyan-400",children:["@",t.tiktok.replace("@","")]})]})]}),(0,a.jsxs)("p",{className:"mt-4",children:["\xa9 ",new Date().getFullYear()," ",t.name,". All rights reserved."]})]})})]}),{}):(0,a.jsx)(()=>(0,a.jsxs)("div",{className:"h-screen relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-cover bg-center",style:{backgroundImage:t.cover_image_url?"url('".concat((0,o.Dw)(t.cover_image_url),"')"):void 0},children:(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-60"})}),(0,a.jsxs)("div",{className:"relative h-full flex flex-col items-center justify-center text-white",children:[(0,a.jsx)("h1",{className:"text-6xl font-bold mb-2",children:t.name}),(0,a.jsxs)("p",{className:"text-lg mb-2",children:[t.view_count||0," views"]}),(0,a.jsx)("p",{className:"text-xl mb-8 max-w-xl text-center",children:t.description||"Discover our exclusive collection"}),(0,a.jsx)(n.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>f(!0),className:"px-8 py-3 bg-cyan-500 text-white rounded-full hover:bg-cyan-600 transition-all",children:"Explore Collection"})]})]}),{})})}},7222:(e,t,s)=>{s.d(t,{A:()=>o});var a=s(5155),l=s(6766),r=s(351),c=s(9911),i=s(2115),n=s(8125);let o=e=>{let{store:t,products:s,searchQuery:o,selectedCategory:x,handleContactClick:d,onSearchQueryChange:m,onSelectedCategoryChange:h}=e,[g,p]=(0,i.useState)(!0),[f,u]=(0,i.useState)(o),[j,b]=(0,i.useState)(x||"All");(0,i.useEffect)(()=>{u(o)},[o]),(0,i.useEffect)(()=>{b(x||"All")},[x]);let N=["All",...Array.from(new Set(s.map(e=>e.category).filter(e=>"string"==typeof e&&""!==e.trim())))],w=e=>{u(e),m&&m(e)},v=e=>{b(e),h&&h(e)},y=s.filter(e=>{let t=e.name.toLowerCase().includes(f.toLowerCase())||e.description&&e.description.toLowerCase().includes(f.toLowerCase()),s="All"===j||e.category===j;return t&&s});return(0,a.jsx)("div",{className:"font-sans",children:g?(0,a.jsx)(()=>(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)("div",{className:"relative h-64 md:h-80 w-full bg-black dark:bg-gray-800",children:(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"h-24 w-24 relative rounded-full overflow-hidden border-4 border-white bg-white",children:t.logo_url?(0,a.jsx)(l.default,{src:t.logo_url?(0,n.Dw)(t.logo_url):"https://via.placeholder.com/300",alt:"".concat(t.name," logo"),fill:!0,sizes:"96px",style:{objectFit:"cover"}}):(0,a.jsx)("div",{className:"h-full w-full flex items-center justify-center bg-gray-200",children:(0,a.jsx)(r.fZZ,{className:"h-12 w-12 text-gray-400"})})}),(0,a.jsxs)("div",{className:"ml-6",children:[(0,a.jsx)("h1",{className:"text-3xl md:text-4xl font-bold text-white",children:t.name}),(0,a.jsxs)("div",{className:"flex items-center mt-3 space-x-4",children:[t.telegram&&(0,a.jsxs)("a",{href:t.telegram.startsWith("https://")?t.telegram:"https://t.me/".concat(t.telegram.replace("@","")),target:"_blank",rel:"noopener noreferrer",className:"text-white hover:text-gray-200 flex items-center",children:[(0,a.jsx)(c.hFS,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Telegram"})]}),t.whatsapp&&(0,a.jsxs)("a",{href:"https://wa.me/".concat(t.whatsapp.replace(/[^0-9]/g,"")),target:"_blank",rel:"noopener noreferrer",className:"text-white hover:text-gray-200 flex items-center",children:[(0,a.jsx)(c.EcP,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"WhatsApp"})]}),t.phone&&(0,a.jsxs)("a",{href:"tel:".concat(t.phone),className:"text-white hover:text-gray-200 flex items-center",children:[(0,a.jsx)(c.Cab,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Call"})]}),t.instagram&&(0,a.jsxs)("a",{href:"https://instagram.com/".concat(t.instagram.replace("@","")),target:"_blank",rel:"noopener noreferrer",className:"text-white hover:text-gray-200 flex items-center",children:[(0,a.jsx)(c.ao$,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Instagram"})]}),t.facebook&&(0,a.jsxs)("a",{href:t.facebook.startsWith("https://")?t.facebook:"https://facebook.com/".concat(t.facebook.replace("@","")),target:"_blank",rel:"noopener noreferrer",className:"text-white hover:text-gray-200 flex items-center",children:[(0,a.jsx)(c.iYk,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Facebook"})]}),t.tiktok&&(0,a.jsxs)("a",{href:"https://tiktok.com/@".concat(t.tiktok.replace("@","")),target:"_blank",rel:"noopener noreferrer",className:"text-white hover:text-gray-200 flex items-center",children:[(0,a.jsx)(c.kkU,{className:"h-5 w-5 mr-1"}),(0,a.jsx)("span",{className:"text-sm",children:"TikTok"})]})]})]})]})})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[t.description&&(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-8 text-center max-w-2xl mx-auto",children:t.description}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center mb-8 gap-4",children:[(0,a.jsxs)("div",{className:"relative w-full md:w-1/2",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(r.CKj,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{type:"text",className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-black focus:border-black",placeholder:"Search menu items...",value:f,onChange:e=>w(e.target.value)})]}),(0,a.jsx)("div",{className:"flex overflow-x-auto space-x-2 pb-2",children:N.map(e=>(0,a.jsx)("button",{className:"px-4 py-2 rounded-full text-sm font-medium transition-colors ".concat(j===e?"bg-black text-white":"bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600"),onClick:()=>v(e),children:e},e))})]}),0===y.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(r.CKj,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("h3",{className:"mt-2 text-lg font-medium text-gray-900 dark:text-white",children:"No items found"}),(0,a.jsx)("p",{className:"mt-1 text-gray-500 dark:text-gray-400",children:"Try adjusting your search or filter to find what you're looking for."})]}):(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:y.map(e=>(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden transform hover:scale-105 transition-transform duration-300",children:[(0,a.jsx)("div",{className:"relative pb-[100%] overflow-hidden",children:e.image_url?(0,a.jsx)(l.default,{src:e.image_url?(0,n.Dw)(e.image_url):"https://via.placeholder.com/300",alt:e.name,fill:!0,sizes:"(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw",style:{objectFit:"cover"},className:"transform transition-transform duration-300 hover:scale-110"}):(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-200 dark:bg-gray-700",children:(0,a.jsx)(r.fZZ,{className:"h-12 w-12 text-gray-400"})})}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsx)("h3",{className:"text-sm sm:text-lg font-semibold text-gray-900 dark:text-white",children:e.name}),(0,a.jsxs)("span",{className:"text-sm sm:text-base text-black dark:text-white font-bold",children:["$",e.price.toFixed(2)]})]}),(0,a.jsx)("p",{className:"mt-2 text-xs sm:text-sm text-gray-600 dark:text-gray-400 line-clamp-2",children:e.description||"No description"}),e.category&&(0,a.jsx)("div",{className:"mt-3",children:(0,a.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",children:e.category})}),e.tags&&e.tags.length>0&&(0,a.jsx)("div",{className:"mt-2 flex flex-wrap gap-1",children:e.tags.map((e,t)=>(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",children:[(0,a.jsx)(r.cnX,{className:"mr-1 h-3 w-3"}),e]},t))}),(0,a.jsxs)("button",{onClick:()=>d(e.contact_link),disabled:!e.contact_link,className:"mt-4 w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,a.jsx)(r.iHs,{className:"mr-2 h-4 w-4"}),"Order Now"]})]})]},e.id))})]}),(0,a.jsx)("footer",{className:"bg-gray-800 dark:bg-gray-900 py-8 mt-12",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-gray-300",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold",children:t.name}),(0,a.jsxs)("div",{className:"mt-4 flex flex-wrap justify-center gap-4",children:[t.phone&&(0,a.jsxs)("p",{children:["Phone: ",(0,a.jsx)("a",{href:"tel:".concat(t.phone),className:"hover:text-gray-400",children:t.phone})]}),t.whatsapp&&(0,a.jsxs)("p",{children:["WhatsApp: ",(0,a.jsx)("a",{href:"https://wa.me/".concat(t.whatsapp.replace(/[^0-9]/g,"")),className:"hover:no-underline hover:text-gray-400",children:"Message Us"})]}),t.instagram&&(0,a.jsxs)("p",{children:["Instagram: ",(0,a.jsxs)("a",{href:"https://instagram.com/".concat(t.instagram.replace("@","")),className:"hover:no-underline hover:text-gray-400",children:["@",t.instagram.replace("@","")]})]}),t.telegram&&(0,a.jsxs)("p",{children:["Telegram: ",(0,a.jsx)("a",{href:t.telegram.startsWith("https://")?t.telegram:"https://t.me/".concat(t.telegram.replace("@","")),className:"hover:no-underline hover:text-gray-400",children:"Contact Us"})]}),t.facebook&&(0,a.jsxs)("p",{children:["Facebook: ",(0,a.jsx)("a",{href:t.facebook.startsWith("https://")?t.facebook:"https://facebook.com/".concat(t.facebook.replace("@","")),className:"hover:no-underline hover:text-gray-400",children:"Follow Us"})]}),t.tiktok&&(0,a.jsxs)("p",{children:["TikTok: ",(0,a.jsxs)("a",{href:"https://tiktok.com/@".concat(t.tiktok.replace("@","")),className:"hover:no-underline hover:text-gray-400",children:["@",t.tiktok.replace("@","")]})]})]}),(0,a.jsxs)("p",{className:"mt-4",children:["\xa9 ",new Date().getFullYear()," ",t.name,". All rights reserved."]})]})})]}),{}):(0,a.jsx)(()=>(0,a.jsxs)("div",{className:"h-screen relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-cover bg-center",style:{backgroundImage:t.cover_image_url?"url('".concat((0,n.Dw)(t.cover_image_url),"')"):"url('https://images.unsplash.com/photo-1441986300917-64674bd600d8')"},children:(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-40"})}),(0,a.jsxs)("div",{className:"relative h-full flex flex-col items-center justify-center text-white",children:[(0,a.jsx)("h1",{className:"text-6xl font-light mb-2",children:t.name}),(0,a.jsxs)("p",{className:"text-lg mb-2",children:[t.view_count||0," views"]}),(0,a.jsx)("p",{className:"text-xl mb-8",children:t.description||"Welcome to our store"}),(0,a.jsx)("button",{onClick:()=>p(!0),className:"px-8 py-3 bg-white text-gray-900 rounded-full hover:bg-opacity-90 transition-all",children:"View Products"})]})]}),{})})}},8125:(e,t,s)=>{s.d(t,{Dw:()=>r});let a="http://localhost:5000/api".replace(/\/api$/,""),l=("https://**************:9000".replace(/^http:/,"https:"),"".concat(a,"/api/upload/serve"));function r(e){if(!e)return"";let t="";if(e.includes("**************:9000")){let s=e.split("/").pop();if(s)return c(e,t="".concat(l,"/").concat(s),"MinIO direct URL converted to proxy"),t}if((e.startsWith("http://")||e.startsWith("https://"))&&!e.includes("**************:9000"))return t=e,c(e,t,"External absolute URL"),t;if(e.startsWith("//"))return t="https:".concat(e),c(e,t,"Protocol-relative URL fixed"),t;if(e.startsWith("uploads/")||e.startsWith("images/")){let s=e.split("/").pop();return s&&s.match(/[0-9a-f]{32}_/)?c(e,t="".concat(l,"/").concat(s),"Path with UUID pattern"):(t="/".concat(e),c(e,t,"Added leading slash for Next.js")),t}let s=e.startsWith("/")?e:e.replace(/^\/+/,"");if(s.match(/[0-9a-f]{32}_/)){let a=s.split("/").pop();return c(e,t="".concat(l,"/").concat(a),"UUID-based filename"),t}if(s.includes("uploads/")){let a=s.split("/").pop();return c(e,t="".concat(l,"/").concat(a),"Uploads directory"),t}return c(e,t=s.startsWith("/")?s:"/".concat(s),"Ensured leading slash for Next.js"),t}function c(e,t){arguments.length>2&&void 0!==arguments[2]&&arguments[2]}},9714:(e,t,s)=>{s.d(t,{A:()=>n});var a=s(5155),l=s(6766),r=s(351),c=s(2115),i=s(8125);let n=e=>{let{store:t,products:s,handleContactClick:n}=e,[o,x]=(0,c.useState)(!0),[d,m]=(0,c.useState)(s.length>0?s[0]:null);return(0,a.jsx)("div",{className:"font-sans",children:o?(0,a.jsx)(()=>(0,a.jsxs)("div",{className:"bg-slate-50 dark:bg-slate-900 text-slate-800 dark:text-slate-200 min-h-screen",children:[(0,a.jsxs)("header",{className:"container mx-auto px-4 py-8 md:flex md:items-center md:justify-between",children:[(0,a.jsxs)("div",{children:[t.logo_url&&(0,a.jsx)(l.default,{src:t.logo_url?(0,i.Dw)(t.logo_url):"https://via.placeholder.com/300",alt:"".concat(t.name," logo"),width:50,height:50,className:"mb-2 md:mb-0 rounded-md object-contain"}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-slate-900 dark:text-white",children:t.name}),(0,a.jsx)("p",{className:"text-slate-600 dark:text-slate-400",children:t.description})]}),(0,a.jsxs)("nav",{className:"mt-4 md:mt-0",children:[(0,a.jsx)("a",{href:"#products",className:"text-slate-700 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2",children:"Products"}),(0,a.jsx)("a",{href:"#contact",className:"text-slate-700 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2",children:"Contact"})]})]}),d&&(0,a.jsx)("section",{className:"bg-slate-100 dark:bg-slate-800 py-12 md:py-20",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 md:flex md:items-center gap-8",children:[(0,a.jsx)("div",{className:"md:w-1/2 h-80 md:h-96 relative rounded-lg overflow-hidden shadow-lg bg-slate-200 dark:bg-slate-700",children:d.imageUrl?(0,a.jsx)(l.default,{src:d.imageUrl?(0,i.Dw)(d.imageUrl):"https://via.placeholder.com/300",alt:d.name,fill:!0,style:{objectFit:"cover"}}):(0,a.jsx)(r.fZZ,{className:"w-24 h-24 text-slate-400 m-auto"})}),(0,a.jsxs)("div",{className:"md:w-1/2 mt-6 md:mt-0",children:[(0,a.jsx)("span",{className:"text-sm font-semibold text-blue-600 dark:text-blue-400 uppercase",children:"Featured Product"}),(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-extrabold text-slate-900 dark:text-white mt-2",children:d.name}),(0,a.jsx)("p",{className:"text-slate-600 dark:text-slate-300 mt-4 text-lg",children:d.description}),(0,a.jsxs)("p",{className:"text-4xl font-bold text-slate-800 dark:text-slate-100 mt-6",children:["$",d.price.toFixed(2)]}),d.contact_link&&(0,a.jsxs)("button",{onClick:()=>n(d.contact_link),className:"mt-8 px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-lg font-semibold flex items-center",children:[(0,a.jsx)(r.iHs,{className:"mr-2"})," Get This Deal"]})]})]})}),(0,a.jsxs)("main",{id:"products",className:"container mx-auto px-4 py-12",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold text-slate-700 dark:text-slate-200 mb-8 text-center",children:"More From Us"}),0===s.length?(0,a.jsx)("p",{className:"text-slate-500 dark:text-slate-400 text-center",children:"No other products to show."}):(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:s.map(e=>(0,a.jsxs)("div",{className:"bg-white dark:bg-slate-800 rounded-lg shadow-md overflow-hidden cursor-pointer transition-all hover:shadow-xl ".concat((null==d?void 0:d.id)===e.id?"ring-2 ring-blue-500":""),onClick:()=>m(e),children:[(0,a.jsx)("div",{className:"w-full h-40 relative bg-slate-200 dark:bg-slate-700",children:e.imageUrl?(0,a.jsx)(l.default,{src:e.imageUrl?(0,i.Dw)(e.imageUrl):"https://via.placeholder.com/300",alt:e.name,fill:!0,sizes:"(max-width: 640px) 100vw, (max-width: 1024px) 50vw, (max-width: 1280px) 33vw, 25vw",style:{objectFit:"cover"}}):(0,a.jsx)(r.fZZ,{className:"w-12 h-12 text-slate-400 m-auto"})}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)("h3",{className:"text-md font-semibold text-slate-900 dark:text-white truncate",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-slate-500 dark:text-slate-400",children:["$",e.price.toFixed(2)]})]})]},e.id))})]}),(0,a.jsxs)("footer",{id:"contact",className:"bg-slate-200 dark:bg-slate-800 py-10 mt-12 text-center",children:[(0,a.jsxs)("p",{className:"text-slate-600 dark:text-slate-400",children:["Questions? Contact us at ",t.phone||"our main line","."]}),(0,a.jsxs)("p",{className:"text-xs text-slate-500 dark:text-slate-500 mt-2",children:["\xa9 ",new Date().getFullYear()," ",t.name,"."]})]})]}),{}):(0,a.jsx)(()=>(0,a.jsxs)("div",{className:"h-screen relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-cover bg-center",style:{backgroundImage:t.cover_image_url?"url('".concat((0,i.Dw)(t.cover_image_url),"')"):void 0},children:(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-blue-900/70 to-slate-900/90"})}),(0,a.jsxs)("div",{className:"relative h-full flex flex-col items-center justify-center text-white",children:[(0,a.jsx)("h1",{className:"text-6xl font-extrabold mb-4 text-center",children:t.name}),(0,a.jsx)("p",{className:"text-xl mb-10 max-w-xl text-center text-blue-100",children:t.description||"Discover our interactive showcase"}),(0,a.jsx)("button",{onClick:()=>x(!0),className:"px-10 py-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all text-lg font-semibold",children:"Explore Products"})]})]}),{})})}},9730:(e,t,s)=>{s.d(t,{A:()=>o});var a=s(5155),l=s(6766),r=s(351),c=s(9911),i=s(2115),n=s(8125);let o=e=>{let{store:t,products:s,searchQuery:o,selectedCategory:x,handleContactClick:d,onSearchQueryChange:m,onSelectedCategoryChange:h}=e,[g,p]=(0,i.useState)(!1),[f,u]=(0,i.useState)(o),[j,b]=(0,i.useState)(x||"All");(0,i.useEffect)(()=>{u(o)},[o]),(0,i.useEffect)(()=>{b(x||"All")},[x]);let N=["All",...Array.from(new Set(s.map(e=>e.category).filter(e=>"string"==typeof e&&""!==e.trim())))],w=e=>{u(e),m&&m(e)},v=e=>{b(e),h&&h(e)},y=s.filter(e=>{let t=e.name.toLowerCase().includes(f.toLowerCase())||e.description&&e.description.toLowerCase().includes(f.toLowerCase()),s="All"===j||e.category===j;return t&&s}),k=e=>{let{product:t}=e;return(0,a.jsxs)("div",{className:"bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300",children:[(0,a.jsx)("div",{className:"relative pb-[100%]",children:(0,a.jsx)(l.default,{src:t.image_url?(0,n.Dw)(t.image_url):"https://via.placeholder.com/400x400?text=No+Image",alt:t.name,className:"absolute inset-0 w-full h-full object-cover",fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"})}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:t.name}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:t.description||"No description"}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("span",{className:"text-gray-900 font-medium",children:["$",t.price.toFixed(2)]}),(0,a.jsx)("button",{onClick:()=>d(t.contact_link),disabled:!t.contact_link,className:"p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors disabled:opacity-50",children:(0,a.jsx)(r.iHs,{className:"w-5 h-5"})})]}),t.category&&(0,a.jsx)("span",{className:"mt-2 inline-block px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-600",children:t.category})]})]})};return(0,a.jsx)("div",{className:"font-sans",children:g?(0,a.jsx)(()=>(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("div",{className:"bg-white shadow-sm",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-light text-gray-900",children:t.name}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[t.telegram&&(0,a.jsx)("a",{href:t.telegram,target:"_blank",rel:"noopener noreferrer",className:"text-gray-600 hover:text-gray-900",children:(0,a.jsx)(c.hFS,{className:"h-5 w-5"})}),t.whatsapp&&(0,a.jsx)("a",{href:"https://wa.me/".concat(t.whatsapp.replace(/[^0-9]/g,"")),target:"_blank",rel:"noopener noreferrer",className:"text-gray-600 hover:text-gray-900",children:(0,a.jsx)(c.EcP,{className:"h-5 w-5"})}),t.phone&&(0,a.jsx)("a",{href:"tel:".concat(t.phone),className:"text-gray-600 hover:text-gray-900",children:(0,a.jsx)(c.Cab,{className:"h-5 w-5"})}),t.instagram&&(0,a.jsx)("a",{href:"https://instagram.com/".concat(t.instagram.replace("@","")),target:"_blank",rel:"noopener noreferrer",className:"text-gray-600 hover:text-gray-900",children:(0,a.jsx)(c.ao$,{className:"h-5 w-5"})})]})]})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center mb-10 gap-4",children:[(0,a.jsxs)("div",{className:"relative w-full md:w-1/2",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(r.CKj,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{type:"text",className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-200 focus:border-gray-200",placeholder:"Search skincare products...",value:f,onChange:e=>w(e.target.value)})]}),(0,a.jsx)("div",{className:"flex overflow-x-auto space-x-2 pb-2",children:N.map(e=>(0,a.jsx)("button",{className:"px-4 py-2 rounded-full text-sm font-medium transition-colors ".concat(j===e?"bg-gray-900 text-white":"bg-gray-100 text-gray-600 hover:bg-gray-200"),onClick:()=>v(e),children:e},e))})]}),(0,a.jsx)("h2",{className:"text-3xl font-semibold mb-8 text-center text-gray-900",children:"Our Products"}),0===y.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(r.CKj,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,a.jsx)("h3",{className:"mt-2 text-lg font-medium text-gray-900",children:"No products found"}),(0,a.jsx)("p",{className:"mt-1 text-gray-400",children:"Try adjusting your search or filter to find what you're looking for."})]}):(0,a.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:y.map(e=>(0,a.jsx)(k,{product:e},e.id))})]}),(0,a.jsx)("footer",{className:"bg-gray-800 py-8 mt-12 border-t border-gray-700",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-gray-300",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold",children:t.name}),(0,a.jsxs)("div",{className:"mt-4 flex flex-wrap justify-center gap-4",children:[t.phone&&(0,a.jsxs)("p",{children:["Phone: ",(0,a.jsx)("a",{href:"tel:".concat(t.phone),className:"hover:text-cyan-400",children:t.phone})]}),t.whatsapp&&(0,a.jsxs)("p",{children:["WhatsApp: ",(0,a.jsx)("a",{href:"https://wa.me/".concat(t.whatsapp.replace(/[^0-9]/g,"")),className:"hover:no-underline hover:text-cyan-400",children:"Message Us"})]}),t.instagram&&(0,a.jsxs)("p",{children:["Instagram: ",(0,a.jsxs)("a",{href:"https://instagram.com/".concat(t.instagram.replace("@","")),className:"hover:no-underline hover:text-cyan-400",children:["@",t.instagram.replace("@","")]})]}),t.telegram&&(0,a.jsxs)("p",{children:["Telegram: ",(0,a.jsx)("a",{href:t.telegram.startsWith("https://")?t.telegram:"https://t.me/".concat(t.telegram.replace("@","")),className:"hover:no-underline hover:text-cyan-400",children:"Contact Us"})]}),t.facebook&&(0,a.jsxs)("p",{children:["Facebook: ",(0,a.jsx)("a",{href:t.facebook.startsWith("https://")?t.facebook:"https://facebook.com/".concat(t.facebook.replace("@","")),className:"hover:no-underline hover:text-cyan-400",children:"Follow Us"})]}),t.tiktok&&(0,a.jsxs)("p",{children:["TikTok: ",(0,a.jsxs)("a",{href:"https://tiktok.com/@".concat(t.tiktok.replace("@","")),className:"hover:no-underline hover:text-cyan-400",children:["@",t.tiktok.replace("@","")]})]})]}),(0,a.jsxs)("p",{className:"mt-4",children:["\xa9 ",new Date().getFullYear()," ",t.name,". All rights reserved."]})]})})]}),{}):(0,a.jsx)(()=>(0,a.jsxs)("div",{className:"h-screen relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-cover bg-center",style:{backgroundImage:t.cover_image_url?"url('".concat((0,n.Dw)(t.cover_image_url),"')"):"url('https://images.unsplash.com/photo-1576426863848-c21f53c60b19')"}}),(0,a.jsxs)("div",{className:"relative h-full flex flex-col items-center justify-center text-white",children:[(0,a.jsx)("h1",{className:"text-6xl font-light mb-4",children:t.name}),(0,a.jsx)("p",{className:"text-xl mb-8",children:t.description||"Discover our collection"}),(0,a.jsx)("button",{onClick:()=>p(!0),className:"px-8 py-3 bg-white text-gray-900 rounded-full hover:bg-opacity-90 transition-all",children:"View Collection"})]})]}),{})})}}}]);