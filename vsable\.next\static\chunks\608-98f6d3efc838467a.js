"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[608],{283:(e,a,r)=>{r.d(a,{A:()=>d,AuthProvider:()=>c});var t=r(5155),o=r(2115),l=r(5695),s=r(3464);let i=(0,o.createContext)(void 0),n="http://localhost:5000/api",c=e=>{let{children:a}=e,[r,c]=(0,o.useState)(null),[d,u]=(0,o.useState)(!0),[g,h]=(0,o.useState)(null),y=(0,l.useRouter)();s.A.defaults.withCredentials=!0,(0,o.useEffect)(()=>{(async()=>{try{let e=await s.A.get("".concat(n,"/auth/me"));c(e.data.user)}catch(e){c(null)}finally{u(!1)}})()},[]);let f=async(e,a,r)=>{u(!0),h(null);try{let t=await s.A.post("".concat(n,"/auth/login"),{email:e,password:a,remember_me:r});c(t.data.user),"admin"===t.data.user.role?y.replace("/admin/dashboard"):"vendor"===t.data.user.role?y.replace("/vendor/dashboard"):y.replace("/")}catch(e){var t,o;throw h(s.A.isAxiosError(e)&&(null==(o=e.response)||null==(t=o.data)?void 0:t.message)||"Login failed"),e}finally{u(!1)}},p=async()=>{u(!0);try{await s.A.post("".concat(n,"/auth/logout")),c(null),y.replace("/login")}catch(r){var e,a;h(s.A.isAxiosError(r)&&(null==(a=r.response)||null==(e=a.data)?void 0:e.message)||"Logout failed")}finally{u(!1)}},m=async function(e,a,r){let t=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"customer";u(!0),h(null);try{await s.A.post("".concat(n,"/auth/register"),{name:e,email:a,password:r,role:t}),y.replace("/login")}catch(e){var o,l;throw h(s.A.isAxiosError(e)&&(null==(l=e.response)||null==(o=l.data)?void 0:o.message)||"Registration failed"),e}finally{u(!1)}},v=async e=>{u(!0),h(null);try{await s.A.post("".concat(n,"/auth/send-otp"),{email:e})}catch(e){var a,r;throw h(s.A.isAxiosError(e)&&(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"Failed to send OTP"),e}finally{u(!1)}},w=async(e,a)=>{u(!0),h(null);try{return(await s.A.post("".concat(n,"/auth/verify-otp"),{email:e,otp:a})).data.user}catch(e){var r,t;throw h(s.A.isAxiosError(e)&&(null==(t=e.response)||null==(r=t.data)?void 0:r.message)||"Invalid OTP"),e}finally{u(!1)}},b=async e=>{u(!0),h(null);try{await s.A.post("".concat(n,"/auth/forgot-password"),{email:e})}catch(e){var a,r;throw h(s.A.isAxiosError(e)&&(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"Failed to send reset email"),e}finally{u(!1)}},A=async(e,a)=>{u(!0),h(null);try{await s.A.post("".concat(n,"/auth/reset-password"),{token:e,password:a})}catch(e){var r,t;throw h(s.A.isAxiosError(e)&&(null==(t=e.response)||null==(r=t.data)?void 0:r.message)||"Failed to reset password"),e}finally{u(!1)}},x=async e=>{u(!0),h(null);try{let a=await s.A.put("".concat(n,"/auth/profile"),e);c(a.data.user)}catch(e){var a,r;throw h(s.A.isAxiosError(e)&&(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"Failed to update profile"),e}finally{u(!1)}},E=async e=>{u(!0),h(null);try{await s.A.post("".concat(n,"/auth/send-verification-email"),{email:e})}catch(e){var a,r;throw h(s.A.isAxiosError(e)&&(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"Failed to send verification email"),e}finally{u(!1)}},_=async e=>{u(!0),h(null);try{let a=(await s.A.post("".concat(n,"/auth/verify-email"),{token:e})).data.user;return c(a),a}catch(e){var a,r;throw h(s.A.isAxiosError(e)&&(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"Email verification failed"),e}finally{u(!1)}};return(0,t.jsx)(i.Provider,{value:{user:r,loading:d,error:g,login:f,logout:p,register:m,sendOTP:v,verifyOTP:w,forgotPassword:b,resetPassword:A,updateProfile:x,sendVerificationEmail:E,verifyEmail:_,setUser:c,isAuthenticated:!!r},children:a})},d=()=>{let e=(0,o.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},9608:(e,a,r)=>{r.d(a,{A:()=>p});var t=r(5155),o=r(2115),l=r(3029),s=r(3464),i=r(283),n=r(6203),c=r(3915),d=r(9509);let u=["NEXT_PUBLIC_FIREBASE_API_KEY","NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN","NEXT_PUBLIC_FIREBASE_PROJECT_ID","NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET","NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID","NEXT_PUBLIC_FIREBASE_APP_ID"].filter(e=>!d.env[e]);u.length>0&&(console.warn("Missing Firebase environment variables: ".concat(u.join(", "))),console.warn("Firebase authentication will use mock mode."));let g=null,h=null,y=null;try{g=(0,c.Wp)({apiKey:"AIzaSyAgrJR95fG9_BqXSaPtYu8beGZz1wqkvGU",authDomain:"vsable-dev.firebaseapp.com",projectId:"vsable-dev",storageBucket:"vsable-dev.firebasestorage.app",messagingSenderId:"763818917063",appId:"1:763818917063:web:112971147059619b7e12e2"}),h=(0,n.xI)(g),(y=new n.HF).addScope("email"),y.addScope("profile"),console.log("Firebase initialized successfully")}catch(e){console.error("Firebase initialization error:",e),console.warn("Firebase authentication will use mock mode.")}let f=()=>{let[e,a]=(0,o.useState)(null),[r,t]=(0,o.useState)(!0),[l,s]=(0,o.useState)(null);(0,o.useEffect)(()=>{if(!h)return void t(!1);let e=(0,n.hg)(h,e=>{a(e),t(!1)});return()=>e()},[]);let i=async()=>{if(!e)return null;try{return await e.getIdToken()}catch(e){return console.error("Get ID token error:",e),s(e instanceof Error?e.message:"Failed to get ID token"),null}};return{user:e,loading:r,error:l,signInWithGoogle:async()=>{if(!h||!y)return s("Firebase is not properly configured"),null;try{return t(!0),s(null),(await (0,n.df)(h,y)).user}catch(e){return console.error("Google sign-in error:",e),s(e instanceof Error?e.message:"Failed to sign in with Google"),null}finally{t(!1)}},signOut:async()=>{if(!h)return void s("Firebase is not properly configured");try{await (0,n.CI)(h),a(null)}catch(e){console.error("Sign out error:",e),s(e instanceof Error?e.message:"Failed to sign out")}},getIdToken:i}},p=e=>{let{mode:a,role:r="customer",onSuccess:n,onError:c,disabled:d=!1}=e,[u,g]=(0,o.useState)(!1),{setUser:h}=(0,i.A)(),{signInWithGoogle:y,getIdToken:p,error:m}=f(),v=async()=>{g(!0);try{let e=await y();if(!e)throw Error(m||"Failed to sign in with Google");let a=await p();if(!a)throw Error("Failed to get authentication token");let t=await s.A.post("".concat("http://localhost:5000/api","/auth/google"),{idToken:a,firebaseUid:e.uid,email:e.email,name:e.displayName,photoURL:e.photoURL,role:r});h&&h(t.data.user),n&&n(a),"admin"===t.data.user.role?window.location.href="/admin/dashboard":"vendor"===t.data.user.role?window.location.href="/vendor/dashboard":window.location.href="/"}catch(a){console.error("Google OAuth error:",a);let e=a instanceof Error?a.message:"Google sign-in failed";c?c(a):alert(e)}finally{g(!1)}};return(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,t.jsx)("div",{className:"w-full border-t border-gray-300 dark:border-gray-600"})}),(0,t.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,t.jsx)("span",{className:"px-2 bg-gray-50 dark:bg-gray-900 text-gray-500 dark:text-gray-400",children:"Or continue with"})})]}),(0,t.jsx)("div",{className:"mt-6",children:(0,t.jsxs)("button",{type:"button",onClick:v,disabled:d||u,className:"w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[(0,t.jsx)(l.F4b,{className:"h-5 w-5 mr-3"}),u?"Connecting...":"signin"===a?"Sign in with Google":"Sign up with Google"]})}),(0,t.jsxs)("div",{className:"mt-4 p-3 bg-gray-50 dark:bg-gray-900/20 border border-gray-200 dark:border-gray-800 rounded-md",children:[(0,t.jsxs)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:[(0,t.jsx)("strong",{children:"Firebase Setup Required:"})," Configure your Firebase project for Google OAuth:"]}),(0,t.jsxs)("ul",{className:"text-xs text-gray-600 dark:text-gray-400 mt-1 ml-4 list-disc",children:[(0,t.jsx)("li",{children:"Create Firebase project at console.firebase.google.com"}),(0,t.jsx)("li",{children:"Enable Google authentication in Firebase Auth"}),(0,t.jsx)("li",{children:"Update environment variables with Firebase config"})]})]})]})}}}]);