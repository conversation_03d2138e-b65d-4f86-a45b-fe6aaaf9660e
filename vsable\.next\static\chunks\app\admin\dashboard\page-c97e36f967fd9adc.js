(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[957],{283:(e,a,s)=>{"use strict";s.d(a,{A:()=>o,AuthProvider:()=>c});var t=s(5155),r=s(2115),l=s(5695),d=s(3464);let i=(0,r.createContext)(void 0),n="http://localhost:5000/api",c=e=>{let{children:a}=e,[s,c]=(0,r.useState)(null),[o,m]=(0,r.useState)(!0),[x,h]=(0,r.useState)(null),u=(0,l.useRouter)();d.A.defaults.withCredentials=!0,(0,r.useEffect)(()=>{(async()=>{try{let e=await d.A.get("".concat(n,"/auth/me"));c(e.data.user)}catch(e){c(null)}finally{m(!1)}})()},[]);let g=async(e,a,s)=>{m(!0),h(null);try{let t=await d.A.post("".concat(n,"/auth/login"),{email:e,password:a,remember_me:s});c(t.data.user),"admin"===t.data.user.role?u.replace("/admin/dashboard"):"vendor"===t.data.user.role?u.replace("/vendor/dashboard"):u.replace("/")}catch(e){var t,r;throw h(d.A.isAxiosError(e)&&(null==(r=e.response)||null==(t=r.data)?void 0:t.message)||"Login failed"),e}finally{m(!1)}},v=async()=>{m(!0);try{await d.A.post("".concat(n,"/auth/logout")),c(null),u.replace("/login")}catch(s){var e,a;h(d.A.isAxiosError(s)&&(null==(a=s.response)||null==(e=a.data)?void 0:e.message)||"Logout failed")}finally{m(!1)}},f=async function(e,a,s){let t=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"customer";m(!0),h(null);try{await d.A.post("".concat(n,"/auth/register"),{name:e,email:a,password:s,role:t}),u.replace("/login")}catch(e){var r,l;throw h(d.A.isAxiosError(e)&&(null==(l=e.response)||null==(r=l.data)?void 0:r.message)||"Registration failed"),e}finally{m(!1)}},y=async e=>{m(!0),h(null);try{await d.A.post("".concat(n,"/auth/send-otp"),{email:e})}catch(e){var a,s;throw h(d.A.isAxiosError(e)&&(null==(s=e.response)||null==(a=s.data)?void 0:a.message)||"Failed to send OTP"),e}finally{m(!1)}},j=async(e,a)=>{m(!0),h(null);try{return(await d.A.post("".concat(n,"/auth/verify-otp"),{email:e,otp:a})).data.user}catch(e){var s,t;throw h(d.A.isAxiosError(e)&&(null==(t=e.response)||null==(s=t.data)?void 0:s.message)||"Invalid OTP"),e}finally{m(!1)}},p=async e=>{m(!0),h(null);try{await d.A.post("".concat(n,"/auth/forgot-password"),{email:e})}catch(e){var a,s;throw h(d.A.isAxiosError(e)&&(null==(s=e.response)||null==(a=s.data)?void 0:a.message)||"Failed to send reset email"),e}finally{m(!1)}},w=async(e,a)=>{m(!0),h(null);try{await d.A.post("".concat(n,"/auth/reset-password"),{token:e,password:a})}catch(e){var s,t;throw h(d.A.isAxiosError(e)&&(null==(t=e.response)||null==(s=t.data)?void 0:s.message)||"Failed to reset password"),e}finally{m(!1)}},N=async e=>{m(!0),h(null);try{let a=await d.A.put("".concat(n,"/auth/profile"),e);c(a.data.user)}catch(e){var a,s;throw h(d.A.isAxiosError(e)&&(null==(s=e.response)||null==(a=s.data)?void 0:a.message)||"Failed to update profile"),e}finally{m(!1)}},A=async e=>{m(!0),h(null);try{await d.A.post("".concat(n,"/auth/send-verification-email"),{email:e})}catch(e){var a,s;throw h(d.A.isAxiosError(e)&&(null==(s=e.response)||null==(a=s.data)?void 0:a.message)||"Failed to send verification email"),e}finally{m(!1)}},b=async e=>{m(!0),h(null);try{let a=(await d.A.post("".concat(n,"/auth/verify-email"),{token:e})).data.user;return c(a),a}catch(e){var a,s;throw h(d.A.isAxiosError(e)&&(null==(s=e.response)||null==(a=s.data)?void 0:a.message)||"Email verification failed"),e}finally{m(!1)}};return(0,t.jsx)(i.Provider,{value:{user:s,loading:o,error:x,login:g,logout:v,register:f,sendOTP:y,verifyOTP:j,forgotPassword:p,resetPassword:w,updateProfile:N,sendVerificationEmail:A,verifyEmail:b,setUser:c,isAuthenticated:!!s},children:a})},o=()=>{let e=(0,r.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},2965:(e,a,s)=>{Promise.resolve().then(s.bind(s,4163))},4163:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>c});var t=s(5155),r=s(2115),l=s(3464),d=s(283),i=s(9053),n=s(351);function c(){let{user:e}=(0,d.A)(),[a,s]=(0,r.useState)([]),[c,o]=(0,r.useState)(!0),[m,x]=(0,r.useState)(null);return(0,r.useEffect)(()=>{(async()=>{try{let e=await l.A.get("".concat("http://localhost:5000/api","/admin/vendors"),{withCredentials:!0});s(e.data.vendors),o(!1)}catch(e){x("Failed to load vendors"),o(!1)}})()},[]),(0,t.jsx)(i.A,{allowedRoles:["admin"],children:(0,t.jsx)("div",{className:"bg-gray-100 dark:bg-gray-900 min-h-screen",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,t.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-white",children:"Admin Dashboard"}),(0,t.jsxs)("p",{className:"mt-1 text-sm text-gray-600 dark:text-gray-400",children:["Welcome back, ",null==e?void 0:e.name]}),(0,t.jsxs)("div",{className:"mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:[(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,t.jsx)("div",{className:"p-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(n.cfS,{className:"h-6 w-6 text-gray-400"})}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"Total Users"}),(0,t.jsx)("dd",{children:(0,t.jsx)("div",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"256"})})]})})]})})}),(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,t.jsx)("div",{className:"p-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(n.p45,{className:"h-6 w-6 text-gray-400"})}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"Total Vendors"}),(0,t.jsx)("dd",{children:(0,t.jsx)("div",{className:"text-lg font-medium text-gray-900 dark:text-white",children:a.length})})]})})]})})}),(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,t.jsx)("div",{className:"p-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(n.y52,{className:"h-6 w-6 text-gray-400"})}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"Total Orders"}),(0,t.jsx)("dd",{children:(0,t.jsx)("div",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"1,234"})})]})})]})})}),(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,t.jsx)("div",{className:"p-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)(n.z8N,{className:"h-6 w-6 text-gray-400"})}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"Total Revenue"}),(0,t.jsx)("dd",{children:(0,t.jsx)("div",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"$89,432"})})]})})]})})})]}),(0,t.jsxs)("div",{className:"mt-8",children:[(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Registered Vendors"}),c?(0,t.jsx)("div",{className:"mt-4 flex justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):m?(0,t.jsx)("div",{className:"mt-4 bg-red-50 dark:bg-red-900/20 p-4 rounded-md",children:(0,t.jsx)("p",{className:"text-sm text-red-700 dark:text-red-400",children:m})}):(0,t.jsx)("div",{className:"mt-4 bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md",children:(0,t.jsx)("ul",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:a.map(e=>(0,t.jsx)("li",{children:(0,t.jsxs)("div",{className:"px-4 py-4 sm:px-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-blue-600 dark:text-blue-400 truncate",children:e.name}),(0,t.jsx)("div",{className:"ml-2 flex-shrink-0 flex",children:(0,t.jsx)("p",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",children:"Active"})})]}),(0,t.jsxs)("div",{className:"mt-2 sm:flex sm:justify-between",children:[(0,t.jsx)("div",{className:"sm:flex",children:(0,t.jsx)("p",{className:"flex items-center text-sm text-gray-500 dark:text-gray-400",children:e.email})}),(0,t.jsx)("div",{className:"mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400 sm:mt-0",children:(0,t.jsxs)("p",{children:["Joined: ",new Date(e.created_at).toLocaleDateString()]})})]})]})},e.id))})})]})]})})})})}},5695:(e,a,s)=>{"use strict";var t=s(8999);s.o(t,"useParams")&&s.d(a,{useParams:function(){return t.useParams}}),s.o(t,"useRouter")&&s.d(a,{useRouter:function(){return t.useRouter}}),s.o(t,"useSearchParams")&&s.d(a,{useSearchParams:function(){return t.useSearchParams}})},9053:(e,a,s)=>{"use strict";s.d(a,{A:()=>i});var t=s(5155),r=s(2115),l=s(5695),d=s(283);let i=e=>{let{children:a,allowedRoles:s=[]}=e,{user:i,loading:n,isAuthenticated:c}=(0,d.A)(),o=(0,l.useRouter)();return((0,r.useEffect)(()=>{n||c?!n&&c&&s.length>0&&i&&!s.includes(i.role)&&("admin"===i.role?o.replace("/admin/dashboard"):"vendor"===i.role?o.replace("/vendor/dashboard"):o.replace("/")):o.replace("/login")},[n,c,i,o,s]),n)?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):!c||s.length>0&&i&&!s.includes(i.role)?null:(0,t.jsx)(t.Fragment,{children:a})}}},e=>{var a=a=>e(e.s=a);e.O(0,[844,673,441,684,358],()=>a(2965)),_N_E=e.O()}]);