(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[162],{2731:(e,r,t)=>{"use strict";t.d(r,{Ay:()=>a});var s=t(5155);t(2115);let a=e=>{let{size:r="md",color:t="black",text:a,fullScreen:l=!1}=e,n=(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,s.jsx)("div",{className:"\n          ".concat({sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12",xl:"h-16 w-16"}[r]," \n          ").concat({black:"border-black border-t-transparent",white:"border-white border-t-transparent",gray:"border-gray-300 border-t-transparent dark:border-gray-600"}[t]," \n          border-2 rounded-full animate-spin\n        ")}),a&&(0,s.jsx)("p",{className:"mt-3 ".concat({sm:"text-sm",md:"text-base",lg:"text-lg",xl:"text-xl"}[r]," text-gray-600 dark:text-gray-400"),children:a})]});return l?(0,s.jsx)("div",{className:"fixed inset-0 bg-white dark:bg-gray-900 flex items-center justify-center z-50",children:n}):n}},4119:(e,r,t)=>{Promise.resolve().then(t.bind(t,9028))},9028:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(5155),a=t(2115),l=t(6874),n=t.n(l),d=t(3464),i=t(351),o=t(2731);function c(){let[e,r]=(0,a.useState)(""),[t,l]=(0,a.useState)(!1),[c,x]=(0,a.useState)(!1),[m,g]=(0,a.useState)(""),u=async r=>{if(r.preventDefault(),g(""),l(!0),!e){g("Please enter your email address"),l(!1);return}try{await d.A.post("".concat("http://localhost:5000/api","/auth/forgot-password"),{email:e}),x(!0)}catch(e){var t,s;g(d.A.isAxiosError(e)&&(null==(s=e.response)||null==(t=s.data)?void 0:t.message)||"Failed to send reset email")}finally{l(!1)}};return c?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-6",children:(0,s.jsx)(i.YrT,{className:"h-8 w-8 text-green-600 dark:text-green-400"})}),(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Check your email"}),(0,s.jsxs)("p",{className:"mt-4 text-gray-600 dark:text-gray-400",children:["If an account with ",(0,s.jsx)("strong",{children:e})," exists, you will receive a password reset link shortly."]}),(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-500 dark:text-gray-500",children:"Didn't receive the email? Check your spam folder or try again."})]}),(0,s.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,s.jsx)("button",{onClick:()=>{x(!1),r("")},className:"w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-colors",children:"Try different email"}),(0,s.jsx)(n(),{href:"/login",className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-colors",children:"Back to login"})]})]})}):(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsx)("div",{children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Forgot your password?"}),(0,s.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"Enter your email address and we'll send you a link to reset your password."})]})}),(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:u,children:[m&&(0,s.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4",children:(0,s.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:m})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"sr-only",children:"Email address"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(i.pHD,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e,onChange:e=>r(e.target.value),className:"appearance-none rounded-md relative block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm",placeholder:"Enter your email address",disabled:t})]})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:t,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:t?(0,s.jsx)(o.Ay,{size:"sm",color:"white"}):"Send reset link"})}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)(n(),{href:"/login",className:"inline-flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white transition-colors",children:[(0,s.jsx)(i.kRp,{className:"mr-2 h-4 w-4"}),"Back to login"]})})]})]})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[844,673,874,441,684,358],()=>r(4119)),_N_E=e.O()}]);