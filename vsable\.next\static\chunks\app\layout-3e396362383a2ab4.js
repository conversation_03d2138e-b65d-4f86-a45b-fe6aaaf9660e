(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{283:(e,r,a)=>{"use strict";a.d(r,{A:()=>c,AuthProvider:()=>d});var t=a(5155),s=a(2115),l=a(5695),o=a(3464);let n=(0,s.createContext)(void 0),i="http://localhost:5000/api",d=e=>{let{children:r}=e,[a,d]=(0,s.useState)(null),[c,h]=(0,s.useState)(!0),[x,m]=(0,s.useState)(null),u=(0,l.useRouter)();o.A.defaults.withCredentials=!0,(0,s.useEffect)(()=>{(async()=>{try{let e=await o.A.get("".concat(i,"/auth/me"));d(e.data.user)}catch(e){d(null)}finally{h(!1)}})()},[]);let v=async(e,r,a)=>{h(!0),m(null);try{let t=await o.A.post("".concat(i,"/auth/login"),{email:e,password:r,remember_me:a});d(t.data.user),"admin"===t.data.user.role?u.replace("/admin/dashboard"):"vendor"===t.data.user.role?u.replace("/vendor/dashboard"):u.replace("/")}catch(e){var t,s;throw m(o.A.isAxiosError(e)&&(null==(s=e.response)||null==(t=s.data)?void 0:t.message)||"Login failed"),e}finally{h(!1)}},g=async()=>{h(!0);try{await o.A.post("".concat(i,"/auth/logout")),d(null),u.replace("/login")}catch(a){var e,r;m(o.A.isAxiosError(a)&&(null==(r=a.response)||null==(e=r.data)?void 0:e.message)||"Logout failed")}finally{h(!1)}},y=async function(e,r,a){let t=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"customer";h(!0),m(null);try{await o.A.post("".concat(i,"/auth/register"),{name:e,email:r,password:a,role:t}),u.replace("/login")}catch(e){var s,l;throw m(o.A.isAxiosError(e)&&(null==(l=e.response)||null==(s=l.data)?void 0:s.message)||"Registration failed"),e}finally{h(!1)}},b=async e=>{h(!0),m(null);try{await o.A.post("".concat(i,"/auth/send-otp"),{email:e})}catch(e){var r,a;throw m(o.A.isAxiosError(e)&&(null==(a=e.response)||null==(r=a.data)?void 0:r.message)||"Failed to send OTP"),e}finally{h(!1)}},f=async(e,r)=>{h(!0),m(null);try{return(await o.A.post("".concat(i,"/auth/verify-otp"),{email:e,otp:r})).data.user}catch(e){var a,t;throw m(o.A.isAxiosError(e)&&(null==(t=e.response)||null==(a=t.data)?void 0:a.message)||"Invalid OTP"),e}finally{h(!1)}},p=async e=>{h(!0),m(null);try{await o.A.post("".concat(i,"/auth/forgot-password"),{email:e})}catch(e){var r,a;throw m(o.A.isAxiosError(e)&&(null==(a=e.response)||null==(r=a.data)?void 0:r.message)||"Failed to send reset email"),e}finally{h(!1)}},k=async(e,r)=>{h(!0),m(null);try{await o.A.post("".concat(i,"/auth/reset-password"),{token:e,password:r})}catch(e){var a,t;throw m(o.A.isAxiosError(e)&&(null==(t=e.response)||null==(a=t.data)?void 0:a.message)||"Failed to reset password"),e}finally{h(!1)}},N=async e=>{h(!0),m(null);try{let r=await o.A.put("".concat(i,"/auth/profile"),e);d(r.data.user)}catch(e){var r,a;throw m(o.A.isAxiosError(e)&&(null==(a=e.response)||null==(r=a.data)?void 0:r.message)||"Failed to update profile"),e}finally{h(!1)}},j=async e=>{h(!0),m(null);try{await o.A.post("".concat(i,"/auth/send-verification-email"),{email:e})}catch(e){var r,a;throw m(o.A.isAxiosError(e)&&(null==(a=e.response)||null==(r=a.data)?void 0:r.message)||"Failed to send verification email"),e}finally{h(!1)}},w=async e=>{h(!0),m(null);try{let r=(await o.A.post("".concat(i,"/auth/verify-email"),{token:e})).data.user;return d(r),r}catch(e){var r,a;throw m(o.A.isAxiosError(e)&&(null==(a=e.response)||null==(r=a.data)?void 0:r.message)||"Email verification failed"),e}finally{h(!1)}};return(0,t.jsx)(n.Provider,{value:{user:a,loading:c,error:x,login:v,logout:g,register:y,sendOTP:b,verifyOTP:f,forgotPassword:p,resetPassword:k,updateProfile:N,sendVerificationEmail:j,verifyEmail:w,setUser:d,isAuthenticated:!!a},children:r})},c=()=>{let e=(0,s.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},347:()=>{},2093:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},5494:(e,r,a)=>{"use strict";a.d(r,{default:()=>d});var t=a(5155),s=a(2115),l=a(6874),o=a.n(l),n=a(283),i=a(351);let d=()=>{let{user:e,logout:r,isAuthenticated:a}=(0,n.A)(),[l,d]=(0,s.useState)(!1),c=async()=>{await r()};return(0,t.jsxs)("nav",{className:"bg-white dark:bg-black border-b border-gray-200 dark:border-gray-800",children:[(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between h-16",children:[(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("div",{className:"flex-shrink-0 flex items-center",children:(0,t.jsxs)(o(),{href:"/",className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-black dark:bg-white rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-sm font-bold text-white dark:text-black",children:"V"})}),(0,t.jsx)("span",{className:"text-xl font-bold text-black dark:text-white",children:"Vsable"})]})}),(0,t.jsxs)("div",{className:"hidden sm:ml-6 sm:flex sm:space-x-8",children:[(0,t.jsx)(o(),{href:"/",className:"border-transparent text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white hover:border-black dark:hover:border-white inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors",children:"Home"}),a&&(null==e?void 0:e.role)==="vendor"&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(o(),{href:"/vendor/dashboard",className:"border-transparent text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Dashboard"}),(0,t.jsx)(o(),{href:"/vendor/store",className:"border-transparent text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Store Profile"}),(0,t.jsx)(o(),{href:"/vendor/products",className:"border-transparent text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Products"})]}),a&&(null==e?void 0:e.role)==="admin"&&(0,t.jsx)(o(),{href:"/admin/dashboard",className:"border-transparent text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white hover:border-gray-300 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium",children:"Admin Dashboard"})]})]}),(0,t.jsx)("div",{className:"hidden sm:ml-6 sm:flex sm:items-center",children:a?(0,t.jsxs)("div",{className:"ml-3 relative flex items-center space-x-4",children:[(0,t.jsxs)("span",{className:"text-gray-700 dark:text-gray-300",children:["Hello, ",null==e?void 0:e.name]}),(0,t.jsxs)("button",{onClick:c,className:"flex items-center text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white",children:[(0,t.jsx)(i.QeK,{className:"mr-1"}),"Logout"]})]}):(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsxs)(o(),{href:"/login",className:"flex items-center text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white",children:[(0,t.jsx)(i.dUr,{className:"mr-1"}),"Login"]}),(0,t.jsxs)(o(),{href:"/register",className:"flex items-center text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-white",children:[(0,t.jsx)(i.vq3,{className:"mr-1"}),"Register"]})]})}),(0,t.jsx)("div",{className:"-mr-2 flex items-center sm:hidden",children:(0,t.jsxs)("button",{onClick:()=>{d(!l)},className:"inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500",children:[(0,t.jsx)("span",{className:"sr-only",children:"Open main menu"}),l?(0,t.jsx)(i.yGN,{className:"block h-6 w-6"}):(0,t.jsx)(i.ND1,{className:"block h-6 w-6"})]})})]})}),l&&(0,t.jsxs)("div",{className:"sm:hidden",children:[(0,t.jsxs)("div",{className:"pt-2 pb-3 space-y-1",children:[(0,t.jsx)(o(),{href:"/",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 hover:text-gray-800 dark:hover:text-white",children:"Home"}),a&&(null==e?void 0:e.role)==="vendor"&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(o(),{href:"/vendor/dashboard",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 hover:text-gray-800 dark:hover:text-white",children:"Dashboard"}),(0,t.jsx)(o(),{href:"/vendor/store",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 hover:text-gray-800 dark:hover:text-white",children:"Store Profile"}),(0,t.jsx)(o(),{href:"/vendor/products",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 hover:text-gray-800 dark:hover:text-white",children:"Products"})]}),a&&(null==e?void 0:e.role)==="admin"&&(0,t.jsx)(o(),{href:"/admin/dashboard",className:"block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 hover:text-gray-800 dark:hover:text-white",children:"Admin Dashboard"})]}),(0,t.jsx)("div",{className:"pt-4 pb-3 border-t border-gray-200 dark:border-gray-700",children:a?(0,t.jsxs)("div",{className:"flex items-center px-4",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center",children:(0,t.jsx)(i.JXP,{className:"h-6 w-6 text-gray-600 dark:text-gray-300"})})}),(0,t.jsxs)("div",{className:"ml-3",children:[(0,t.jsx)("div",{className:"text-base font-medium text-gray-800 dark:text-white",children:null==e?void 0:e.name}),(0,t.jsx)("div",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:null==e?void 0:e.email})]}),(0,t.jsx)("button",{onClick:c,className:"ml-auto flex-shrink-0 p-1 rounded-full text-gray-400 hover:text-gray-500 dark:hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:(0,t.jsx)(i.QeK,{className:"h-6 w-6"})})]}):(0,t.jsxs)("div",{className:"flex flex-col space-y-2 px-4",children:[(0,t.jsxs)(o(),{href:"/login",className:"flex items-center text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white",children:[(0,t.jsx)(i.dUr,{className:"mr-2"}),"Login"]}),(0,t.jsxs)(o(),{href:"/register",className:"flex items-center text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white",children:[(0,t.jsx)(i.vq3,{className:"mr-2"}),"Register"]})]})})]})]})}},5695:(e,r,a)=>{"use strict";var t=a(8999);a.o(t,"useParams")&&a.d(r,{useParams:function(){return t.useParams}}),a.o(t,"useRouter")&&a.d(r,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(r,{useSearchParams:function(){return t.useSearchParams}})},7735:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},8992:(e,r,a)=>{Promise.resolve().then(a.t.bind(a,2093,23)),Promise.resolve().then(a.t.bind(a,7735,23)),Promise.resolve().then(a.t.bind(a,347,23)),Promise.resolve().then(a.bind(a,5494)),Promise.resolve().then(a.bind(a,283))}},e=>{var r=r=>e(e.s=r);e.O(0,[360,844,673,874,441,684,358],()=>r(8992)),_N_E=e.O()}]);