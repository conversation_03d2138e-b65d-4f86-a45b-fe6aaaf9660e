(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{3689:(e,r,a)=>{Promise.resolve().then(a.bind(a,9690))},9690:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>i});var s=a(5155),t=a(2115),d=a(6874),l=a.n(d),n=a(283),o=a(9608);function i(){let[e,r]=(0,t.useState)(""),[a,d]=(0,t.useState)(""),[i,c]=(0,t.useState)(!1),[m,x]=(0,t.useState)(""),[u,h]=(0,t.useState)(!1),{login:b,loading:g,error:y,sendOTP:p}=(0,n.A)(),f=async r=>{if(r.preventDefault(),x(""),u){if(!e)return void x("Please enter your email address");try{await p(e),window.location.href="/verify-otp?email=".concat(encodeURIComponent(e))}catch(e){}}else{if(!e||!a)return void x("Please enter both email and password");try{await b(e,a,i)}catch(e){}}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white",children:"Sign in to your account"}),(0,s.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600 dark:text-gray-400",children:["Or"," ",(0,s.jsx)(l(),{href:"/register",className:"font-medium text-black hover:text-gray-700 dark:text-white dark:hover:text-gray-300",children:"create a new account"})]})]}),(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:f,children:[(y||m)&&(0,s.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,s.jsx)("div",{className:"flex",children:(0,s.jsx)("div",{className:"ml-3",children:(0,s.jsx)("h3",{className:"text-sm font-medium text-red-800",children:m||y})})})}),(0,s.jsxs)("div",{className:"rounded-md shadow-sm -space-y-px",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email-address",className:"sr-only",children:"Email address"}),(0,s.jsx)("input",{id:"email-address",name:"email",type:"email",autoComplete:"email",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-white",placeholder:"Email address",value:e,onChange:e=>r(e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"sr-only",children:"Password"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!u,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-white",placeholder:"Password",value:a,onChange:e=>d(e.target.value),disabled:g||u,style:{display:u?"none":"block"}})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"flex items-center justify-center",children:(0,s.jsx)("button",{type:"button",onClick:()=>{h(!u),d(""),x("")},className:"text-sm text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white transition-colors",children:u?"Use password instead":"Login with OTP"})}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[!u&&(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-black focus:ring-black border-gray-300 rounded dark:bg-gray-800 dark:border-gray-700",checked:i,onChange:e=>c(e.target.checked)}),(0,s.jsx)("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-900 dark:text-gray-300",children:"Remember me"})]}),(0,s.jsx)("div",{className:"text-sm",children:(0,s.jsx)(l(),{href:"/forgot-password",className:"font-medium text-black hover:text-gray-700 dark:text-white dark:hover:text-gray-300 transition-colors",children:"Forgot your password?"})})]})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:g,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:g?u?"Sending OTP...":"Signing in...":u?"Send OTP":"Sign in"})}),(0,s.jsx)(o.A,{mode:"signin",disabled:g})]})]})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[753,965,673,874,851,608,441,684,358],()=>r(3689)),_N_E=e.O()}]);