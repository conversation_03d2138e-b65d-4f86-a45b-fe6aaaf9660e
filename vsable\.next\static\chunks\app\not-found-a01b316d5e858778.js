(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[345],{4436:(e,r,t)=>{"use strict";t.d(r,{k5:()=>u});var n=t(2115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},o=n.createContext&&n.createContext(a),s=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}function c(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(r){return Object.getOwnPropertyDescriptor(e,r).enumerable})),t.push.apply(t,n)}return t}function l(e){for(var r=1;r<arguments.length;r++){var t=null!=arguments[r]?arguments[r]:{};r%2?c(Object(t),!0).forEach(function(r){var n,a,o;n=e,a=r,o=t[r],(a=function(e){var r=function(e,r){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,r||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"==typeof r?r:r+""}(a))in n?Object.defineProperty(n,a,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[a]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):c(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function u(e){return r=>n.createElement(d,i({attr:l({},e.attr)},r),function e(r){return r&&r.map((r,t)=>n.createElement(r.tag,l({key:t},r.attr),e(r.child)))}(e.child))}function d(e){var r=r=>{var t,{attr:a,size:o,title:c}=e,u=function(e,r){if(null==e)return{};var t,n,a=function(e,r){if(null==e)return{};var t={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(r.indexOf(n)>=0)continue;t[n]=e[n]}return t}(e,r);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)t=o[n],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(a[t]=e[t])}return a}(e,s),d=o||r.size||"1em";return r.className&&(t=r.className),e.className&&(t=(t?t+" ":"")+e.className),n.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},r.attr,a,u,{className:t,style:l(l({color:e.color||r.color},r.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),c&&n.createElement("title",null,c),e.children)};return void 0!==o?n.createElement(o.Consumer,null,e=>r(e)):r(a)}},4841:(e,r,t)=>{Promise.resolve().then(t.bind(t,9543))},9543:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var n=t(5155),a=t(6874),o=t.n(a),s=t(351);function i(){return(0,n.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center px-4",children:(0,n.jsxs)("div",{className:"max-w-lg w-full text-center",children:[(0,n.jsx)("div",{className:"mb-8",children:(0,n.jsx)("h1",{className:"text-9xl font-bold text-black dark:text-white opacity-20",children:"404"})}),(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:"Page Not Found"}),(0,n.jsx)("p",{className:"text-lg text-gray-600 dark:text-gray-400 mb-2",children:"The page you're looking for doesn't exist."}),(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-500",children:"It might have been moved, deleted, or you entered the wrong URL."})]}),(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,n.jsxs)(o(),{href:"/",className:"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-colors",children:[(0,n.jsx)(s.V5Y,{className:"mr-2 h-5 w-5"}),"Go Home"]}),(0,n.jsxs)("button",{onClick:()=>window.history.back(),className:"inline-flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-base font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-colors",children:[(0,n.jsx)(s.kRp,{className:"mr-2 h-5 w-5"}),"Go Back"]})]}),(0,n.jsxs)("div",{className:"mt-8 pt-8 border-t border-gray-200 dark:border-gray-700",children:[(0,n.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-4",children:"Looking for something specific?"}),(0,n.jsxs)(o(),{href:"/",className:"inline-flex items-center text-black dark:text-white hover:text-gray-700 dark:hover:text-gray-300 transition-colors",children:[(0,n.jsx)(s.CKj,{className:"mr-2 h-4 w-4"}),"Browse our marketplace"]})]})]})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[844,874,441,684,358],()=>r(4841)),_N_E=e.O()}]);