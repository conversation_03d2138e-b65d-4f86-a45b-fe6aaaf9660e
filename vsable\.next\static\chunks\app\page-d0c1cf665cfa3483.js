(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{3792:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var r=a(5155),s=a(2115),l=a(5590),i=a(6874),d=a.n(i),c=a(3464),n=a(351);function o(){let[e,t]=(0,s.useState)(""),[a,i]=(0,s.useState)([]),[o,x]=(0,s.useState)(!0),[h,g]=(0,s.useState)(null);return(0,s.useEffect)(()=>{(async()=>{try{let e=await c.A.get("".concat("http://localhost:5000/api","/store/"));i(e.data.stores),x(!1)}catch(e){g("Failed to load stores"),x(!1)}})()},[]),(0,r.jsxs)("div",{className:"min-h-screen bg-white dark:bg-gray-900",children:[(0,r.jsx)("div",{className:"relative bg-white dark:bg-gray-900 border-b border-gray-100 dark:border-gray-800",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 md:py-28",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-5xl md:text-7xl font-light text-black dark:text-white mb-6",children:"Vsable"}),(0,r.jsx)("p",{className:"text-xl md:text-2xl text-gray-600 dark:text-gray-300 font-light mb-12 max-w-2xl mx-auto leading-relaxed",children:"Shop from multiple vendors in one place"}),(0,r.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:(0,r.jsx)(n.CKj,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{type:"text",className:"block w-full pl-12 pr-4 py-4 text-lg border border-gray-200 dark:border-gray-700 rounded-xl bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-black dark:focus:ring-white focus:border-transparent transition-all",placeholder:"Search for vendors or products...",value:e,onChange:e=>t(e.target.value)})]})})]})})}),(0,r.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16",children:[(0,r.jsx)("h2",{className:"text-3xl font-light text-black dark:text-white mb-12 text-center",children:"Featured Vendors"}),o?(0,r.jsx)("div",{className:"flex justify-center py-16",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-black dark:border-white"})}):h?(0,r.jsx)("div",{className:"text-center py-16",children:(0,r.jsx)("p",{className:"text-red-600 dark:text-red-400 font-light",children:h})}):0===a.length?(0,r.jsx)("div",{className:"text-center py-16",children:(0,r.jsx)("p",{className:"text-gray-500 dark:text-gray-400 font-light",children:"No stores available yet."})}):(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:a.map(e=>(0,r.jsx)(d(),{href:"/store/".concat(e.slug),className:"group",children:(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-700 rounded-2xl hover:border-gray-200 dark:hover:border-gray-600 transition-all duration-300 overflow-hidden",children:(0,r.jsxs)("div",{className:"p-8",children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)("div",{className:"h-16 w-16 relative rounded-2xl overflow-hidden bg-gray-50 dark:bg-gray-700 flex-shrink-0",children:e.logo_url?(0,r.jsx)(l.A,{src:e.logo_url||"",alt:e.name,className:"h-full w-full object-cover"}):(0,r.jsx)("div",{className:"h-full w-full flex items-center justify-center",children:(0,r.jsx)(n.fZZ,{className:"h-8 w-8 text-gray-400"})})}),(0,r.jsx)("div",{className:"ml-4",children:(0,r.jsx)("h3",{className:"text-xl font-medium text-black dark:text-white group-hover:text-gray-700 dark:group-hover:text-gray-300 transition-colors",children:e.name})})]}),e.description&&(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-400 font-light leading-relaxed line-clamp-3 mb-6",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center text-black dark:text-white font-medium group-hover:translate-x-1 transition-transform",children:["Visit Store",(0,r.jsx)(n.dyV,{className:"ml-2 h-4 w-4"})]})]})})},e.id))})]}),(0,r.jsx)("div",{className:"bg-gray-50 dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h2",{className:"text-3xl md:text-4xl font-light text-black dark:text-white mb-6",children:"Are you a vendor?"}),(0,r.jsx)("p",{className:"text-xl text-gray-600 dark:text-gray-300 font-light mb-12 max-w-2xl mx-auto leading-relaxed",children:"Join our platform and start selling your products to a wider audience today."}),(0,r.jsxs)(d(),{href:"/register",className:"inline-flex items-center px-8 py-4 text-lg font-medium text-white bg-black dark:bg-white dark:text-black rounded-xl hover:bg-gray-800 dark:hover:bg-gray-100 transition-all duration-300",children:["Register as a Vendor",(0,r.jsx)(n.dyV,{className:"ml-2 h-5 w-5"})]})]})})})]})}},5590:(e,t,a)=>{"use strict";a.d(t,{A:()=>d});var r=a(5155),s=a(6766),l=a(8125),i=a(2115);let d=e=>{let{src:t,debug:a=!1,...d}=e,[c,n]=(0,i.useState)(null),o=(0,l.Dw)(t),x=o.startsWith("http://")||o.startsWith("https://");return((0,i.useEffect)(()=>{a&&console.log('[SafeImage] Original: "'.concat(t,'" → Safe: "').concat(o,'"'))},[t,o,a]),c)?(0,r.jsx)("div",{className:"flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-md p-2",children:(0,r.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Image not available"})}):(0,r.jsx)(s.default,{src:o,...d,alt:d.alt||"Image",onError:()=>{if(n("Failed to load image: ".concat(o)),console.error('[SafeImage] Error loading image: "'.concat(o,'" (original: "').concat(t,'")')),o.includes("/api/upload/serve/")){let e=o.split("/").pop(),t="".concat("https://**************:9000","/uploads/").concat(e);console.log("[SafeImage] Trying fallback URL: ".concat(t));{let e=new window.Image;e.src=t,e.onload=()=>{n(null)}}}},unoptimized:x})}},6376:(e,t,a)=>{Promise.resolve().then(a.bind(a,3792))},8125:(e,t,a)=>{"use strict";a.d(t,{Dw:()=>l});let r="http://localhost:5000/api".replace(/\/api$/,""),s=("https://**************:9000".replace(/^http:/,"https:"),"".concat(r,"/api/upload/serve"));function l(e){if(!e)return"";let t="";if(e.includes("**************:9000")){let a=e.split("/").pop();if(a)return i(e,t="".concat(s,"/").concat(a),"MinIO direct URL converted to proxy"),t}if((e.startsWith("http://")||e.startsWith("https://"))&&!e.includes("**************:9000"))return t=e,i(e,t,"External absolute URL"),t;if(e.startsWith("//"))return t="https:".concat(e),i(e,t,"Protocol-relative URL fixed"),t;if(e.startsWith("uploads/")||e.startsWith("images/")){let a=e.split("/").pop();return a&&a.match(/[0-9a-f]{32}_/)?i(e,t="".concat(s,"/").concat(a),"Path with UUID pattern"):(t="/".concat(e),i(e,t,"Added leading slash for Next.js")),t}let a=e.startsWith("/")?e:e.replace(/^\/+/,"");if(a.match(/[0-9a-f]{32}_/)){let r=a.split("/").pop();return i(e,t="".concat(s,"/").concat(r),"UUID-based filename"),t}if(a.includes("uploads/")){let r=a.split("/").pop();return i(e,t="".concat(s,"/").concat(r),"Uploads directory"),t}return i(e,t=a.startsWith("/")?a:"/".concat(a),"Ensured leading slash for Next.js"),t}function i(e,t){arguments.length>2&&void 0!==arguments[2]&&arguments[2]}}},e=>{var t=t=>e(e.s=t);e.O(0,[844,673,874,766,441,684,358],()=>t(6376)),_N_E=e.O()}]);