(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[636],{283:(e,r,a)=>{"use strict";a.d(r,{A:()=>c,AuthProvider:()=>o});var t=a(5155),s=a(2115),l=a(5695),i=a(3464);let n=(0,s.createContext)(void 0),d="http://localhost:5000/api",o=e=>{let{children:r}=e,[a,o]=(0,s.useState)(null),[c,u]=(0,s.useState)(!0),[m,x]=(0,s.useState)(null),g=(0,l.useRouter)();i.A.defaults.withCredentials=!0,(0,s.useEffect)(()=>{(async()=>{try{let e=await i.A.get("".concat(d,"/auth/me"));o(e.data.user)}catch(e){o(null)}finally{u(!1)}})()},[]);let h=async(e,r,a)=>{u(!0),x(null);try{let t=await i.A.post("".concat(d,"/auth/login"),{email:e,password:r,remember_me:a});o(t.data.user),"admin"===t.data.user.role?g.replace("/admin/dashboard"):"vendor"===t.data.user.role?g.replace("/vendor/dashboard"):g.replace("/")}catch(e){var t,s;throw x(i.A.isAxiosError(e)&&(null==(s=e.response)||null==(t=s.data)?void 0:t.message)||"Login failed"),e}finally{u(!1)}},f=async()=>{u(!0);try{await i.A.post("".concat(d,"/auth/logout")),o(null),g.replace("/login")}catch(a){var e,r;x(i.A.isAxiosError(a)&&(null==(r=a.response)||null==(e=r.data)?void 0:e.message)||"Logout failed")}finally{u(!1)}},b=async function(e,r,a){let t=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"customer";u(!0),x(null);try{await i.A.post("".concat(d,"/auth/register"),{name:e,email:r,password:a,role:t}),g.replace("/login")}catch(e){var s,l;throw x(i.A.isAxiosError(e)&&(null==(l=e.response)||null==(s=l.data)?void 0:s.message)||"Registration failed"),e}finally{u(!1)}},y=async e=>{u(!0),x(null);try{await i.A.post("".concat(d,"/auth/send-otp"),{email:e})}catch(e){var r,a;throw x(i.A.isAxiosError(e)&&(null==(a=e.response)||null==(r=a.data)?void 0:r.message)||"Failed to send OTP"),e}finally{u(!1)}},p=async(e,r)=>{u(!0),x(null);try{return(await i.A.post("".concat(d,"/auth/verify-otp"),{email:e,otp:r})).data.user}catch(e){var a,t;throw x(i.A.isAxiosError(e)&&(null==(t=e.response)||null==(a=t.data)?void 0:a.message)||"Invalid OTP"),e}finally{u(!1)}},v=async e=>{u(!0),x(null);try{await i.A.post("".concat(d,"/auth/forgot-password"),{email:e})}catch(e){var r,a;throw x(i.A.isAxiosError(e)&&(null==(a=e.response)||null==(r=a.data)?void 0:r.message)||"Failed to send reset email"),e}finally{u(!1)}},w=async(e,r)=>{u(!0),x(null);try{await i.A.post("".concat(d,"/auth/reset-password"),{token:e,password:r})}catch(e){var a,t;throw x(i.A.isAxiosError(e)&&(null==(t=e.response)||null==(a=t.data)?void 0:a.message)||"Failed to reset password"),e}finally{u(!1)}},j=async e=>{u(!0),x(null);try{let r=await i.A.put("".concat(d,"/auth/profile"),e);o(r.data.user)}catch(e){var r,a;throw x(i.A.isAxiosError(e)&&(null==(a=e.response)||null==(r=a.data)?void 0:r.message)||"Failed to update profile"),e}finally{u(!1)}},k=async e=>{u(!0),x(null);try{await i.A.post("".concat(d,"/auth/send-verification-email"),{email:e})}catch(e){var r,a;throw x(i.A.isAxiosError(e)&&(null==(a=e.response)||null==(r=a.data)?void 0:r.message)||"Failed to send verification email"),e}finally{u(!1)}},N=async e=>{u(!0),x(null);try{let r=(await i.A.post("".concat(d,"/auth/verify-email"),{token:e})).data.user;return o(r),r}catch(e){var r,a;throw x(i.A.isAxiosError(e)&&(null==(a=e.response)||null==(r=a.data)?void 0:r.message)||"Email verification failed"),e}finally{u(!1)}};return(0,t.jsx)(n.Provider,{value:{user:a,loading:c,error:m,login:h,logout:f,register:b,sendOTP:y,verifyOTP:p,forgotPassword:v,resetPassword:w,updateProfile:j,sendVerificationEmail:k,verifyEmail:N,setUser:o,isAuthenticated:!!a},children:r})},c=()=>{let e=(0,s.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},2731:(e,r,a)=>{"use strict";a.d(r,{Ay:()=>s});var t=a(5155);a(2115);let s=e=>{let{size:r="md",color:a="black",text:s,fullScreen:l=!1}=e,i=(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,t.jsx)("div",{className:"\n          ".concat({sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12",xl:"h-16 w-16"}[r]," \n          ").concat({black:"border-black border-t-transparent",white:"border-white border-t-transparent",gray:"border-gray-300 border-t-transparent dark:border-gray-600"}[a]," \n          border-2 rounded-full animate-spin\n        ")}),s&&(0,t.jsx)("p",{className:"mt-3 ".concat({sm:"text-sm",md:"text-base",lg:"text-lg",xl:"text-xl"}[r]," text-gray-600 dark:text-gray-400"),children:s})]});return l?(0,t.jsx)("div",{className:"fixed inset-0 bg-white dark:bg-gray-900 flex items-center justify-center z-50",children:i}):i}},3246:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>o});var t=a(5155),s=a(2115),l=a(283),i=a(351),n=a(2731),d=a(6766);function o(){let{user:e,updateProfile:r,loading:a}=(0,l.A)(),[o,c]=(0,s.useState)(!1),[u,m]=(0,s.useState)({name:(null==e?void 0:e.name)||"",email:(null==e?void 0:e.email)||""}),[x,g]=(0,s.useState)(!1),[h,f]=(0,s.useState)(""),[b,y]=(0,s.useState)("");if(a)return(0,t.jsx)(n.Ay,{fullScreen:!0,text:"Loading profile..."});if(!e)return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:"Access Denied"}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Please log in to view your profile."})]})});let p=e=>{m({...u,[e.target.name]:e.target.value})},v=async e=>{e.preventDefault(),g(!0),y(""),f("");try{await r(u),f("Profile updated successfully!"),c(!1),setTimeout(()=>f(""),3e3)}catch(e){console.error("Profile update error:",e),y("Failed to update profile. Please try again.")}finally{g(!1)}};return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 py-12",children:(0,t.jsx)("div",{className:"max-w-3xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:[(0,t.jsx)("div",{className:"px-6 py-8 border-b border-gray-200 dark:border-gray-700",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"relative",children:[e.profile_picture?(0,t.jsx)(d.default,{src:e.profile_picture,alt:e.name,width:80,height:80,className:"rounded-full object-cover"}):(0,t.jsx)("div",{className:"w-20 h-20 bg-black dark:bg-white rounded-full flex items-center justify-center",children:(0,t.jsx)(i.JXP,{className:"h-10 w-10 text-white dark:text-black"})}),(0,t.jsx)("button",{className:"absolute bottom-0 right-0 bg-black dark:bg-white text-white dark:text-black rounded-full p-2 hover:bg-gray-800 dark:hover:bg-gray-200 transition-colors",children:(0,t.jsx)(i.PoE,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e.name}),(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 capitalize",children:e.role})]})]}),!o&&(0,t.jsx)("button",{onClick:()=>c(!0),className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors",children:"Edit Profile"})]})}),(0,t.jsxs)("div",{className:"px-6 py-8",children:[h&&(0,t.jsx)("div",{className:"mb-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(i.YrT,{className:"h-5 w-5 text-green-600 dark:text-green-400 mr-2"}),(0,t.jsx)("p",{className:"text-sm text-green-600 dark:text-green-400",children:h})]})}),b&&(0,t.jsx)("div",{className:"mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(i.yGN,{className:"h-5 w-5 text-red-600 dark:text-red-400 mr-2"}),(0,t.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:b})]})}),(0,t.jsxs)("form",{onSubmit:v,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Basic Information"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Full Name"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(i.JXP,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)("input",{type:"text",id:"name",name:"name",value:u.name,onChange:p,disabled:!o,className:"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-black focus:border-black dark:focus:ring-white dark:focus:border-white disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:cursor-not-allowed"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email Address"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(i.pHD,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)("input",{type:"email",id:"email",name:"email",value:u.email,onChange:p,disabled:!o,className:"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-black focus:border-black dark:focus:ring-white dark:focus:border-white disabled:bg-gray-50 dark:disabled:bg-gray-800 disabled:cursor-not-allowed"})]})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Account Status"}),(0,t.jsx)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(i.pcC,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:"Email Verification"}),(0,t.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.email_verified?"Your email is verified":"Your email is not verified"})]})]}),(0,t.jsx)("div",{className:"px-3 py-1 rounded-full text-xs font-medium ".concat(e.email_verified?"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200":"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200"),children:e.email_verified?"Verified":"Unverified"})]})})]}),o&&(0,t.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,t.jsx)("button",{type:"button",onClick:()=>{m({name:e.name,email:e.email}),c(!1),y("")},className:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors",children:"Cancel"}),(0,t.jsx)("button",{type:"submit",disabled:x,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:x?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(n.Ay,{size:"sm",color:"white"}),(0,t.jsx)("span",{className:"ml-2",children:"Saving..."})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(i.Bc_,{className:"mr-2 h-4 w-4"}),"Save Changes"]})})]})]})]})]})})})}},4613:(e,r,a)=>{Promise.resolve().then(a.bind(a,3246))},5695:(e,r,a)=>{"use strict";var t=a(8999);a.o(t,"useParams")&&a.d(r,{useParams:function(){return t.useParams}}),a.o(t,"useRouter")&&a.d(r,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(r,{useSearchParams:function(){return t.useSearchParams}})},6654:(e,r,a)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useMergedRef",{enumerable:!0,get:function(){return s}});let t=a(2115);function s(e,r){let a=(0,t.useRef)(null),s=(0,t.useRef)(null);return(0,t.useCallback)(t=>{if(null===t){let e=a.current;e&&(a.current=null,e());let r=s.current;r&&(s.current=null,r())}else e&&(a.current=l(e,t)),r&&(s.current=l(r,t))},[e,r])}function l(e,r){if("function"!=typeof e)return e.current=r,()=>{e.current=null};{let a=e(r);return"function"==typeof a?a:()=>e(null)}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)}},e=>{var r=r=>e(e.s=r);e.O(0,[844,673,766,441,684,358],()=>r(4613)),_N_E=e.O()}]);