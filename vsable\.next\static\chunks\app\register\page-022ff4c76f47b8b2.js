(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[454],{836:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>m});var s=a(5155),t=a(2115),l=a(6874),d=a.n(l),n=a(283),o=a(9608),c=a(351);let i=e=>{let{selectedRole:r,onRoleChange:a,disabled:t=!1}=e,l=[{id:"customer",name:"Customer",description:"Browse and purchase products",icon:c.JXP,color:"blue"},{id:"vendor",name:"Vendor",description:"Sell products and manage store",icon:c.y52,color:"green"},{id:"admin",name:"Admin",description:"Manage platform and users",icon:c.VSk,color:"purple"}];return(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Select your role"}),(0,s.jsx)("div",{className:"grid grid-cols-1 gap-3",children:l.map(e=>{let l=e.icon,d=r===e.id;return(0,s.jsxs)("button",{type:"button",onClick:()=>!t&&a(e.id),disabled:t,className:"\n                relative flex items-center p-4 border rounded-lg text-left transition-all\n                ".concat(d?"border-black dark:border-white bg-black/5 dark:bg-white/5":"border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500","\n                ").concat(t?"opacity-50 cursor-not-allowed":"cursor-pointer","\n              "),children:[(0,s.jsx)("div",{className:"\n                flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-4\n                ".concat(d?"bg-black dark:bg-white text-white dark:text-black":"bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400","\n              "),children:(0,s.jsx)(l,{className:"w-5 h-5"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h3",{className:"\n                    text-sm font-medium\n                    ".concat(d?"text-black dark:text-white":"text-gray-900 dark:text-gray-100","\n                  "),children:e.name}),d&&(0,s.jsx)("div",{className:"w-4 h-4 rounded-full bg-black dark:bg-white flex items-center justify-center",children:(0,s.jsx)("div",{className:"w-2 h-2 rounded-full bg-white dark:bg-black"})})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:e.description})]})]},e.id)})})]})};function m(){let[e,r]=(0,t.useState)(""),[a,l]=(0,t.useState)(""),[c,m]=(0,t.useState)(""),[u,x]=(0,t.useState)(""),[g,b]=(0,t.useState)("customer"),[h,p]=(0,t.useState)(""),{register:y,loading:f,error:k}=(0,n.A)(),w=async r=>{if(r.preventDefault(),p(""),!e||!a||!c||!u)return void p("Please fill in all fields");if(c!==u)return void p("Passwords do not match");try{await y(e,a,c,g)}catch(e){}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white",children:"Create a new account"}),(0,s.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600 dark:text-gray-400",children:["Or"," ",(0,s.jsx)(d(),{href:"/login",className:"font-medium text-black hover:text-gray-700 dark:text-white dark:hover:text-gray-300",children:"sign in to your existing account"})]})]}),(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:w,children:[(k||h)&&(0,s.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,s.jsx)("div",{className:"flex",children:(0,s.jsx)("div",{className:"ml-3",children:(0,s.jsx)("h3",{className:"text-sm font-medium text-red-800",children:h||k})})})}),(0,s.jsxs)("div",{className:"rounded-md shadow-sm -space-y-px",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"sr-only",children:"Full Name"}),(0,s.jsx)("input",{id:"name",name:"name",type:"text",autoComplete:"name",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-white",placeholder:"Full Name",value:e,onChange:e=>r(e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email-address",className:"sr-only",children:"Email address"}),(0,s.jsx)("input",{id:"email-address",name:"email",type:"email",autoComplete:"email",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-white",placeholder:"Email address",value:a,onChange:e=>l(e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"sr-only",children:"Password"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",autoComplete:"new-password",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-white",placeholder:"Password",value:c,onChange:e=>m(e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"confirm-password",className:"sr-only",children:"Confirm Password"}),(0,s.jsx)("input",{id:"confirm-password",name:"confirm-password",type:"password",autoComplete:"new-password",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-white",placeholder:"Confirm Password",value:u,onChange:e=>x(e.target.value)})]})]}),(0,s.jsx)(i,{selectedRole:g,onRoleChange:b,disabled:f}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:f,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed",children:f?"Creating account...":"Create account"})}),(0,s.jsx)(o.A,{mode:"signup",role:g,disabled:f})]})]})})}},4095:(e,r,a)=>{Promise.resolve().then(a.bind(a,836))}},e=>{var r=r=>e(e.s=r);e.O(0,[844,753,965,673,874,851,608,441,684,358],()=>r(4095)),_N_E=e.O()}]);