(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[700],{2731:(e,s,r)=>{"use strict";r.d(s,{Ay:()=>a});var t=r(5155);r(2115);let a=e=>{let{size:s="md",color:r="black",text:a,fullScreen:l=!1}=e,n=(0,t.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,t.jsx)("div",{className:"\n          ".concat({sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12",xl:"h-16 w-16"}[s]," \n          ").concat({black:"border-black border-t-transparent",white:"border-white border-t-transparent",gray:"border-gray-300 border-t-transparent dark:border-gray-600"}[r]," \n          border-2 rounded-full animate-spin\n        ")}),a&&(0,t.jsx)("p",{className:"mt-3 ".concat({sm:"text-sm",md:"text-base",lg:"text-lg",xl:"text-xl"}[s]," text-gray-600 dark:text-gray-400"),children:a})]});return l?(0,t.jsx)("div",{className:"fixed inset-0 bg-white dark:bg-gray-900 flex items-center justify-center z-50",children:n}):n}},4622:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>x});var t=r(5155),a=r(2115),l=r(6874),n=r.n(l),d=r(5695),o=r(3464),c=r(351),i=r(2731);function u(){let[e,s]=(0,a.useState)(""),[r,l]=(0,a.useState)(""),[u,x]=(0,a.useState)(!1),[m,h]=(0,a.useState)(!1),[g,p]=(0,a.useState)(!1),[b,f]=(0,a.useState)(!1),[y,w]=(0,a.useState)(""),[j,v]=(0,a.useState)(""),k=(0,d.useSearchParams)();(0,a.useEffect)(()=>{let e=k.get("token");if(!e)return void w("Invalid reset link. Please request a new password reset.");v(e)},[k]);let N=e=>e.length<8?"Password must be at least 8 characters long":"",P=async s=>{if(s.preventDefault(),w(""),!j)return void w("Invalid reset link. Please request a new password reset.");let t=N(e);if(t)return void w(t);if(e!==r)return void w("Passwords do not match");p(!0);try{await o.A.post("".concat("http://localhost:5000/api","/auth/reset-password"),{token:j,password:e}),f(!0)}catch(e){var a,l;w(o.A.isAxiosError(e)&&(null==(l=e.response)||null==(a=l.data)?void 0:a.message)||"Failed to reset password")}finally{p(!1)}};return b?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"mx-auto h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-6",children:(0,t.jsx)(c.YrT,{className:"h-8 w-8 text-green-600 dark:text-green-400"})}),(0,t.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Password reset successful"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600 dark:text-gray-400",children:"Your password has been successfully reset. You can now log in with your new password."})]}),(0,t.jsx)("div",{children:(0,t.jsx)(n(),{href:"/login",className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-colors",children:"Go to login"})})]})}):(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,t.jsx)("div",{children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Reset your password"}),(0,t.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"Enter your new password below"})]})}),(0,t.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:P,children:[y&&(0,t.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4",children:(0,t.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:y})}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"sr-only",children:"New Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(c.F5$,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)("input",{id:"password",name:"password",type:u?"text":"password",required:!0,value:e,onChange:e=>s(e.target.value),className:"appearance-none rounded-md relative block w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm",placeholder:"New password",disabled:g}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>x(!u),children:u?(0,t.jsx)(c._NO,{className:"h-5 w-5 text-gray-400"}):(0,t.jsx)(c.Vap,{className:"h-5 w-5 text-gray-400"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"confirmPassword",className:"sr-only",children:"Confirm Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(c.F5$,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:m?"text":"password",required:!0,value:r,onChange:e=>l(e.target.value),className:"appearance-none rounded-md relative block w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm",placeholder:"Confirm new password",disabled:g}),(0,t.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>h(!m),children:m?(0,t.jsx)(c._NO,{className:"h-5 w-5 text-gray-400"}):(0,t.jsx)(c.Vap,{className:"h-5 w-5 text-gray-400"})})]})]})]}),(0,t.jsx)("div",{children:(0,t.jsx)("button",{type:"submit",disabled:g||!j,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:g?(0,t.jsx)(i.Ay,{size:"sm",color:"white"}):"Reset password"})}),(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)(n(),{href:"/login",className:"text-sm text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white transition-colors",children:"Back to login"})})]})]})})}function x(){return(0,t.jsx)(a.Suspense,{fallback:(0,t.jsx)(i.Ay,{fullScreen:!0,text:"Loading..."}),children:(0,t.jsx)(u,{})})}},5695:(e,s,r)=>{"use strict";var t=r(8999);r.o(t,"useParams")&&r.d(s,{useParams:function(){return t.useParams}}),r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(s,{useSearchParams:function(){return t.useSearchParams}})},6857:(e,s,r)=>{Promise.resolve().then(r.bind(r,4622))}},e=>{var s=s=>e(e.s=s);e.O(0,[844,673,874,441,684,358],()=>s(6857)),_N_E=e.O()}]);