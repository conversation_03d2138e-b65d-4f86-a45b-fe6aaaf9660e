(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[904],{3648:(e,t,r)=>{Promise.resolve().then(r.bind(r,9122))},9122:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var a=r(5155),s=r(2115),l=r(6874),n=r.n(l),o=r(3464),c=r(5695),i=r(1821),u=r(7222),d=r(5280),g=r(9730),m=r(486),h=r(9714);function f(){let e=(0,c.useParams)(),t=e&&"string"==typeof e.slug?e.slug:Array.isArray(null==e?void 0:e.slug)?e.slug[0]:void 0,[r,l]=(0,s.useState)(""),[f,p]=(0,s.useState)("All"),[x,y]=(0,s.useState)(null),[b,j]=(0,s.useState)([]),[w,A]=(0,s.useState)(["All"]),[C,S]=(0,s.useState)(!0),[k,v]=(0,s.useState)(null);(0,s.useEffect)(()=>{(async()=>{if(!t){v("Store slug is missing."),S(!1);return}S(!0),v(null);try{let e=await o.A.get("".concat("http://localhost:5000/api","/store/").concat(t));y(e.data.store);let r=e.data.products.map(e=>({...e,imageUrl:e.imageUrl||e.image_url}));j(r);let a=["All"];r.forEach(e=>{e.category&&!a.includes(e.category)&&a.push(e.category)}),A(a)}catch(e){console.error("Error fetching store:",e),v(e instanceof Error?e.message:"Store not found or an error occurred.")}finally{S(!1)}})()},[t]);let _=(0,s.useMemo)(()=>b.filter(e=>{let t=e.name.toLowerCase().includes(r.toLowerCase())||e.description&&e.description.toLowerCase().includes(r.toLowerCase()),a="All"===f||e.category===f;return t&&a}),[b,r,f]);if(C)return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})});if(k||!x)return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,a.jsxs)("div",{className:"text-center p-4",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"Store Not Available"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:k||"The store you're looking for doesn't exist or has been removed."}),(0,a.jsx)(n(),{href:"/",className:"mt-4 inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Go to Homepage"})]})});let N={store:x,products:_,categories:w,searchQuery:r,selectedCategory:f,handleContactClick:e=>{e&&window.open(e,"_blank","noopener,noreferrer")}},E={...N,onSearchQueryChange:l,onSelectedCategoryChange:p},L={...N,onSearchQueryChange:l,onSelectedCategoryChange:p};switch(x.selected_template_id||"default"){case"default":return(0,a.jsx)(i.A,{...E});case"template1":return(0,a.jsx)(u.A,{...L});case"template2":return(0,a.jsx)(d.A,{...L});case"template3":return(0,a.jsx)(g.A,{...L});case"template4":return(0,a.jsx)(m.A,{...L});case"template5":return(0,a.jsx)(h.A,{...L});default:return console.warn("Unknown template ID: ".concat(x.selected_template_id,". Defaulting to TemplateDefault.")),(0,a.jsx)(i.A,{...E})}}}},e=>{var t=t=>e(e.s=t);e.O(0,[844,711,673,874,766,108,462,441,684,358],()=>t(3648)),_N_E=e.O()}]);