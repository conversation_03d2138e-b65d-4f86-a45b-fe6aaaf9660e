(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[382],{283:(e,s,a)=>{"use strict";a.d(s,{A:()=>o,AuthProvider:()=>c});var t=a(5155),r=a(2115),l=a(5695),d=a(3464);let i=(0,r.createContext)(void 0),n="http://localhost:5000/api",c=e=>{let{children:s}=e,[a,c]=(0,r.useState)(null),[o,m]=(0,r.useState)(!0),[u,h]=(0,r.useState)(null),x=(0,l.useRouter)();d.A.defaults.withCredentials=!0,(0,r.useEffect)(()=>{(async()=>{try{let e=await d.A.get("".concat(n,"/auth/me"));c(e.data.user)}catch(e){c(null)}finally{m(!1)}})()},[]);let g=async(e,s,a)=>{m(!0),h(null);try{let t=await d.A.post("".concat(n,"/auth/login"),{email:e,password:s,remember_me:a});c(t.data.user),"admin"===t.data.user.role?x.replace("/admin/dashboard"):"vendor"===t.data.user.role?x.replace("/vendor/dashboard"):x.replace("/")}catch(e){var t,r;throw h(d.A.isAxiosError(e)&&(null==(r=e.response)||null==(t=r.data)?void 0:t.message)||"Login failed"),e}finally{m(!1)}},v=async()=>{m(!0);try{await d.A.post("".concat(n,"/auth/logout")),c(null),x.replace("/login")}catch(a){var e,s;h(d.A.isAxiosError(a)&&(null==(s=a.response)||null==(e=s.data)?void 0:e.message)||"Logout failed")}finally{m(!1)}},f=async function(e,s,a){let t=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"customer";m(!0),h(null);try{await d.A.post("".concat(n,"/auth/register"),{name:e,email:s,password:a,role:t}),x.replace("/login")}catch(e){var r,l;throw h(d.A.isAxiosError(e)&&(null==(l=e.response)||null==(r=l.data)?void 0:r.message)||"Registration failed"),e}finally{m(!1)}},p=async e=>{m(!0),h(null);try{await d.A.post("".concat(n,"/auth/send-otp"),{email:e})}catch(e){var s,a;throw h(d.A.isAxiosError(e)&&(null==(a=e.response)||null==(s=a.data)?void 0:s.message)||"Failed to send OTP"),e}finally{m(!1)}},w=async(e,s)=>{m(!0),h(null);try{return(await d.A.post("".concat(n,"/auth/verify-otp"),{email:e,otp:s})).data.user}catch(e){var a,t;throw h(d.A.isAxiosError(e)&&(null==(t=e.response)||null==(a=t.data)?void 0:a.message)||"Invalid OTP"),e}finally{m(!1)}},y=async e=>{m(!0),h(null);try{await d.A.post("".concat(n,"/auth/forgot-password"),{email:e})}catch(e){var s,a;throw h(d.A.isAxiosError(e)&&(null==(a=e.response)||null==(s=a.data)?void 0:s.message)||"Failed to send reset email"),e}finally{m(!1)}},j=async(e,s)=>{m(!0),h(null);try{await d.A.post("".concat(n,"/auth/reset-password"),{token:e,password:s})}catch(e){var a,t;throw h(d.A.isAxiosError(e)&&(null==(t=e.response)||null==(a=t.data)?void 0:a.message)||"Failed to reset password"),e}finally{m(!1)}},N=async e=>{m(!0),h(null);try{let s=await d.A.put("".concat(n,"/auth/profile"),e);c(s.data.user)}catch(e){var s,a;throw h(d.A.isAxiosError(e)&&(null==(a=e.response)||null==(s=a.data)?void 0:s.message)||"Failed to update profile"),e}finally{m(!1)}},b=async e=>{m(!0),h(null);try{await d.A.post("".concat(n,"/auth/send-verification-email"),{email:e})}catch(e){var s,a;throw h(d.A.isAxiosError(e)&&(null==(a=e.response)||null==(s=a.data)?void 0:s.message)||"Failed to send verification email"),e}finally{m(!1)}},A=async e=>{m(!0),h(null);try{let s=(await d.A.post("".concat(n,"/auth/verify-email"),{token:e})).data.user;return c(s),s}catch(e){var s,a;throw h(d.A.isAxiosError(e)&&(null==(a=e.response)||null==(s=a.data)?void 0:s.message)||"Email verification failed"),e}finally{m(!1)}};return(0,t.jsx)(i.Provider,{value:{user:a,loading:o,error:u,login:g,logout:v,register:f,sendOTP:p,verifyOTP:w,forgotPassword:y,resetPassword:j,updateProfile:N,sendVerificationEmail:b,verifyEmail:A,setUser:c,isAuthenticated:!!a},children:s})},o=()=>{let e=(0,r.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},1348:(e,s,a)=>{Promise.resolve().then(a.bind(a,3568))},3568:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>u});var t=a(5155),r=a(2115),l=a(6874),d=a.n(l),i=a(3464),n=a(351),c=a(283),o=a(9053);let m="http://localhost:5000/api";function u(){let{user:e}=(0,c.A)(),[s,a]=(0,r.useState)([]),[l,u]=(0,r.useState)({total_views:0}),[h,x]=(0,r.useState)(""),[g,v]=(0,r.useState)(!0),[f,p]=(0,r.useState)(null);return(0,r.useEffect)(()=>{let e=!0;return async function(){try{var s,t,r;let{data:l}=await i.A.get("".concat(m,"/vendor/menu"),{withCredentials:!0}),d="";try{let{data:e}=await i.A.get("".concat(m,"/store/my-store"),{withCredentials:!0});d=null!=(t=null==e||null==(s=e.store)?void 0:s.slug)?t:""}catch(e){}let n=0;try{let{data:e}=await i.A.get("".concat(m,"/vendor/stats"),{withCredentials:!0});n=(null==e?void 0:e.total_views)||0}catch(e){}e&&(a(null!=(r=l.menu_items)?r:[]),x(d),u({total_views:n}),v(!1))}catch(s){e&&(p(i.A.isAxiosError(s)&&s.response?"API error: ".concat(s.response.status," ").concat(s.response.statusText):"Failed to load dashboard data."),v(!1))}}(),()=>{e=!1}},[]),(0,t.jsx)(o.A,{allowedRoles:["vendor","admin"],children:(0,t.jsx)("div",{className:"bg-gray-100 dark:bg-gray-900 min-h-screen",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,t.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-white",children:"Vendor Dashboard"}),(0,t.jsxs)("p",{className:"mt-1 text-sm text-gray-600 dark:text-gray-400",children:["Welcome back, ",null==e?void 0:e.name]}),(0,t.jsx)("div",{className:"mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-2",children:[{icon:n.cfS,label:"Total Views",value:l.total_views},{icon:n.ND1,label:"Menu Items",value:s.length}].map(e=>{let{icon:s,label:a,value:r}=e;return(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg",children:(0,t.jsx)("div",{className:"p-5",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(s,{className:"h-6 w-6 text-gray-400 flex-shrink-0"}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:a}),(0,t.jsx)("dd",{children:(0,t.jsx)("div",{className:"text-lg font-medium text-gray-900 dark:text-white",children:r})})]})})]})})},a)})}),(0,t.jsxs)("div",{className:"mt-8",children:[(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Quick Actions"}),(0,t.jsxs)("div",{className:"mt-4 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3",children:[(0,t.jsx)(d(),{href:"/vendor/store",className:"block",children:(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow",children:(0,t.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0 bg-blue-500 rounded-md p-3",children:(0,t.jsx)(n.SG1,{className:"h-6 w-6 text-white"})}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"Manage Store Profile"}),(0,t.jsx)("dd",{className:"mt-1",children:(0,t.jsx)("div",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Edit store details"})})]})})]})})})}),(0,t.jsx)(d(),{href:"/vendor/products",className:"block",children:(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow",children:(0,t.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0 bg-green-500 rounded-md p-3",children:(0,t.jsx)(n.y52,{className:"h-6 w-6 text-white"})}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"Manage Products"}),(0,t.jsx)("dd",{className:"mt-1",children:(0,t.jsx)("div",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Add or edit products"})})]})})]})})})}),h&&(0,t.jsx)(()=>(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow cursor-pointer",onClick:()=>window.open("/store/".concat(h),"_blank"),children:(0,t.jsx)("div",{className:"px-4 py-5 sm:p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0 bg-purple-500 rounded-md p-3",children:(0,t.jsx)(n.HaR,{className:"h-6 w-6 text-white"})}),(0,t.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,t.jsxs)("dl",{children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate",children:"View Your Store"}),(0,t.jsx)("dd",{className:"mt-1",children:(0,t.jsx)("div",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"See customer view"})})]})})]})})}),{})]})]}),(0,t.jsxs)("div",{className:"mt-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Your Menu Items"}),(0,t.jsxs)(d(),{href:"/vendor/products",className:"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,t.jsx)(n.GGD,{className:"-ml-1 mr-2 h-4 w-4"}),"Add Product"]})]}),g?(0,t.jsx)("div",{className:"mt-4 flex justify-center",children:(0,t.jsx)("span",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):f?(0,t.jsx)("div",{className:"mt-4 bg-red-50 dark:bg-red-900/20 p-4 rounded-md",children:(0,t.jsx)("p",{className:"text-sm text-red-700 dark:text-red-400",children:f})}):(0,t.jsx)("div",{className:"mt-4 bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md",children:(0,t.jsx)("ul",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:s.length>0?s.map(e=>(0,t.jsx)("li",{className:"px-4 py-4 sm:px-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-16 h-16 rounded-md overflow-hidden bg-gray-200 dark:bg-gray-700",children:e.imageUrl?(0,t.jsx)("img",{src:e.imageUrl,alt:e.name,className:"w-full h-full object-cover"}):(0,t.jsx)("div",{className:"flex items-center justify-center w-full h-full",children:(0,t.jsx)(n.fZZ,{className:"h-6 w-6 text-gray-400"})})}),(0,t.jsxs)("div",{className:"ml-4 min-w-0 flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-blue-600 dark:text-blue-400 truncate",children:e.name}),(0,t.jsxs)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",children:["$",e.price]})]}),(0,t.jsxs)("div",{className:"mt-1 sm:flex sm:justify-between",children:[e.description&&(0,t.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.description}),e.category&&(0,t.jsxs)("p",{className:"mt-1 sm:mt-0 text-sm text-gray-500 dark:text-gray-400",children:["Category:\xa0",e.category]})]})]})]})},e.id)):(0,t.jsx)("li",{className:"px-4 py-4 sm:px-6",children:(0,t.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"No menu items yet. Click “Add Product” to create your first item."})})})})]})]})})})})}},5695:(e,s,a)=>{"use strict";var t=a(8999);a.o(t,"useParams")&&a.d(s,{useParams:function(){return t.useParams}}),a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(s,{useSearchParams:function(){return t.useSearchParams}})},9053:(e,s,a)=>{"use strict";a.d(s,{A:()=>i});var t=a(5155),r=a(2115),l=a(5695),d=a(283);let i=e=>{let{children:s,allowedRoles:a=[]}=e,{user:i,loading:n,isAuthenticated:c}=(0,d.A)(),o=(0,l.useRouter)();return((0,r.useEffect)(()=>{n||c?!n&&c&&a.length>0&&i&&!a.includes(i.role)&&("admin"===i.role?o.replace("/admin/dashboard"):"vendor"===i.role?o.replace("/vendor/dashboard"):o.replace("/")):o.replace("/login")},[n,c,i,o,a]),n)?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):!c||a.length>0&&i&&!a.includes(i.role)?null:(0,t.jsx)(t.Fragment,{children:s})}}},e=>{var s=s=>e(e.s=s);e.O(0,[844,673,874,441,684,358],()=>s(1348)),_N_E=e.O()}]);