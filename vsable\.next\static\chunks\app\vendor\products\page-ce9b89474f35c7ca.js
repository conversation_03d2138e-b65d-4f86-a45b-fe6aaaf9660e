(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[14],{283:(e,t,a)=>{"use strict";a.d(t,{A:()=>c,AuthProvider:()=>o});var r=a(5155),s=a(2115),l=a(5695),i=a(3464);let n=(0,s.createContext)(void 0),d="http://localhost:5000/api",o=e=>{let{children:t}=e,[a,o]=(0,s.useState)(null),[c,u]=(0,s.useState)(!0),[m,x]=(0,s.useState)(null),g=(0,l.useRouter)();i.A.defaults.withCredentials=!0,(0,s.useEffect)(()=>{(async()=>{try{let e=await i.A.get("".concat(d,"/auth/me"));o(e.data.user)}catch(e){o(null)}finally{u(!1)}})()},[]);let h=async(e,t,a)=>{u(!0),x(null);try{let r=await i.A.post("".concat(d,"/auth/login"),{email:e,password:t,remember_me:a});o(r.data.user),"admin"===r.data.user.role?g.replace("/admin/dashboard"):"vendor"===r.data.user.role?g.replace("/vendor/dashboard"):g.replace("/")}catch(e){var r,s;throw x(i.A.isAxiosError(e)&&(null==(s=e.response)||null==(r=s.data)?void 0:r.message)||"Login failed"),e}finally{u(!1)}},f=async()=>{u(!0);try{await i.A.post("".concat(d,"/auth/logout")),o(null),g.replace("/login")}catch(a){var e,t;x(i.A.isAxiosError(a)&&(null==(t=a.response)||null==(e=t.data)?void 0:e.message)||"Logout failed")}finally{u(!1)}},p=async function(e,t,a){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"customer";u(!0),x(null);try{await i.A.post("".concat(d,"/auth/register"),{name:e,email:t,password:a,role:r}),g.replace("/login")}catch(e){var s,l;throw x(i.A.isAxiosError(e)&&(null==(l=e.response)||null==(s=l.data)?void 0:s.message)||"Registration failed"),e}finally{u(!1)}},b=async e=>{u(!0),x(null);try{await i.A.post("".concat(d,"/auth/send-otp"),{email:e})}catch(e){var t,a;throw x(i.A.isAxiosError(e)&&(null==(a=e.response)||null==(t=a.data)?void 0:t.message)||"Failed to send OTP"),e}finally{u(!1)}},y=async(e,t)=>{u(!0),x(null);try{return(await i.A.post("".concat(d,"/auth/verify-otp"),{email:e,otp:t})).data.user}catch(e){var a,r;throw x(i.A.isAxiosError(e)&&(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"Invalid OTP"),e}finally{u(!1)}},v=async e=>{u(!0),x(null);try{await i.A.post("".concat(d,"/auth/forgot-password"),{email:e})}catch(e){var t,a;throw x(i.A.isAxiosError(e)&&(null==(a=e.response)||null==(t=a.data)?void 0:t.message)||"Failed to send reset email"),e}finally{u(!1)}},w=async(e,t)=>{u(!0),x(null);try{await i.A.post("".concat(d,"/auth/reset-password"),{token:e,password:t})}catch(e){var a,r;throw x(i.A.isAxiosError(e)&&(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"Failed to reset password"),e}finally{u(!1)}},j=async e=>{u(!0),x(null);try{let t=await i.A.put("".concat(d,"/auth/profile"),e);o(t.data.user)}catch(e){var t,a;throw x(i.A.isAxiosError(e)&&(null==(a=e.response)||null==(t=a.data)?void 0:t.message)||"Failed to update profile"),e}finally{u(!1)}},N=async e=>{u(!0),x(null);try{await i.A.post("".concat(d,"/auth/send-verification-email"),{email:e})}catch(e){var t,a;throw x(i.A.isAxiosError(e)&&(null==(a=e.response)||null==(t=a.data)?void 0:t.message)||"Failed to send verification email"),e}finally{u(!1)}},k=async e=>{u(!0),x(null);try{let t=(await i.A.post("".concat(d,"/auth/verify-email"),{token:e})).data.user;return o(t),t}catch(e){var t,a;throw x(i.A.isAxiosError(e)&&(null==(a=e.response)||null==(t=a.data)?void 0:t.message)||"Email verification failed"),e}finally{u(!1)}};return(0,r.jsx)(n.Provider,{value:{user:a,loading:c,error:m,login:h,logout:f,register:p,sendOTP:b,verifyOTP:y,forgotPassword:v,resetPassword:w,updateProfile:j,sendVerificationEmail:N,verifyEmail:k,setUser:o,isAuthenticated:!!a},children:t})},c=()=>{let e=(0,s.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},450:(e,t,a)=>{Promise.resolve().then(a.bind(a,8864))},485:(e,t,a)=>{"use strict";a.d(t,{A:()=>c});var r=a(5155),s=a(2115),l=a(3464),i=a(6766),n=a(351),d=a(8125),o=a(5590);let c=e=>{let{onFileUploaded:t,endpoint:a,acceptedFileTypes:c="image/*,video/*",maxSizeMB:u=5,currentFileUrl:m=null,label:x="Upload File",className:g=""}=e,[h,f]=(0,s.useState)(null),[p,b]=(0,s.useState)(m),[y,v]=(0,s.useState)(!1),[w,j]=(0,s.useState)(null),[N,k]=(0,s.useState)(!1),A=(0,s.useRef)(null),P=(0,s.useId)();(0,s.useEffect)(()=>{b(m||null)},[m]);let C=async()=>{var e,r,s,i,n,o,c;if(!h)return void j("Please select a file first");v(!0),j(null),k(!1);let u=new FormData;u.append("file",h);try{let{data:o}=await l.A.post("".concat("http://localhost:5000/api").concat(a),u,{headers:{"Content-Type":"multipart/form-data"},withCredentials:!0});k(!0);let c=null!=(n=null!=(i=null!=(s=null==o||null==(e=o.store)?void 0:e.cover_image_url)?s:null==o||null==(r=o.store)?void 0:r.logo_url)?i:null==o?void 0:o.file_url)?n:null==o?void 0:o.file_path;if(c){console.log("Original URL from server:",c);let e=(0,d.Dw)(c);console.log("Formatted URL for Next.js:",e),t(e)}else j("Upload succeeded but no file URL returned")}catch(e){j(l.A.isAxiosError(e)&&(null==(c=e.response)||null==(o=c.data)?void 0:o.message)||"Error uploading file")}finally{v(!1)}},S=()=>p?(null==h?void 0:h.type.startsWith("image/"))||p.startsWith("data:image/")?(0,r.jsx)(n.fZZ,{className:"w-12 h-12 text-gray-400"}):(null==h?void 0:h.type.startsWith("video/"))||p.startsWith("data:video/")?(0,r.jsx)(n.pVQ,{className:"w-12 h-12 text-gray-400"}):(0,r.jsx)(n.QuH,{className:"w-12 h-12 text-gray-400"}):(0,r.jsx)(n.QuH,{className:"w-12 h-12 text-gray-400"});return(0,r.jsxs)("div",{className:"w-full ".concat(g),children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:x}),(0,r.jsxs)("div",{className:"w-full h-48 bg-gray-100 dark:bg-gray-800 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 flex items-center justify-center overflow-hidden relative",children:[p?p.startsWith("data:image/")||p.match(/\.(jpe?g|png|gif|webp|avif|svg)$/i)?(0,r.jsx)("div",{className:"relative w-full h-full",children:p.startsWith("data:")?(0,r.jsx)(i.default,{src:p,alt:"preview",fill:!0,style:{objectFit:"contain"},unoptimized:!0}):(0,r.jsx)(o.A,{src:p,alt:"preview",fill:!0,sizes:"(max-width: 768px) 100vw, 50vw",style:{objectFit:"contain"},debug:!0})}):p.startsWith("data:video/")||p.match(/\.(mp4|mov|webm|ogg)$/i)?(0,r.jsx)("video",{src:p,controls:!0,className:"max-h-full max-w-full"}):S():(0,r.jsxs)("div",{className:"text-center p-4",children:[S(),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-500 dark:text-gray-400",children:"Drag & drop a file here, or click to select"}),(0,r.jsxs)("p",{className:"text-xs text-gray-400 dark:text-gray-500 mt-1",children:[c.replace("*","").replace(/,/g,", ")," files up to ",u," MB"]})]}),p&&(0,r.jsx)("button",{type:"button",onClick:()=>{f(null),b(m||null),j(null),k(!1),A.current&&(A.current.value="")},className:"absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:(0,r.jsx)(n.yGN,{className:"w-4 h-4"})})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center space-x-3 w-full",children:[(0,r.jsx)("input",{id:P,ref:A,type:"file",accept:c,className:"hidden",onChange:e=>{var t;j(null),k(!1);let a=null==(t=e.target.files)?void 0:t[0];if(!a)return;let r=1024*u*1024;if(a.size>r)return void j("File size exceeds ".concat(u," MB"));f(a);let s=new FileReader;s.onloadend=()=>b(s.result),s.readAsDataURL(a)}}),(0,r.jsx)("label",{htmlFor:P,className:"flex-1 text-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer",children:"Select file"}),(0,r.jsx)("button",{type:"button",onClick:C,disabled:!h||y,className:"px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center justify-center ".concat(!h||y?"opacity-50 cursor-not-allowed":""),children:y?(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Uploading…"]}):N?(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(n.YrT,{className:"-ml-1 mr-2 h-4 w-4"}),"Uploaded"]}):(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(n.B88,{className:"-ml-1 mr-2 h-4 w-4"}),"Upload"]})})]}),w&&(0,r.jsx)("div",{className:"mt-2 text-sm text-red-600 dark:text-red-400",children:w})]})}},5590:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(5155),s=a(6766),l=a(8125),i=a(2115);let n=e=>{let{src:t,debug:a=!1,...n}=e,[d,o]=(0,i.useState)(null),c=(0,l.Dw)(t),u=c.startsWith("http://")||c.startsWith("https://");return((0,i.useEffect)(()=>{a&&console.log('[SafeImage] Original: "'.concat(t,'" → Safe: "').concat(c,'"'))},[t,c,a]),d)?(0,r.jsx)("div",{className:"flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-md p-2",children:(0,r.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Image not available"})}):(0,r.jsx)(s.default,{src:c,...n,alt:n.alt||"Image",onError:()=>{if(o("Failed to load image: ".concat(c)),console.error('[SafeImage] Error loading image: "'.concat(c,'" (original: "').concat(t,'")')),c.includes("/api/upload/serve/")){let e=c.split("/").pop(),t="".concat("https://**************:9000","/uploads/").concat(e);console.log("[SafeImage] Trying fallback URL: ".concat(t));{let e=new window.Image;e.src=t,e.onload=()=>{o(null)}}}},unoptimized:u})}},5695:(e,t,a)=>{"use strict";var r=a(8999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},6654:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return s}});let r=a(2115);function s(e,t){let a=(0,r.useRef)(null),s=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=a.current;e&&(a.current=null,e());let t=s.current;t&&(s.current=null,t())}else e&&(a.current=l(e,r)),t&&(s.current=l(t,r))},[e,t])}function l(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let a=e(t);return"function"==typeof a?a:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8125:(e,t,a)=>{"use strict";a.d(t,{Dw:()=>l});let r="http://localhost:5000/api".replace(/\/api$/,""),s=("https://**************:9000".replace(/^http:/,"https:"),"".concat(r,"/api/upload/serve"));function l(e){if(!e)return"";let t="";if(e.includes("**************:9000")){let a=e.split("/").pop();if(a)return i(e,t="".concat(s,"/").concat(a),"MinIO direct URL converted to proxy"),t}if((e.startsWith("http://")||e.startsWith("https://"))&&!e.includes("**************:9000"))return t=e,i(e,t,"External absolute URL"),t;if(e.startsWith("//"))return t="https:".concat(e),i(e,t,"Protocol-relative URL fixed"),t;if(e.startsWith("uploads/")||e.startsWith("images/")){let a=e.split("/").pop();return a&&a.match(/[0-9a-f]{32}_/)?i(e,t="".concat(s,"/").concat(a),"Path with UUID pattern"):(t="/".concat(e),i(e,t,"Added leading slash for Next.js")),t}let a=e.startsWith("/")?e:e.replace(/^\/+/,"");if(a.match(/[0-9a-f]{32}_/)){let r=a.split("/").pop();return i(e,t="".concat(s,"/").concat(r),"UUID-based filename"),t}if(a.includes("uploads/")){let r=a.split("/").pop();return i(e,t="".concat(s,"/").concat(r),"Uploads directory"),t}return i(e,t=a.startsWith("/")?a:"/".concat(a),"Ensured leading slash for Next.js"),t}function i(e,t){arguments.length>2&&void 0!==arguments[2]&&arguments[2]}},8864:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var r=a(5155),s=a(2115),l=a(6766),i=a(3464),n=a(283),d=a(9053),o=a(485),c=a(351);let u="http://localhost:5000/api";function m(){let{user:e}=(0,n.A)(),[t,a]=(0,s.useState)(!0),[m,x]=(0,s.useState)([]),[g,h]=(0,s.useState)(null),[f,p]=(0,s.useState)(null),[b,y]=(0,s.useState)(!1),[v,w]=(0,s.useState)("create"),[j,N]=(0,s.useState)(null),[k,A]=(0,s.useState)(""),[P,C]=(0,s.useState)(""),[S,F]=(0,s.useState)(""),[_,E]=(0,s.useState)(""),[U,R]=(0,s.useState)(""),[W,I]=(0,s.useState)(""),[D,O]=(0,s.useState)(!0),[z,M]=(0,s.useState)(!1),[L,T]=(0,s.useState)(null);(0,s.useEffect)(()=>{let t=async()=>{try{let e=await i.A.get("".concat(u,"/product/"),{withCredentials:!0});x(e.data.products),a(!1)}catch(e){h("Failed to load products"),a(!1)}};e&&t()},[e]);let B=()=>{A(""),C(""),F(""),E(""),R(""),I(""),O(!0)},G=()=>{B(),w("create"),y(!0)},$=e=>{N(e),A(e.name),C(e.description||""),F(e.price.toString()),E(e.image_url||""),R(e.category||""),I(e.tags.join(", ")),O(e.is_active),w("edit"),y(!0)},X=()=>{y(!1),N(null)},Z=async e=>{if(e.preventDefault(),h(null),p(null),!k||!S)return void h("Name and price are required");let t=W.split(",").map(e=>e.trim()).filter(e=>e);try{if("create"===v){let e=await i.A.post("".concat(u,"/product/create"),{name:k,description:P||null,price:parseFloat(S),image_url:_||null,category:U||null,tags:t,is_active:D},{withCredentials:!0});x([...m,e.data.product]),p("Product created successfully!")}else if("edit"===v&&j){let e=await i.A.put("".concat(u,"/product/").concat(j.id),{name:k,description:P||null,price:parseFloat(S),image_url:_||null,category:U||null,tags:t,is_active:D},{withCredentials:!0});x(m.map(t=>t.id===j.id?e.data.product:t)),p("Product updated successfully!")}X()}catch(e){h(e instanceof Error?e.message:"Failed to save product")}},q=e=>{T(e),M(!0)},H=async()=>{if(L){h(null),p(null);try{await i.A.delete("".concat(u,"/product/").concat(L),{withCredentials:!0}),x(m.filter(e=>e.id!==L)),p("Product deleted successfully!"),M(!1),T(null)}catch(e){h(e instanceof Error?e.message:"Failed to delete product")}}},Q=async e=>{h(null),p(null);try{let t=await i.A.put("".concat(u,"/product/").concat(e,"/toggle"),{},{withCredentials:!0});x(m.map(a=>a.id===e?t.data.product:a)),p("Product ".concat(t.data.product.is_active?"activated":"deactivated"," successfully!"))}catch(e){h(e instanceof Error?e.message:"Failed to update product status")}};return(0,r.jsxs)(d.A,{allowedRoles:["vendor","admin"],children:[(0,r.jsx)("div",{className:"bg-gray-100 dark:bg-gray-900 min-h-screen",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-white",children:"Products"}),(0,r.jsxs)("button",{onClick:G,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,r.jsx)(c.GGD,{className:"-ml-1 mr-2 h-5 w-5"}),"Add Product"]})]}),g&&(0,r.jsx)("div",{className:"mt-4 bg-red-50 dark:bg-red-900/20 p-4 rounded-md",children:(0,r.jsx)("p",{className:"text-sm text-red-700 dark:text-red-400",children:g})}),f&&(0,r.jsx)("div",{className:"mt-4 bg-green-50 dark:bg-green-900/20 p-4 rounded-md",children:(0,r.jsx)("p",{className:"text-sm text-green-700 dark:text-green-400",children:f})}),t?(0,r.jsx)("div",{className:"mt-6 flex justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):0===m.length?(0,r.jsxs)("div",{className:"mt-6 text-center py-12 bg-white dark:bg-gray-800 rounded-lg shadow",children:[(0,r.jsx)(c.cnX,{className:"mx-auto h-12 w-12 text-gray-400"}),(0,r.jsx)("h3",{className:"mt-2 text-lg font-medium text-gray-900 dark:text-white",children:"No products yet"}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Get started by adding your first product."}),(0,r.jsx)("div",{className:"mt-6",children:(0,r.jsxs)("button",{onClick:G,className:"inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:[(0,r.jsx)(c.GGD,{className:"-ml-1 mr-2 h-5 w-5"}),"Add Product"]})})]}):(0,r.jsx)("div",{className:"mt-6 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3",children:m.map(e=>(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg ".concat(e.is_active?"":"opacity-60"),children:[(0,r.jsxs)("div",{className:"h-48 w-full relative",children:[e.image_url?(0,r.jsx)(l.default,{src:e.image_url,alt:e.name,fill:!0,style:{objectFit:"cover"}}):(0,r.jsx)("div",{className:"h-full w-full flex items-center justify-center bg-gray-200 dark:bg-gray-700",children:(0,r.jsx)(c.fZZ,{className:"h-12 w-12 text-gray-400"})}),!e.is_active&&(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center",children:(0,r.jsx)("span",{className:"px-2 py-1 bg-red-500 text-white text-xs font-bold uppercase rounded",children:"Inactive"})})]}),(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e.name}),(0,r.jsxs)("span",{className:"text-blue-600 dark:text-blue-400 font-bold",children:["$",e.price.toFixed(2)]})]}),(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400 line-clamp-2",children:e.description||"No description"}),e.category&&(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",children:e.category})}),(0,r.jsxs)("div",{className:"mt-4 flex justify-between",children:[(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>$(e),className:"inline-flex items-center p-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:bg-gray-600",children:(0,r.jsx)(c.WXf,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>q(e.id),className:"inline-flex items-center p-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:bg-gray-600",children:(0,r.jsx)(c.IXo,{className:"h-4 w-4 text-red-500"})})]}),(0,r.jsx)("button",{onClick:()=>Q(e.id),className:"inline-flex items-center p-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:bg-gray-600",title:e.is_active?"Deactivate":"Activate",children:e.is_active?(0,r.jsx)(c._NO,{className:"h-4 w-4"}):(0,r.jsx)(c.Vap,{className:"h-4 w-4"})})]})]})]},e.id))})]})})}),b&&(0,r.jsx)("div",{className:"fixed inset-0 overflow-y-auto z-50",children:(0,r.jsxs)("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,r.jsx)("span",{className:"hidden sm:inline-block sm:align-middle sm:h-screen","aria-hidden":"true",children:"​"}),(0,r.jsx)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:(0,r.jsxs)("form",{onSubmit:Z,children:[(0,r.jsxs)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-white",children:"create"===v?"Add New Product":"Edit Product"}),(0,r.jsxs)("div",{className:"mt-4 space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Product Name *"}),(0,r.jsx)("div",{className:"mt-1 relative rounded-md shadow-sm",children:(0,r.jsx)("input",{type:"text",name:"name",id:"name",required:!0,className:"focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"Product Name",value:k,onChange:e=>A(e.target.value)})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"price",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Price *"}),(0,r.jsxs)("div",{className:"mt-1 relative rounded-md shadow-sm",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)("span",{className:"text-gray-500 dark:text-gray-400 sm:text-sm",children:"$"})}),(0,r.jsx)("input",{type:"number",name:"price",id:"price",required:!0,min:"0",step:"0.01",className:"focus:ring-blue-500 focus:border-blue-500 block w-full pl-7 sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"0.00",value:S,onChange:e=>F(e.target.value)})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Description"}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)("textarea",{id:"description",name:"description",rows:3,className:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"Product description",value:P,onChange:e=>C(e.target.value)})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Product Image"}),(0,r.jsx)("div",{className:"mt-1",children:j&&"edit"===v?(0,r.jsx)(o.A,{onFileUploaded:e=>E(e),endpoint:"/product/".concat(j.id,"/upload-image"),acceptedFileTypes:"image/*,video/*",maxSizeMB:5,currentFileUrl:_,label:"Product Image"}):(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsx)(o.A,{onFileUploaded:e=>E(e),endpoint:"/upload/image",acceptedFileTypes:"image/*,video/*",maxSizeMB:5,currentFileUrl:_,label:"Product Image"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"You can upload the image now, or add it after creating the product."})]})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Category"}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)("input",{type:"text",name:"category",id:"category",className:"focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"e.g. Main Course, Dessert, etc.",value:U,onChange:e=>R(e.target.value)})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"tags",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Tags (comma separated)"}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)("input",{type:"text",name:"tags",id:"tags",className:"focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-700 dark:border-gray-600 dark:text-white",placeholder:"e.g. vegetarian, spicy, gluten-free",value:W,onChange:e=>I(e.target.value)})})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("input",{id:"is-active",name:"is-active",type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:bg-gray-700 dark:border-gray-600",checked:D,onChange:e=>O(e.target.checked)}),(0,r.jsx)("label",{htmlFor:"is-active",className:"ml-2 block text-sm text-gray-900 dark:text-gray-300",children:"Product is active and visible to customers"})]})]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,r.jsx)("button",{type:"submit",className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm",children:"create"===v?"Add Product":"Update Product"}),(0,r.jsx)("button",{type:"button",className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700",onClick:X,children:"Cancel"})]})]})})]})}),z&&(0,r.jsx)("div",{className:"fixed inset-0 overflow-y-auto z-50",children:(0,r.jsxs)("div",{className:"flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[(0,r.jsx)("span",{className:"hidden sm:inline-block sm:align-middle sm:h-screen","aria-hidden":"true",children:"​"}),(0,r.jsxs)("div",{className:"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:[(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:(0,r.jsxs)("div",{className:"sm:flex sm:items-start",children:[(0,r.jsx)("div",{className:"mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10",children:(0,r.jsx)(c.IXo,{className:"h-6 w-6 text-red-600"})}),(0,r.jsxs)("div",{className:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left",children:[(0,r.jsx)("h3",{className:"text-lg leading-6 font-medium text-gray-900 dark:text-white",children:"Delete Product"}),(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Are you sure you want to delete this product? This action cannot be undone."})})]})]})}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[(0,r.jsx)("button",{type:"button",className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm",onClick:H,children:"Delete"}),(0,r.jsx)("button",{type:"button",className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700",onClick:()=>{T(null),M(!1)},children:"Cancel"})]})]})]})})]})}},9053:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});var r=a(5155),s=a(2115),l=a(5695),i=a(283);let n=e=>{let{children:t,allowedRoles:a=[]}=e,{user:n,loading:d,isAuthenticated:o}=(0,i.A)(),c=(0,l.useRouter)();return((0,s.useEffect)(()=>{d||o?!d&&o&&a.length>0&&n&&!a.includes(n.role)&&("admin"===n.role?c.replace("/admin/dashboard"):"vendor"===n.role?c.replace("/vendor/dashboard"):c.replace("/")):c.replace("/login")},[d,o,n,c,a]),d)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):!o||a.length>0&&n&&!a.includes(n.role)?null:(0,r.jsx)(r.Fragment,{children:t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[844,673,766,441,684,358],()=>t(450)),_N_E=e.O()}]);