(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[203],{523:(e,t,r)=>{Promise.resolve().then(r.bind(r,8233))},8233:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var a=r(5155),s=r(2115),l=r(3464),d=r(7398),o=r(9053),n=r(6874),i=r.n(n);function c(){let[e,t]=(0,s.useState)(null),[r,n]=(0,s.useState)(!0),[c,u]=(0,s.useState)(null);return((0,s.useEffect)(()=>{(async()=>{n(!0),u(null);try{let e=await l.A.get("".concat("http://localhost:5000/api","/vendor/dashboard"),{withCredentials:!0});e.data.store?t(e.data.store):u("No store found. Please create a store first to manage its settings.")}catch(t){var e,r,a;console.error("Error fetching vendor data:",t),l.A.isAxiosError(t)&&(null==(e=t.response)?void 0:e.status)===401?u("Authentication failed. Please log in again."):u(l.A.isAxiosError(t)&&(null==(a=t.response)||null==(r=a.data)?void 0:r.message)||"Failed to load store settings.")}finally{n(!1)}})()},[]),r)?(0,a.jsx)(o.A,{allowedRoles:["vendor"],children:(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})})}):(0,a.jsx)(o.A,{allowedRoles:["vendor"],children:(0,a.jsx)("div",{className:"min-h-screen bg-gray-100 dark:bg-gray-900 py-8",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)(i(),{href:"/vendor/dashboard",className:"text-blue-600 dark:text-blue-400 hover:underline",children:"← Back to Dashboard"})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:"Store Appearance Settings"}),(0,a.jsx)("p",{className:"text-md text-gray-600 dark:text-gray-400 mb-8",children:"Customize the look and feel of your public storefront."}),c&&!e&&(0,a.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 p-4 rounded-md mb-6",children:[(0,a.jsx)("p",{className:"text-sm text-red-700 dark:text-red-400",children:c}),c.includes("create a store first")&&(0,a.jsx)(i(),{href:"/vendor/store/create",className:"mt-2 inline-block text-sm text-blue-600 dark:text-blue-400 hover:underline",children:"Create your store now →"})]}),e?(0,a.jsx)(d.A,{currentStore:e,onTemplateUpdate:e=>{t(e)}}):!r&&!c&&(0,a.jsx)("div",{className:"bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-md",children:(0,a.jsx)("p",{className:"text-sm text-yellow-700 dark:text-yellow-400",children:"Store data is not available. If you've just created your store, please try refreshing."})})]})})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[844,711,673,874,766,108,462,122,441,684,358],()=>t(523)),_N_E=e.O()}]);