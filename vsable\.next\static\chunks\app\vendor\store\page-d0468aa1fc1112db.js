(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[555],{485:(e,a,r)=>{"use strict";r.d(a,{A:()=>c});var t=r(5155),s=r(2115),l=r(3464),d=r(6766),o=r(351),n=r(8125),i=r(5590);let c=e=>{let{onFileUploaded:a,endpoint:r,acceptedFileTypes:c="image/*,video/*",maxSizeMB:m=5,currentFileUrl:u=null,label:x="Upload File",className:g=""}=e,[p,h]=(0,s.useState)(null),[b,f]=(0,s.useState)(u),[y,k]=(0,s.useState)(!1),[j,v]=(0,s.useState)(null),[N,w]=(0,s.useState)(!1),S=(0,s.useRef)(null),F=(0,s.useId)();(0,s.useEffect)(()=>{f(u||null)},[u]);let _=async()=>{var e,t,s,d,o,i,c;if(!p)return void v("Please select a file first");k(!0),v(null),w(!1);let m=new FormData;m.append("file",p);try{let{data:i}=await l.A.post("".concat("http://localhost:5000/api").concat(r),m,{headers:{"Content-Type":"multipart/form-data"},withCredentials:!0});w(!0);let c=null!=(o=null!=(d=null!=(s=null==i||null==(e=i.store)?void 0:e.cover_image_url)?s:null==i||null==(t=i.store)?void 0:t.logo_url)?d:null==i?void 0:i.file_url)?o:null==i?void 0:i.file_path;if(c){console.log("Original URL from server:",c);let e=(0,n.Dw)(c);console.log("Formatted URL for Next.js:",e),a(e)}else v("Upload succeeded but no file URL returned")}catch(e){v(l.A.isAxiosError(e)&&(null==(c=e.response)||null==(i=c.data)?void 0:i.message)||"Error uploading file")}finally{k(!1)}},C=()=>b?(null==p?void 0:p.type.startsWith("image/"))||b.startsWith("data:image/")?(0,t.jsx)(o.fZZ,{className:"w-12 h-12 text-gray-400"}):(null==p?void 0:p.type.startsWith("video/"))||b.startsWith("data:video/")?(0,t.jsx)(o.pVQ,{className:"w-12 h-12 text-gray-400"}):(0,t.jsx)(o.QuH,{className:"w-12 h-12 text-gray-400"}):(0,t.jsx)(o.QuH,{className:"w-12 h-12 text-gray-400"});return(0,t.jsxs)("div",{className:"w-full ".concat(g),children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:x}),(0,t.jsxs)("div",{className:"w-full h-48 bg-gray-100 dark:bg-gray-800 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 flex items-center justify-center overflow-hidden relative",children:[b?b.startsWith("data:image/")||b.match(/\.(jpe?g|png|gif|webp|avif|svg)$/i)?(0,t.jsx)("div",{className:"relative w-full h-full",children:b.startsWith("data:")?(0,t.jsx)(d.default,{src:b,alt:"preview",fill:!0,style:{objectFit:"contain"},unoptimized:!0}):(0,t.jsx)(i.A,{src:b,alt:"preview",fill:!0,style:{objectFit:"contain"},debug:!0})}):b.startsWith("data:video/")||b.match(/\.(mp4|mov|webm|ogg)$/i)?(0,t.jsx)("video",{src:b,controls:!0,className:"max-h-full max-w-full"}):C():(0,t.jsxs)("div",{className:"text-center p-4",children:[C(),(0,t.jsx)("p",{className:"mt-2 text-sm text-gray-500 dark:text-gray-400",children:"Drag & drop a file here, or click to select"}),(0,t.jsxs)("p",{className:"text-xs text-gray-400 dark:text-gray-500 mt-1",children:[c.replace("*","").replace(/,/g,", ")," files up to ",m," MB"]})]}),b&&(0,t.jsx)("button",{type:"button",onClick:()=>{h(null),f(u||null),v(null),w(!1),S.current&&(S.current.value="")},className:"absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500",children:(0,t.jsx)(o.yGN,{className:"w-4 h-4"})})]}),(0,t.jsxs)("div",{className:"mt-4 flex items-center space-x-3 w-full",children:[(0,t.jsx)("input",{id:F,ref:S,type:"file",accept:c,className:"hidden",onChange:e=>{var a;v(null),w(!1);let r=null==(a=e.target.files)?void 0:a[0];if(!r)return;let t=1024*m*1024;if(r.size>t)return void v("File size exceeds ".concat(m," MB"));h(r);let s=new FileReader;s.onloadend=()=>f(s.result),s.readAsDataURL(r)}}),(0,t.jsx)("label",{htmlFor:F,className:"flex-1 text-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer",children:"Select file"}),(0,t.jsx)("button",{type:"button",onClick:_,disabled:!p||y,className:"px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center justify-center ".concat(!p||y?"opacity-50 cursor-not-allowed":""),children:y?(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Uploading…"]}):N?(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(o.YrT,{className:"-ml-1 mr-2 h-4 w-4"}),"Uploaded"]}):(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(o.B88,{className:"-ml-1 mr-2 h-4 w-4"}),"Upload"]})})]}),j&&(0,t.jsx)("div",{className:"mt-2 text-sm text-red-600 dark:text-red-400",children:j})]})}},1537:(e,a,r)=>{Promise.resolve().then(r.bind(r,8553))},5590:(e,a,r)=>{"use strict";r.d(a,{A:()=>o});var t=r(5155),s=r(6766),l=r(8125),d=r(2115);let o=e=>{let{src:a,debug:r=!1,...o}=e,[n,i]=(0,d.useState)(null),c=(0,l.Dw)(a),m=c.startsWith("http://")||c.startsWith("https://");return((0,d.useEffect)(()=>{r&&console.log('[SafeImage] Original: "'.concat(a,'" → Safe: "').concat(c,'"'))},[a,c,r]),n)?(0,t.jsx)("div",{className:"flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-md p-2",children:(0,t.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Image not available"})}):(0,t.jsx)(s.default,{src:c,...o,alt:o.alt||"Image",onError:()=>{if(i("Failed to load image: ".concat(c)),console.error('[SafeImage] Error loading image: "'.concat(c,'" (original: "').concat(a,'")')),c.includes("/api/upload/serve/")){let e=c.split("/").pop(),a="".concat("https://35.240.129.146:9000","/uploads/").concat(e);console.log("[SafeImage] Trying fallback URL: ".concat(a));{let e=new window.Image;e.src=a,e.onload=()=>{i(null)}}}},unoptimized:m})}},6654:(e,a,r)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"useMergedRef",{enumerable:!0,get:function(){return s}});let t=r(2115);function s(e,a){let r=(0,t.useRef)(null),s=(0,t.useRef)(null);return(0,t.useCallback)(t=>{if(null===t){let e=r.current;e&&(r.current=null,e());let a=s.current;a&&(s.current=null,a())}else e&&(r.current=l(e,t)),a&&(s.current=l(a,t))},[e,a])}function l(e,a){if("function"!=typeof e)return e.current=a,()=>{e.current=null};{let r=e(a);return"function"==typeof r?r:()=>e(null)}}("function"==typeof a.default||"object"==typeof a.default&&null!==a.default)&&void 0===a.default.__esModule&&(Object.defineProperty(a.default,"__esModule",{value:!0}),Object.assign(a.default,a),e.exports=a.default)},8553:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>g});var t=r(5155),s=r(2115),l=r(5695),d=r(3464),o=r(283),n=r(9053),i=r(7398),c=r(485),m=r(351),u=r(9911);let x="http://localhost:5000/api";function g(){let{user:e}=(0,o.A)(),a=(0,l.useRouter)(),[r,g]=(0,s.useState)(!0),[p,h]=(0,s.useState)(!1),[b,f]=(0,s.useState)(null),[y,k]=(0,s.useState)(null),[j,v]=(0,s.useState)(null),[N,w]=(0,s.useState)(null),[S,F]=(0,s.useState)(""),[_,C]=(0,s.useState)(""),[U,A]=(0,s.useState)(""),[E,R]=(0,s.useState)(""),[I,O]=(0,s.useState)(""),[T,z]=(0,s.useState)(""),[M,P]=(0,s.useState)(""),[W,L]=(0,s.useState)(""),[B,D]=(0,s.useState)(""),[Y,H]=(0,s.useState)("");(0,s.useEffect)(()=>{let a=async()=>{try{let e=await d.A.get("".concat(x,"/store/my-store"),{withCredentials:!0});v(e.data.store),w(!0),F(e.data.store.name||""),C(e.data.store.description||""),A(e.data.store.logo_url||""),R(e.data.store.cover_image_url||""),O(e.data.store.telegram||""),z(e.data.store.whatsapp||""),P(e.data.store.phone||""),L(e.data.store.instagram||""),D(e.data.store.facebook||""),H(e.data.store.tiktok||""),g(!1)}catch(a){var e;d.A.isAxiosError(a)&&(null==(e=a.response)?void 0:e.status)===404?w(!1):f("Failed to load store data"),g(!1)}};e&&a()},[e]);let Q=async e=>{e.preventDefault(),h(!0),f(null),k(null);try{let e=await d.A.post("".concat(x,"/store/create"),{name:S,description:_||null,logo_url:U||null,cover_image_url:E||null,telegram:I||null,whatsapp:T||null,phone:M||null,instagram:W||null,facebook:B||null,tiktok:Y||null},{withCredentials:!0});v(e.data.store),w(!0),k("Store created successfully!"),setTimeout(()=>{a.replace("/vendor/store")},1500)}catch(e){var r,t;f(d.A.isAxiosError(e)&&(null==(t=e.response)||null==(r=t.data)?void 0:r.message)||"Failed to create store")}finally{h(!1)}},$=async e=>{e.preventDefault(),h(!0),f(null),k(null);try{let e=await d.A.put("".concat(x,"/store/update"),{name:S,description:_||null,logo_url:U||null,cover_image_url:E||null,telegram:I||null,whatsapp:T||null,phone:M||null,instagram:W||null,facebook:B||null,tiktok:Y||null},{withCredentials:!0});v(e.data.store),k("Store updated successfully!")}catch(e){var a,r;f(d.A.isAxiosError(e)&&(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"Failed to update store")}finally{h(!1)}};return(0,t.jsx)(n.A,{allowedRoles:["vendor","admin"],children:(0,t.jsx)("div",{className:"bg-gray-100 dark:bg-gray-900 min-h-screen",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,t.jsx)("h1",{className:"text-2xl font-semibold text-gray-900 dark:text-white",children:N?"Store Profile":"Create Your Store"}),r?(0,t.jsx)("div",{className:"mt-6 flex justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})}):(0,t.jsxs)("div",{className:"mt-6",children:[b&&(0,t.jsx)("div",{className:"mb-4 bg-red-50 dark:bg-red-900/20 p-4 rounded-md",children:(0,t.jsx)("p",{className:"text-sm text-red-700 dark:text-red-400",children:b})}),y&&(0,t.jsx)("div",{className:"mb-4 bg-green-50 dark:bg-green-900/20 p-4 rounded-md",children:(0,t.jsx)("p",{className:"text-sm text-green-700 dark:text-green-400",children:y})}),(0,t.jsxs)("form",{onSubmit:N?$:Q,className:"space-y-6",children:[(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow px-4 py-5 sm:rounded-lg sm:p-6",children:(0,t.jsxs)("div",{className:"md:grid md:grid-cols-3 md:gap-6",children:[(0,t.jsxs)("div",{className:"md:col-span-1",children:[(0,t.jsx)("h3",{className:"text-lg font-medium leading-6 text-gray-900 dark:text-white",children:"Store Information"}),(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"This information will be displayed publicly on your store page."})]}),(0,t.jsx)("div",{className:"mt-5 md:mt-0 md:col-span-2",children:(0,t.jsxs)("div",{className:"grid grid-cols-6 gap-6",children:[(0,t.jsxs)("div",{className:"col-span-6 sm:col-span-4",children:[(0,t.jsx)("label",{htmlFor:"store-name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Store Name *"}),(0,t.jsxs)("div",{className:"mt-1 flex rounded-md shadow-sm",children:[(0,t.jsx)("span",{className:"inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm",children:(0,t.jsx)(m.qYx,{className:"h-4 w-4"})}),(0,t.jsx)("input",{type:"text",name:"store-name",id:"store-name",required:!0,className:"focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-white",placeholder:"Your Store Name",value:S,onChange:e=>F(e.target.value)})]})]}),(0,t.jsxs)("div",{className:"col-span-6",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Store Logo"}),(0,t.jsx)("div",{className:"mt-1",children:j?(0,t.jsx)(c.A,{onFileUploaded:e=>{if(console.log("Logo uploaded:",e),A(e),j){let a={...j,logo_url:e};console.log("Updating store with new logo:",a),v(a)}},endpoint:"/store/upload-logo",acceptedFileTypes:"image/*,video/*",maxSizeMB:5,currentFileUrl:U,label:"Store Logo"}):(0,t.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,t.jsx)(c.A,{onFileUploaded:e=>A(e),endpoint:"/upload/image",acceptedFileTypes:"image/*,video/*",maxSizeMB:5,currentFileUrl:U,label:"Store Logo"}),(0,t.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"You can upload the logo now, or add it after creating the store."})]})}),(0,t.jsx)("p",{className:"mt-2 text-sm text-gray-500 dark:text-gray-400",children:"Upload a logo for your store. Recommended size: 200x200 pixels."})]}),(0,t.jsxs)("div",{className:"col-span-6",children:[(0,t.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Description"}),(0,t.jsx)("div",{className:"mt-1",children:(0,t.jsx)("textarea",{id:"description",name:"description",rows:3,className:"shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-800 dark:border-gray-600 dark:text-white",placeholder:"Describe your store and what you offer",value:_,onChange:e=>C(e.target.value)})})]}),(0,t.jsxs)("div",{className:"col-span-6",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Cover Image"}),(0,t.jsx)("div",{className:"mt-1",children:j?(0,t.jsx)(c.A,{onFileUploaded:e=>{if(console.log("Cover image uploaded:",e),R(e),j){let a={...j,cover_image_url:e};console.log("Updating store with new cover image:",a),v(a)}},endpoint:"/store/upload-cover",acceptedFileTypes:"image/*,video/*",maxSizeMB:10,currentFileUrl:E,label:"Cover Image"}):(0,t.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,t.jsx)(c.A,{onFileUploaded:e=>R(e),endpoint:"/upload/image",acceptedFileTypes:"image/*,video/*",maxSizeMB:10,currentFileUrl:E,label:"Cover Image"}),(0,t.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"You can upload the cover image now, or add it after creating the store."})]})}),(0,t.jsx)("p",{className:"mt-2 text-sm text-gray-500 dark:text-gray-400",children:"Upload a cover image for your store's landing page. Recommended size: 1920x1080 pixels."})]})]})})]})}),(0,t.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow px-4 py-5 sm:rounded-lg sm:p-6",children:(0,t.jsxs)("div",{className:"md:grid md:grid-cols-3 md:gap-6",children:[(0,t.jsxs)("div",{className:"md:col-span-1",children:[(0,t.jsx)("h3",{className:"text-lg font-medium leading-6 text-gray-900 dark:text-white",children:"Contact Information"}),(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"How customers can reach you to place orders."})]}),(0,t.jsx)("div",{className:"mt-5 md:mt-0 md:col-span-2",children:(0,t.jsxs)("div",{className:"grid grid-cols-6 gap-6",children:[(0,t.jsxs)("div",{className:"col-span-6 sm:col-span-4",children:[(0,t.jsx)("label",{htmlFor:"telegram",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Telegram Username or Link"}),(0,t.jsxs)("div",{className:"mt-1 flex rounded-md shadow-sm",children:[(0,t.jsx)("span",{className:"inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm",children:(0,t.jsx)(u.hFS,{className:"h-4 w-4 text-blue-500"})}),(0,t.jsx)("input",{type:"text",name:"telegram",id:"telegram",className:"focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-white",placeholder:"@username or https://t.me/username",value:I,onChange:e=>O(e.target.value)})]})]}),(0,t.jsxs)("div",{className:"col-span-6 sm:col-span-4",children:[(0,t.jsx)("label",{htmlFor:"whatsapp",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"WhatsApp Number (Optional)"}),(0,t.jsxs)("div",{className:"mt-1 flex rounded-md shadow-sm",children:[(0,t.jsx)("span",{className:"inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm",children:(0,t.jsx)(u.EcP,{className:"h-4 w-4 text-green-500"})}),(0,t.jsx)("input",{type:"text",name:"whatsapp",id:"whatsapp",className:"focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-white",placeholder:"+1234567890",value:T,onChange:e=>z(e.target.value)})]})]}),(0,t.jsxs)("div",{className:"col-span-6 sm:col-span-4",children:[(0,t.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Phone Number (Optional)"}),(0,t.jsxs)("div",{className:"mt-1 flex rounded-md shadow-sm",children:[(0,t.jsx)("span",{className:"inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm",children:(0,t.jsx)(m.QFc,{className:"h-4 w-4"})}),(0,t.jsx)("input",{type:"tel",name:"phone",id:"phone",className:"focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-white",placeholder:"+1234567890",value:M,onChange:e=>P(e.target.value)})]})]}),(0,t.jsxs)("div",{className:"col-span-6 sm:col-span-4",children:[(0,t.jsx)("label",{htmlFor:"instagram",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Instagram Username (Optional)"}),(0,t.jsxs)("div",{className:"mt-1 flex rounded-md shadow-sm",children:[(0,t.jsx)("span",{className:"inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm",children:(0,t.jsx)(u.ao$,{className:"h-4 w-4 text-pink-500"})}),(0,t.jsx)("input",{type:"text",name:"instagram",id:"instagram",className:"focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-white",placeholder:"username",value:W,onChange:e=>L(e.target.value)})]})]}),(0,t.jsxs)("div",{className:"col-span-6 sm:col-span-4",children:[(0,t.jsx)("label",{htmlFor:"facebook",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Facebook Page or Profile (Optional)"}),(0,t.jsxs)("div",{className:"mt-1 flex rounded-md shadow-sm",children:[(0,t.jsx)("span",{className:"inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm",children:(0,t.jsx)(u.iYk,{className:"h-4 w-4 text-blue-600"})}),(0,t.jsx)("input",{type:"text",name:"facebook",id:"facebook",className:"focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-white",placeholder:"https://facebook.com/yourpage or username",value:B,onChange:e=>D(e.target.value)})]})]}),(0,t.jsxs)("div",{className:"col-span-6 sm:col-span-4",children:[(0,t.jsx)("label",{htmlFor:"tiktok",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"TikTok Username (Optional)"}),(0,t.jsxs)("div",{className:"mt-1 flex rounded-md shadow-sm",children:[(0,t.jsx)("span",{className:"inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm",children:(0,t.jsx)(u.kkU,{className:"h-4 w-4 text-black dark:text-white"})}),(0,t.jsx)("input",{type:"text",name:"tiktok",id:"tiktok",className:"focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-white",placeholder:"@username",value:Y,onChange:e=>H(e.target.value)})]})]})]})})]})}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("button",{type:"submit",disabled:p,className:"inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:[(0,t.jsx)(m.Bc_,{className:"mr-2 -ml-1 h-5 w-5"}),p?"Saving...":N?"Update Store":"Create Store"]}),N&&j&&(0,t.jsxs)("button",{type:"button",onClick:()=>{j&&j.slug&&window.open("/store/".concat(j.slug),"_blank")},className:"inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:bg-gray-600",children:[(0,t.jsx)(m.ayE,{className:"mr-2 -ml-1 h-5 w-5"}),"Preview Store"]})]})]}),N&&j&&(0,t.jsx)("div",{className:"mt-8",children:(0,t.jsx)(i.A,{currentStore:j,onTemplateUpdate:e=>{v(e),k("Store template updated successfully!")}})})]})]})})})})}}},e=>{var a=a=>e(e.s=a);e.O(0,[844,711,673,766,108,462,122,441,684,358],()=>a(1537)),_N_E=e.O()}]);