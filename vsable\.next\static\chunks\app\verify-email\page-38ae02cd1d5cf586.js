(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[839],{2731:(e,r,t)=>{"use strict";t.d(r,{Ay:()=>s});var a=t(5155);t(2115);let s=e=>{let{size:r="md",color:t="black",text:s,fullScreen:i=!1}=e,n=(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,a.jsx)("div",{className:"\n          ".concat({sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12",xl:"h-16 w-16"}[r]," \n          ").concat({black:"border-black border-t-transparent",white:"border-white border-t-transparent",gray:"border-gray-300 border-t-transparent dark:border-gray-600"}[t]," \n          border-2 rounded-full animate-spin\n        ")}),s&&(0,a.jsx)("p",{className:"mt-3 ".concat({sm:"text-sm",md:"text-base",lg:"text-lg",xl:"text-xl"}[r]," text-gray-600 dark:text-gray-400"),children:s})]});return i?(0,a.jsx)("div",{className:"fixed inset-0 bg-white dark:bg-gray-900 flex items-center justify-center z-50",children:n}):n}},5695:(e,r,t)=>{"use strict";var a=t(8999);t.o(a,"useParams")&&t.d(r,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},6277:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var a=t(5155),s=t(2115),i=t(6874),n=t.n(i),l=t(5695),c=t(3464),d=t(351),o=t(2731);let x="http://localhost:5000/api";function m(){let[e,r]=(0,s.useState)(!0),[t,i]=(0,s.useState)(!1),[m,u]=(0,s.useState)(""),[g,h]=(0,s.useState)(!1),[f,y]=(0,s.useState)(!1),[b,k]=(0,s.useState)(""),j=(0,l.useSearchParams)(),p=(0,l.useRouter)(),v=(0,s.useCallback)(async e=>{try{await c.A.post("".concat(x,"/auth/verify-email"),{token:e}),i(!0),setTimeout(()=>{p.push("/login?verified=true")},3e3)}catch(e){var t,a;u(c.A.isAxiosError(e)?(null==(a=e.response)||null==(t=a.data)?void 0:t.message)||"Invalid or expired verification link":"Verification failed")}finally{r(!1)}},[p]);(0,s.useEffect)(()=>{let e=j.get("token"),t=j.get("email");e?v(e):(t?k(t):u("Invalid verification link. Please check your email for the correct link."),r(!1))},[j,v]);let N=async()=>{if(!b)return void u("Email address is required to resend verification.");h(!0),u(""),y(!1);try{await c.A.post("".concat(x,"/auth/send-verification-email"),{email:b}),y(!0),setTimeout(()=>y(!1),5e3)}catch(t){var e,r;u(c.A.isAxiosError(t)&&(null==(r=t.response)||null==(e=r.data)?void 0:e.message)||"Failed to resend verification email")}finally{h(!1)}};return e?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",children:(0,a.jsx)(o.Ay,{size:"lg",text:"Verifying your email..."})}):t?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mx-auto h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-6",children:(0,a.jsx)(d.YrT,{className:"h-8 w-8 text-green-600 dark:text-green-400"})}),(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Email Verified!"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600 dark:text-gray-400",children:"Your email has been successfully verified. You can now access all features of your account."}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-500 dark:text-gray-500",children:"Redirecting to login page..."})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)(n(),{href:"/login",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-colors",children:"Continue to Login"})})]})}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mx-auto h-16 w-16 rounded-full flex items-center justify-center mb-6 ".concat(m?"bg-red-100 dark:bg-red-900":"bg-black dark:bg-white"),children:m?(0,a.jsx)(d.yGN,{className:"h-8 w-8 text-red-600 dark:text-red-400"}):(0,a.jsx)(d.pHD,{className:"h-8 w-8 text-white dark:text-black"})}),(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:m?"Verification Failed":"Verify Your Email"}),m?(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("p",{className:"text-red-600 dark:text-red-400 mb-4",children:m}),b&&(0,a.jsxs)("div",{className:"space-y-4",children:[f&&(0,a.jsx)("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4",children:(0,a.jsx)("p",{className:"text-sm text-green-600 dark:text-green-400",children:"Verification email sent successfully! Please check your inbox."})}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"Need a new verification link?"}),(0,a.jsx)("button",{onClick:N,disabled:g,className:"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:g?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.Ay,{size:"sm",color:"black"}),(0,a.jsx)("span",{className:"ml-2",children:"Sending..."})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d.jTZ,{className:"mr-2 h-4 w-4"}),"Resend Verification Email"]})})]})]}):(0,a.jsx)("p",{className:"mt-4 text-gray-600 dark:text-gray-400",children:"Please check your email for the verification link."})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)(n(),{href:"/login",className:"text-sm text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white transition-colors",children:"Back to login"})})]})})}function u(){return(0,a.jsx)(s.Suspense,{fallback:(0,a.jsx)(o.Ay,{fullScreen:!0,text:"Loading..."}),children:(0,a.jsx)(m,{})})}},6518:(e,r,t)=>{Promise.resolve().then(t.bind(t,6277))}},e=>{var r=r=>e(e.s=r);e.O(0,[844,673,874,441,684,358],()=>r(6518)),_N_E=e.O()}]);