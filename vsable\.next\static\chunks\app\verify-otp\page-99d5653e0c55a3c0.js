(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[734],{283:(e,t,a)=>{"use strict";a.d(t,{A:()=>d,AuthProvider:()=>c});var r=a(5155),s=a(2115),l=a(5695),n=a(3464);let i=(0,s.createContext)(void 0),o="http://localhost:5000/api",c=e=>{let{children:t}=e,[a,c]=(0,s.useState)(null),[d,u]=(0,s.useState)(!0),[x,m]=(0,s.useState)(null),h=(0,l.useRouter)();n.A.defaults.withCredentials=!0,(0,s.useEffect)(()=>{(async()=>{try{let e=await n.A.get("".concat(o,"/auth/me"));c(e.data.user)}catch(e){c(null)}finally{u(!1)}})()},[]);let f=async(e,t,a)=>{u(!0),m(null);try{let r=await n.A.post("".concat(o,"/auth/login"),{email:e,password:t,remember_me:a});c(r.data.user),"admin"===r.data.user.role?h.replace("/admin/dashboard"):"vendor"===r.data.user.role?h.replace("/vendor/dashboard"):h.replace("/")}catch(e){var r,s;throw m(n.A.isAxiosError(e)&&(null==(s=e.response)||null==(r=s.data)?void 0:r.message)||"Login failed"),e}finally{u(!1)}},g=async()=>{u(!0);try{await n.A.post("".concat(o,"/auth/logout")),c(null),h.replace("/login")}catch(a){var e,t;m(n.A.isAxiosError(a)&&(null==(t=a.response)||null==(e=t.data)?void 0:e.message)||"Logout failed")}finally{u(!1)}},y=async function(e,t,a){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"customer";u(!0),m(null);try{await n.A.post("".concat(o,"/auth/register"),{name:e,email:t,password:a,role:r}),h.replace("/login")}catch(e){var s,l;throw m(n.A.isAxiosError(e)&&(null==(l=e.response)||null==(s=l.data)?void 0:s.message)||"Registration failed"),e}finally{u(!1)}},p=async e=>{u(!0),m(null);try{await n.A.post("".concat(o,"/auth/send-otp"),{email:e})}catch(e){var t,a;throw m(n.A.isAxiosError(e)&&(null==(a=e.response)||null==(t=a.data)?void 0:t.message)||"Failed to send OTP"),e}finally{u(!1)}},b=async(e,t)=>{u(!0),m(null);try{return(await n.A.post("".concat(o,"/auth/verify-otp"),{email:e,otp:t})).data.user}catch(e){var a,r;throw m(n.A.isAxiosError(e)&&(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"Invalid OTP"),e}finally{u(!1)}},v=async e=>{u(!0),m(null);try{await n.A.post("".concat(o,"/auth/forgot-password"),{email:e})}catch(e){var t,a;throw m(n.A.isAxiosError(e)&&(null==(a=e.response)||null==(t=a.data)?void 0:t.message)||"Failed to send reset email"),e}finally{u(!1)}},w=async(e,t)=>{u(!0),m(null);try{await n.A.post("".concat(o,"/auth/reset-password"),{token:e,password:t})}catch(e){var a,r;throw m(n.A.isAxiosError(e)&&(null==(r=e.response)||null==(a=r.data)?void 0:a.message)||"Failed to reset password"),e}finally{u(!1)}},j=async e=>{u(!0),m(null);try{let t=await n.A.put("".concat(o,"/auth/profile"),e);c(t.data.user)}catch(e){var t,a;throw m(n.A.isAxiosError(e)&&(null==(a=e.response)||null==(t=a.data)?void 0:t.message)||"Failed to update profile"),e}finally{u(!1)}},k=async e=>{u(!0),m(null);try{await n.A.post("".concat(o,"/auth/send-verification-email"),{email:e})}catch(e){var t,a;throw m(n.A.isAxiosError(e)&&(null==(a=e.response)||null==(t=a.data)?void 0:t.message)||"Failed to send verification email"),e}finally{u(!1)}},A=async e=>{u(!0),m(null);try{let t=(await n.A.post("".concat(o,"/auth/verify-email"),{token:e})).data.user;return c(t),t}catch(e){var t,a;throw m(n.A.isAxiosError(e)&&(null==(a=e.response)||null==(t=a.data)?void 0:t.message)||"Email verification failed"),e}finally{u(!1)}};return(0,r.jsx)(i.Provider,{value:{user:a,loading:d,error:x,login:f,logout:g,register:y,sendOTP:p,verifyOTP:b,forgotPassword:v,resetPassword:w,updateProfile:j,sendVerificationEmail:k,verifyEmail:A,setUser:c,isAuthenticated:!!a},children:t})},d=()=>{let e=(0,s.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},1984:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>x});var r=a(5155),s=a(2115),l=a(6874),n=a.n(l),i=a(5695),o=a(283),c=a(351),d=a(2731);function u(){let[e,t]=(0,s.useState)(["","","","","",""]),[a,l]=(0,s.useState)(""),[u,x]=(0,s.useState)(!1),[m,h]=(0,s.useState)(!1),[f,g]=(0,s.useState)(""),[y,p]=(0,s.useState)(!1),[b,v]=(0,s.useState)(0),{sendOTP:w,verifyOTP:j}=(0,o.A)(),k=(0,i.useSearchParams)(),A=(0,i.useRouter)();(0,s.useEffect)(()=>{let e=k.get("email");if(!e)return void g("Email parameter is missing. Please try again.");l(e)},[k]),(0,s.useEffect)(()=>{if(b>0){let e=setTimeout(()=>v(b-1),1e3);return()=>clearTimeout(e)}},[b]);let N=(a,r)=>{if(r.length>1)return;let s=[...e];if(s[a]=r,t(s),r&&a<5){let e=document.getElementById("otp-".concat(a+1));null==e||e.focus()}},E=(t,a)=>{if("Backspace"===a.key&&!e[t]&&t>0){let e=document.getElementById("otp-".concat(t-1));null==e||e.focus()}},P=async t=>{t.preventDefault(),g("");let r=e.join("");if(6!==r.length)return void g("Please enter the complete 6-digit code");x(!0);try{await j(a,r),h(!0),setTimeout(()=>{A.push("/login")},2e3)}catch(e){console.error("OTP verification error:",e),g("Invalid or expired OTP code. Please try again.")}finally{x(!1)}},S=async()=>{if(!(b>0)){p(!0),g("");try{await w(a),v(60),t(["","","","","",""])}catch(e){console.error("OTP resend error:",e),g("Failed to resend OTP. Please try again.")}finally{p(!1)}}};return m?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-6",children:(0,r.jsx)(c.YrT,{className:"h-8 w-8 text-green-600 dark:text-green-400"})}),(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Verification Successful"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600 dark:text-gray-400",children:"Your email has been verified successfully. Redirecting to login..."})]})})}):(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto h-16 w-16 bg-black dark:bg-white rounded-full flex items-center justify-center mb-6",children:(0,r.jsx)(c.pHD,{className:"h-8 w-8 text-white dark:text-black"})}),(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Verify your email"}),(0,r.jsxs)("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:["We've sent a 6-digit code to ",(0,r.jsx)("strong",{children:a})]})]})}),(0,r.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:P,children:[f&&(0,r.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4",children:(0,r.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:f})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Enter verification code"}),(0,r.jsx)("div",{className:"flex space-x-2 justify-center",children:e.map((e,t)=>(0,r.jsx)("input",{id:"otp-".concat(t),type:"text",maxLength:1,value:e,onChange:e=>N(t,e.target.value),onKeyDown:e=>E(t,e),className:"w-12 h-12 text-center text-xl font-bold border-2 border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-black dark:focus:ring-white dark:focus:border-white bg-white dark:bg-gray-800 text-gray-900 dark:text-white",disabled:u},t))})]}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",disabled:u||6!==e.join("").length,className:"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:u?(0,r.jsx)(d.Ay,{size:"sm",color:"white"}):"Verify Code"})}),(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Didn't receive the code?"}),(0,r.jsx)("button",{type:"button",onClick:S,disabled:y||b>0,className:"inline-flex items-center text-sm font-medium text-black dark:text-white hover:text-gray-700 dark:hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:y?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.Ay,{size:"sm",color:"black"}),(0,r.jsx)("span",{className:"ml-2",children:"Sending..."})]}):b>0?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.jTZ,{className:"mr-2 h-4 w-4"}),"Resend in ",b,"s"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.jTZ,{className:"mr-2 h-4 w-4"}),"Resend code"]})})]}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)(n(),{href:"/login",className:"inline-flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white transition-colors",children:[(0,r.jsx)(c.kRp,{className:"mr-2 h-4 w-4"}),"Back to login"]})})]})]})})}function x(){return(0,r.jsx)(s.Suspense,{fallback:(0,r.jsx)(d.Ay,{fullScreen:!0,text:"Loading..."}),children:(0,r.jsx)(u,{})})}},2731:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>s});var r=a(5155);a(2115);let s=e=>{let{size:t="md",color:a="black",text:s,fullScreen:l=!1}=e,n=(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,r.jsx)("div",{className:"\n          ".concat({sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12",xl:"h-16 w-16"}[t]," \n          ").concat({black:"border-black border-t-transparent",white:"border-white border-t-transparent",gray:"border-gray-300 border-t-transparent dark:border-gray-600"}[a]," \n          border-2 rounded-full animate-spin\n        ")}),s&&(0,r.jsx)("p",{className:"mt-3 ".concat({sm:"text-sm",md:"text-base",lg:"text-lg",xl:"text-xl"}[t]," text-gray-600 dark:text-gray-400"),children:s})]});return l?(0,r.jsx)("div",{className:"fixed inset-0 bg-white dark:bg-gray-900 flex items-center justify-center z-50",children:n}):n}},5695:(e,t,a)=>{"use strict";var r=a(8999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},8303:(e,t,a)=>{Promise.resolve().then(a.bind(a,1984))}},e=>{var t=t=>e(e.s=t);e.O(0,[844,673,874,441,684,358],()=>t(8303)),_N_E=e.O()}]);