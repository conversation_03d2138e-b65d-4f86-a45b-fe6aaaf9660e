{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/StartUpJourney/Development/Test_dev/vsable/src/utils/imageUtils.ts"], "sourcesContent": ["/**\n * Utility functions for handling image paths\n */\n\n// API base URL without the /api suffix\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL\n  ? process.env.NEXT_PUBLIC_API_URL.replace(/\\/api$/, '')\n  : 'http://localhost:5000';\n\n// MinIO URL - direct access (force HTTPS)\nconst MINIO_URL = process.env.NEXT_PUBLIC_MINIO_URL\n  ? process.env.NEXT_PUBLIC_MINIO_URL.replace(/^http:/, 'https:')\n  : 'https://**************:9000';\n\n// Use this for proxied access through the backend\nconst PROXY_URL = `${API_BASE_URL}/api/upload/serve`;\n\n/**\n * Ensures that image paths are properly formatted for Next.js Image component\n * Next.js requires image paths to start with a leading slash or be absolute URLs\n */\nexport function getImagePath(src: string): string {\n  if (!src) return '';\n\n  let result = '';\n\n  // If it's already a MinIO URL, convert it to use the proxy\n  if (src.includes('**************:9000')) {\n    const filename = src.split('/').pop();\n    if (filename) {\n      result = `${PROXY_URL}/${filename}`;\n      logImagePathTransformation(src, result, 'MinIO direct URL converted to proxy');\n      return result;\n    }\n  }\n\n  // If it's already an absolute URL and not a MinIO URL, return as is\n  if ((src.startsWith('http://') || src.startsWith('https://'))\n      && !src.includes('**************:9000')) {\n    result = src;\n    logImagePathTransformation(src, result, 'External absolute URL');\n    return result;\n  }\n\n  // Handle protocol-relative URLs\n  if (src.startsWith('//')) {\n    result = `https:${src}`;\n    logImagePathTransformation(src, result, 'Protocol-relative URL fixed');\n    return result;\n  }\n\n  // If it starts with 'uploads/' or 'images/', add a leading slash\n  if (src.startsWith('uploads/') || src.startsWith('images/')) {\n    // Extract the filename if it has a UUID pattern\n    const filename = src.split('/').pop();\n    if (filename && filename.match(/[0-9a-f]{32}_/)) {\n      result = `${PROXY_URL}/${filename}`;\n      logImagePathTransformation(src, result, 'Path with UUID pattern');\n      return result;\n    }\n\n    // Otherwise, add a leading slash for Next.js\n    result = `/${src}`;\n    logImagePathTransformation(src, result, 'Added leading slash for Next.js');\n    return result;\n  }\n\n  // Clean the path (but don't remove leading slashes for Next.js)\n  const cleanPath = src.startsWith('/') ? src : src.replace(/^\\/+/, '');\n\n  // If it contains a UUID pattern, it's likely a MinIO file\n  if (cleanPath.match(/[0-9a-f]{32}_/)) {\n    const filename = cleanPath.split('/').pop();\n    result = `${PROXY_URL}/${filename}`;\n    logImagePathTransformation(src, result, 'UUID-based filename');\n    return result;\n  }\n\n  // Handle uploads directory\n  if (cleanPath.includes('uploads/')) {\n    const filename = cleanPath.split('/').pop();\n    result = `${PROXY_URL}/${filename}`;\n    logImagePathTransformation(src, result, 'Uploads directory');\n    return result;\n  }\n\n  // For all other cases, ensure it has a leading slash or use the proxy\n  if (cleanPath.startsWith('/')) {\n    result = cleanPath;\n  } else {\n    result = `/${cleanPath}`;\n  }\n\n  logImagePathTransformation(src, result, 'Ensured leading slash for Next.js');\n  return result;\n}\n\n/**\n * Gets the correct URL for uploaded files\n * Ensures paths are compatible with Next.js Image component\n */\nexport function getUploadUrl(path: string): string {\n  if (!path) return '';\n\n  // Handle protocol-relative URLs\n  if (path.startsWith('//')) {\n    return `https:${path}`;\n  }\n\n  // If it's already an absolute URL and not a MinIO URL, return as is\n  if ((path.startsWith('http://') || path.startsWith('https://'))\n      && !path.includes('**************:9000')) {\n    return path;\n  }\n\n  // For MinIO URLs, use the proxy\n  if (path.includes('**************:9000')) {\n    const filename = path.split('/').pop();\n    return `${PROXY_URL}/${filename}`;\n  }\n\n  // If it starts with 'uploads/' or 'images/', add a leading slash for Next.js\n  if (path.startsWith('uploads/') || path.startsWith('images/')) {\n    // Extract the filename if it has a UUID pattern\n    const filename = path.split('/').pop();\n    if (filename && filename.match(/[0-9a-f]{32}_/)) {\n      return `${PROXY_URL}/${filename}`;\n    }\n\n    // Otherwise, add a leading slash for Next.js\n    return `/${path}`;\n  }\n\n  // Clean the path (but preserve leading slashes for Next.js)\n  const cleanPath = path.startsWith('/') ? path : path.replace(/^\\/+/, '');\n\n  // If it contains a UUID pattern, it's likely a MinIO file\n  if (cleanPath.match(/[0-9a-f]{32}_/)) {\n    const filename = cleanPath.split('/').pop();\n    return `${PROXY_URL}/${filename}`;\n  }\n\n  // Handle uploads directory\n  if (cleanPath.includes('uploads/')) {\n    const filename = cleanPath.split('/').pop();\n    return `${PROXY_URL}/${filename}`;\n  }\n\n  // For all other cases, ensure it has a leading slash\n  if (cleanPath.startsWith('/')) {\n    return cleanPath;\n  } else {\n    return `/${cleanPath}`;\n  }\n}\n\n/**\n * Gets a direct MinIO URL for a file\n */\nexport function getMinioUrl(path: string): string {\n  if (!path) return '';\n\n  // If it's already a MinIO URL, return as is\n  if (path.includes('**************:9000')) {\n    return path;\n  }\n\n  // Extract the filename\n  const filename = path.split('/').pop();\n  if (!filename) return '';\n\n  // Return the MinIO URL\n  return `${MINIO_URL}/uploads/${filename}`;\n}\n\n/**\n * Debug utility to log image path transformations\n */\nexport function logImagePathTransformation(\n  originalPath: string,\n  transformedPath: string,\n  context: string = ''\n): void {\n  if (process.env.NODE_ENV === 'development') {\n    console.log(`Image path transformation (${context}):`, {\n      from: originalPath,\n      to: transformedPath\n    });\n  }\n}\n\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,uCAAuC;;;;;;;AAClB;AAArB,MAAM,eAAe,uCACjB,8DAAgC,OAAO,CAAC,UAAU;AAGtD,0CAA0C;AAC1C,MAAM,YAAY,uCACd,gEAAkC,OAAO,CAAC,UAAU;AAGxD,kDAAkD;AAClD,MAAM,YAAY,GAAG,aAAa,iBAAiB,CAAC;AAM7C,SAAS,aAAa,GAAW;IACtC,IAAI,CAAC,KAAK,OAAO;IAEjB,IAAI,SAAS;IAEb,2DAA2D;IAC3D,IAAI,IAAI,QAAQ,CAAC,wBAAwB;QACvC,MAAM,WAAW,IAAI,KAAK,CAAC,KAAK,GAAG;QACnC,IAAI,UAAU;YACZ,SAAS,GAAG,UAAU,CAAC,EAAE,UAAU;YACnC,2BAA2B,KAAK,QAAQ;YACxC,OAAO;QACT;IACF;IAEA,oEAAoE;IACpE,IAAI,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,UAAU,CAAC,WAAW,KACrD,CAAC,IAAI,QAAQ,CAAC,wBAAwB;QAC3C,SAAS;QACT,2BAA2B,KAAK,QAAQ;QACxC,OAAO;IACT;IAEA,gCAAgC;IAChC,IAAI,IAAI,UAAU,CAAC,OAAO;QACxB,SAAS,CAAC,MAAM,EAAE,KAAK;QACvB,2BAA2B,KAAK,QAAQ;QACxC,OAAO;IACT;IAEA,iEAAiE;IACjE,IAAI,IAAI,UAAU,CAAC,eAAe,IAAI,UAAU,CAAC,YAAY;QAC3D,gDAAgD;QAChD,MAAM,WAAW,IAAI,KAAK,CAAC,KAAK,GAAG;QACnC,IAAI,YAAY,SAAS,KAAK,CAAC,kBAAkB;YAC/C,SAAS,GAAG,UAAU,CAAC,EAAE,UAAU;YACnC,2BAA2B,KAAK,QAAQ;YACxC,OAAO;QACT;QAEA,6CAA6C;QAC7C,SAAS,CAAC,CAAC,EAAE,KAAK;QAClB,2BAA2B,KAAK,QAAQ;QACxC,OAAO;IACT;IAEA,gEAAgE;IAChE,MAAM,YAAY,IAAI,UAAU,CAAC,OAAO,MAAM,IAAI,OAAO,CAAC,QAAQ;IAElE,0DAA0D;IAC1D,IAAI,UAAU,KAAK,CAAC,kBAAkB;QACpC,MAAM,WAAW,UAAU,KAAK,CAAC,KAAK,GAAG;QACzC,SAAS,GAAG,UAAU,CAAC,EAAE,UAAU;QACnC,2BAA2B,KAAK,QAAQ;QACxC,OAAO;IACT;IAEA,2BAA2B;IAC3B,IAAI,UAAU,QAAQ,CAAC,aAAa;QAClC,MAAM,WAAW,UAAU,KAAK,CAAC,KAAK,GAAG;QACzC,SAAS,GAAG,UAAU,CAAC,EAAE,UAAU;QACnC,2BAA2B,KAAK,QAAQ;QACxC,OAAO;IACT;IAEA,sEAAsE;IACtE,IAAI,UAAU,UAAU,CAAC,MAAM;QAC7B,SAAS;IACX,OAAO;QACL,SAAS,CAAC,CAAC,EAAE,WAAW;IAC1B;IAEA,2BAA2B,KAAK,QAAQ;IACxC,OAAO;AACT;AAMO,SAAS,aAAa,IAAY;IACvC,IAAI,CAAC,MAAM,OAAO;IAElB,gCAAgC;IAChC,IAAI,KAAK,UAAU,CAAC,OAAO;QACzB,OAAO,CAAC,MAAM,EAAE,MAAM;IACxB;IAEA,oEAAoE;IACpE,IAAI,CAAC,KAAK,UAAU,CAAC,cAAc,KAAK,UAAU,CAAC,WAAW,KACvD,CAAC,KAAK,QAAQ,CAAC,wBAAwB;QAC5C,OAAO;IACT;IAEA,gCAAgC;IAChC,IAAI,KAAK,QAAQ,CAAC,wBAAwB;QACxC,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK,GAAG;QACpC,OAAO,GAAG,UAAU,CAAC,EAAE,UAAU;IACnC;IAEA,6EAA6E;IAC7E,IAAI,KAAK,UAAU,CAAC,eAAe,KAAK,UAAU,CAAC,YAAY;QAC7D,gDAAgD;QAChD,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK,GAAG;QACpC,IAAI,YAAY,SAAS,KAAK,CAAC,kBAAkB;YAC/C,OAAO,GAAG,UAAU,CAAC,EAAE,UAAU;QACnC;QAEA,6CAA6C;QAC7C,OAAO,CAAC,CAAC,EAAE,MAAM;IACnB;IAEA,4DAA4D;IAC5D,MAAM,YAAY,KAAK,UAAU,CAAC,OAAO,OAAO,KAAK,OAAO,CAAC,QAAQ;IAErE,0DAA0D;IAC1D,IAAI,UAAU,KAAK,CAAC,kBAAkB;QACpC,MAAM,WAAW,UAAU,KAAK,CAAC,KAAK,GAAG;QACzC,OAAO,GAAG,UAAU,CAAC,EAAE,UAAU;IACnC;IAEA,2BAA2B;IAC3B,IAAI,UAAU,QAAQ,CAAC,aAAa;QAClC,MAAM,WAAW,UAAU,KAAK,CAAC,KAAK,GAAG;QACzC,OAAO,GAAG,UAAU,CAAC,EAAE,UAAU;IACnC;IAEA,qDAAqD;IACrD,IAAI,UAAU,UAAU,CAAC,MAAM;QAC7B,OAAO;IACT,OAAO;QACL,OAAO,CAAC,CAAC,EAAE,WAAW;IACxB;AACF;AAKO,SAAS,YAAY,IAAY;IACtC,IAAI,CAAC,MAAM,OAAO;IAElB,4CAA4C;IAC5C,IAAI,KAAK,QAAQ,CAAC,wBAAwB;QACxC,OAAO;IACT;IAEA,uBAAuB;IACvB,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK,GAAG;IACpC,IAAI,CAAC,UAAU,OAAO;IAEtB,uBAAuB;IACvB,OAAO,GAAG,UAAU,SAAS,EAAE,UAAU;AAC3C;AAKO,SAAS,2BACd,YAAoB,EACpB,eAAuB,EACvB,UAAkB,EAAE;IAEpB,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,QAAQ,EAAE,CAAC,EAAE;YACrD,MAAM;YACN,IAAI;QACN;IACF;AACF", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/StartUpJourney/Development/Test_dev/vsable/src/components/SafeImage.tsx"], "sourcesContent": ["'use client';\n\nimport NextImage, { ImageProps } from 'next/image';\nimport { getImagePath } from '@/utils/imageUtils';\nimport { useState, useEffect } from 'react';\n\ninterface SafeImageProps extends Omit<ImageProps, 'src'> {\n  src: string;\n  debug?: boolean;\n}\n\n/**\n * A wrapper around Next.js Image component that ensures image paths are properly formatted\n */\nconst SafeImage = ({ src, debug = false, ...props }: SafeImageProps) => {\n  const [error, setError] = useState<string | null>(null);\n  const safeSrc = getImagePath(src);\n  const isExternal = safeSrc.startsWith('http://') || safeSrc.startsWith('https://');\n\n  useEffect(() => {\n    if (debug) {\n      console.log(`[SafeImage] Original: \"${src}\" → Safe: \"${safeSrc}\"`);\n    }\n  }, [src, safeSrc, debug]);\n\n  const handleError = () => {\n    setError(`Failed to load image: ${safeSrc}`);\n    console.error(`[SafeImage] Error loading image: \"${safeSrc}\" (original: \"${src}\")`);\n    \n    // Try fallback to direct MinIO URL if proxy fails\n    if (safeSrc.includes('/api/upload/serve/')) {\n      const filename = safeSrc.split('/').pop();\n      const fallbackUrl = `${process.env.NEXT_PUBLIC_MINIO_URL}/uploads/${filename}`;\n      console.log(`[SafeImage] Trying fallback URL: ${fallbackUrl}`);\n      // Update the image src\n      if (typeof window !== 'undefined') {\n        const img = new window.Image();\n        img.src = fallbackUrl;\n        img.onload = () => {\n          setError(null);\n        };\n      }\n    }\n  };\n\n  if (error) {\n    return (\n      <div className=\"flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-md p-2\">\n        <span className=\"text-xs text-gray-500 dark:text-gray-400\">Image not available</span>\n      </div>\n    );\n  }\n  return (\n    <NextImage\n      src={safeSrc}\n      {...props}\n      alt={props.alt || \"Image\"}\n      onError={handleError}\n      unoptimized={isExternal}\n    />\n  )\n}\n\nexport default SafeImage;\n\n"], "names": [], "mappings": ";;;AAgC6B;;AA9B7B;AACA;AACA;;;AAJA;;;;AAWA;;CAEC,GACD,MAAM,YAAY,CAAC,EAAE,GAAG,EAAE,QAAQ,KAAK,EAAE,GAAG,OAAuB;;IACjE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,UAAU,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD,EAAE;IAC7B,MAAM,aAAa,QAAQ,UAAU,CAAC,cAAc,QAAQ,UAAU,CAAC;IAEvE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,OAAO;gBACT,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,IAAI,WAAW,EAAE,QAAQ,CAAC,CAAC;YACnE;QACF;8BAAG;QAAC;QAAK;QAAS;KAAM;IAExB,MAAM,cAAc;QAClB,SAAS,CAAC,sBAAsB,EAAE,SAAS;QAC3C,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,QAAQ,cAAc,EAAE,IAAI,EAAE,CAAC;QAElF,kDAAkD;QAClD,IAAI,QAAQ,QAAQ,CAAC,uBAAuB;YAC1C,MAAM,WAAW,QAAQ,KAAK,CAAC,KAAK,GAAG;YACvC,MAAM,cAAc,mEAAqC,SAAS,EAAE,UAAU;YAC9E,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,aAAa;YAC7D,uBAAuB;YACvB,wCAAmC;gBACjC,MAAM,MAAM,IAAI,OAAO,KAAK;gBAC5B,IAAI,GAAG,GAAG;gBACV,IAAI,MAAM,GAAG;oBACX,SAAS;gBACX;YACF;QACF;IACF;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAK,WAAU;0BAA2C;;;;;;;;;;;IAGjE;IACA,qBACE,6LAAC,gIAAA,CAAA,UAAS;QACR,KAAK;QACJ,GAAG,KAAK;QACT,KAAK,MAAM,GAAG,IAAI;QAClB,SAAS;QACT,aAAa;;;;;;AAGnB;GA/CM;KAAA;uCAiDS", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/StartUpJourney/Development/Test_dev/vsable/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport SafeImage from '@/components/SafeImage';\nimport Link from 'next/link';\nimport axios from 'axios';\nimport { FiSearch, FiImage, FiArrowRight } from 'react-icons/fi';\n\n// API base URL\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\n\ninterface Store {\n  id: number;\n  name: string;\n  slug: string;\n  description: string | null;\n  logo_url: string | null;\n  telegram: string | null;\n  whatsapp: string | null;\n  phone: string | null;\n  instagram: string | null;\n  user_id: number;\n  created_at: string;\n  updated_at: string;\n}\n\nexport default function Home() {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [stores, setStores] = useState<Store[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchStores = async () => {\n      try {\n        const response = await axios.get(`${API_URL}/store/`);\n        setStores(response.data.stores);\n        setLoading(false);\n      } catch {\n        setError('Failed to load stores');\n        setLoading(false);\n      }\n    };\n\n    fetchStores();\n  }, []);\n\n  return (\n    <div className=\"min-h-screen bg-white dark:bg-gray-900\">\n      {/* Hero Section */}\n      <div className=\"relative bg-white dark:bg-gray-900 border-b border-gray-100 dark:border-gray-800\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20 md:py-28\">\n          <div className=\"text-center\">\n            <h1 className=\"text-5xl md:text-7xl font-light text-black dark:text-white mb-6\">\n              Vsable\n            </h1>\n            <p className=\"text-xl md:text-2xl text-gray-600 dark:text-gray-300 font-light mb-12 max-w-2xl mx-auto leading-relaxed\">\n              Shop from multiple vendors in one place\n            </p>\n            <div className=\"max-w-2xl mx-auto\">\n              <div className=\"relative\">\n                <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n                  <FiSearch className=\"h-5 w-5 text-gray-400\" />\n                </div>\n                <input\n                  type=\"text\"\n                  className=\"block w-full pl-12 pr-4 py-4 text-lg border border-gray-200 dark:border-gray-700 rounded-xl bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-black dark:focus:ring-white focus:border-transparent transition-all\"\n                  placeholder=\"Search for vendors or products...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Featured Vendors */}\n      <div className=\"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16\">\n        <h2 className=\"text-3xl font-light text-black dark:text-white mb-12 text-center\">Featured Vendors</h2>\n\n        {loading ? (\n          <div className=\"flex justify-center py-16\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-black dark:border-white\"></div>\n          </div>\n        ) : error ? (\n          <div className=\"text-center py-16\">\n            <p className=\"text-red-600 dark:text-red-400 font-light\">{error}</p>\n          </div>\n        ) : stores.length === 0 ? (\n          <div className=\"text-center py-16\">\n            <p className=\"text-gray-500 dark:text-gray-400 font-light\">No stores available yet.</p>\n          </div>\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {stores.map((store) => (\n              <Link href={`/store/${store.slug}`} key={store.id} className=\"group\">\n                <div className=\"bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-700 rounded-2xl hover:border-gray-200 dark:hover:border-gray-600 transition-all duration-300 overflow-hidden\">\n                  <div className=\"p-8\">\n                    <div className=\"flex items-center mb-6\">\n                      <div className=\"h-16 w-16 relative rounded-2xl overflow-hidden bg-gray-50 dark:bg-gray-700 flex-shrink-0\">\n                        {store.logo_url ? (\n                          <SafeImage\n                            src={store.logo_url || ''}\n                            alt={store.name}\n                            width={64}\n                            height={64}\n                            className=\"h-full w-full object-cover\"\n                          />\n                        ) : (\n                          <div className=\"h-full w-full flex items-center justify-center\">\n                            <FiImage className=\"h-8 w-8 text-gray-400\" />\n                          </div>\n                        )}\n                      </div>\n                      <div className=\"ml-4\">\n                        <h3 className=\"text-xl font-medium text-black dark:text-white group-hover:text-gray-700 dark:group-hover:text-gray-300 transition-colors\">{store.name}</h3>\n                      </div>\n                    </div>\n                    {store.description && (\n                      <p className=\"text-gray-600 dark:text-gray-400 font-light leading-relaxed line-clamp-3 mb-6\">{store.description}</p>\n                    )}\n                    <div className=\"flex items-center text-black dark:text-white font-medium group-hover:translate-x-1 transition-transform\">\n                      Visit Store\n                      <FiArrowRight className=\"ml-2 h-4 w-4\" />\n                    </div>\n                  </div>\n                </div>\n              </Link>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Call to Action for Vendors */}\n      <div className=\"bg-gray-50 dark:bg-gray-800 border-t border-gray-100 dark:border-gray-700\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n          <div className=\"text-center\">\n            <h2 className=\"text-3xl md:text-4xl font-light text-black dark:text-white mb-6\">\n              Are you a vendor?\n            </h2>\n            <p className=\"text-xl text-gray-600 dark:text-gray-300 font-light mb-12 max-w-2xl mx-auto leading-relaxed\">\n              Join our platform and start selling your products to a wider audience today.\n            </p>\n            <Link\n              href=\"/register\"\n              className=\"inline-flex items-center px-8 py-4 text-lg font-medium text-white bg-black dark:bg-white dark:text-black rounded-xl hover:bg-gray-800 dark:hover:bg-gray-100 transition-all duration-300\"\n            >\n              Register as a Vendor\n              <FiArrowRight className=\"ml-2 h-5 w-5\" />\n            </Link>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;AASgB;;AAPhB;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQA,eAAe;AACf,MAAM,UAAU,iEAAmC;AAkBpC,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM;8CAAc;oBAClB,IAAI;wBACF,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,GAAG,QAAQ,OAAO,CAAC;wBACpD,UAAU,SAAS,IAAI,CAAC,MAAM;wBAC9B,WAAW;oBACb,EAAE,OAAM;wBACN,SAAS;wBACT,WAAW;oBACb;gBACF;;YAEA;QACF;yBAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAkE;;;;;;0CAGhF,6LAAC;gCAAE,WAAU;0CAA0G;;;;;;0CAGvH,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,iJAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;4CACC,MAAK;4CACL,WAAU;4CACV,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS1D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmE;;;;;;oBAEhF,wBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;+BAEf,sBACF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAA6C;;;;;;;;;;+BAE1D,OAAO,MAAM,KAAK,kBACpB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAA8C;;;;;;;;;;6CAG7D,6LAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAM,CAAC,OAAO,EAAE,MAAM,IAAI,EAAE;gCAAiB,WAAU;0CAC3D,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,MAAM,QAAQ,iBACb,6LAAC,kIAAA,CAAA,UAAS;4DACR,KAAK,MAAM,QAAQ,IAAI;4DACvB,KAAK,MAAM,IAAI;4DACf,OAAO;4DACP,QAAQ;4DACR,WAAU;;;;;iFAGZ,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,iJAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAIzB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAG,WAAU;sEAA6H,MAAM,IAAI;;;;;;;;;;;;;;;;;4CAGxJ,MAAM,WAAW,kBAChB,6LAAC;gDAAE,WAAU;0DAAiF,MAAM,WAAW;;;;;;0DAEjH,6LAAC;gDAAI,WAAU;;oDAA0G;kEAEvH,6LAAC,iJAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+BA5BS,MAAM,EAAE;;;;;;;;;;;;;;;;0BAuCzD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAkE;;;;;;0CAGhF,6LAAC;gCAAE,WAAU;0CAA8F;;;;;;0CAG3G,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;oCACX;kDAEC,6LAAC,iJAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC;GAlIwB;KAAA", "debugId": null}}]}