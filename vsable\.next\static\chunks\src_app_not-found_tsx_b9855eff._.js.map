{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/StartUpJourney/Development/Test_dev/vsable/src/app/not-found.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { <PERSON>H<PERSON>, FiArrowLeft, FiSearch } from 'react-icons/fi';\n\nexport default function NotFound() {\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center px-4\">\n      <div className=\"max-w-lg w-full text-center\">\n        {/* 404 Number */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-9xl font-bold text-black dark:text-white opacity-20\">\n            404\n          </h1>\n        </div>\n\n        {/* Error Message */}\n        <div className=\"mb-8\">\n          <h2 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-4\">\n            Page Not Found\n          </h2>\n          <p className=\"text-lg text-gray-600 dark:text-gray-400 mb-2\">\n            The page you&apos;re looking for doesn&apos;t exist.\n          </p>\n          <p className=\"text-gray-500 dark:text-gray-500\">\n            It might have been moved, deleted, or you entered the wrong URL.\n          </p>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n          <Link\n            href=\"/\"\n            className=\"inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-colors\"\n          >\n            <FiHome className=\"mr-2 h-5 w-5\" />\n            Go Home\n          </Link>\n\n          <button\n            onClick={() => window.history.back()}\n            className=\"inline-flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-base font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-colors\"\n          >\n            <FiArrowLeft className=\"mr-2 h-5 w-5\" />\n            Go Back\n          </button>\n        </div>\n\n        {/* Search Suggestion */}\n        <div className=\"mt-8 pt-8 border-t border-gray-200 dark:border-gray-700\">\n          <p className=\"text-sm text-gray-500 dark:text-gray-400 mb-4\">\n            Looking for something specific?\n          </p>\n          <Link\n            href=\"/\"\n            className=\"inline-flex items-center text-black dark:text-white hover:text-gray-700 dark:hover:text-gray-300 transition-colors\"\n          >\n            <FiSearch className=\"mr-2 h-4 w-4\" />\n            Browse our marketplace\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;;;;;;8BAM3E,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,6LAAC;4BAAE,WAAU;sCAAgD;;;;;;sCAG7D,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;8BAMlD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6LAAC,iJAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIrC,6LAAC;4BACC,SAAS,IAAM,OAAO,OAAO,CAAC,IAAI;4BAClC,WAAU;;8CAEV,6LAAC,iJAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;8BAM5C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAgD;;;;;;sCAG7D,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6LAAC,iJAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAOjD;KA3DwB", "debugId": null}}]}