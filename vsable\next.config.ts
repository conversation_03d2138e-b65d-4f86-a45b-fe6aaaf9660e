/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: 'https',  // Force HTTPS
        hostname: '**************',
        port: '9000',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '5000',
        pathname: '/api/upload/serve/**',
      }
    ],
    domains: [
      'images.unsplash.com',
      '1000logos.net',
      'localhost',
      '127.0.0.1',
      '************',
      '**************',
    ],
  },
};

module.exports = nextConfig;

