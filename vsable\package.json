{"name": "vsable", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "deploy": "npx wrangler pages deploy .next --project-name vsable"}, "dependencies": {"axios": "^1.9.0", "firebase": "^11.8.1", "framer-motion": "^12.12.1", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-lazy-load-image-component": "^1.6.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-lazy-load-image-component": "^1.6.4", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5", "wrangler": "^4.15.2"}}