'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import GoogleSignIn from '@/components/GoogleSignIn';

export default function Login() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [formError, setFormError] = useState('');
  // const [showOtpOption, setShowOtpOption] = useState(false);
  const [otpMode, setOtpMode] = useState(false);
  const { login, loading, error, sendOTP } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setFormError('');

    if (otpMode) {
      // OTP login mode - redirect to OTP verification
      if (!email) {
        setFormError('Please enter your email address');
        return;
      }

      try {
        await sendOTP(email);
        // Redirect to OTP verification page
        window.location.href = `/verify-otp?email=${encodeURIComponent(email)}`;
      } catch {
        // Error is handled by the auth context
      }
    } else {
      // Regular login mode
      if (!email || !password) {
        setFormError('Please enter both email and password');
        return;
      }

      try {
        await login(email, password, rememberMe);
      } catch {
        // Error is handled by the auth context
      }
    }
  };

  const handleOtpToggle = () => {
    setOtpMode(!otpMode);
    setPassword('');
    setFormError('');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900 dark:text-white">
            Sign in to your account
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600 dark:text-gray-400">
            Or{' '}
            <Link href="/register" className="font-medium text-black hover:text-gray-700 dark:text-white dark:hover:text-gray-300">
              create a new account
            </Link>
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          {(error || formError) && (
            <div className="rounded-md bg-red-50 p-4">
              <div className="flex">
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    {formError || error}
                  </h3>
                </div>
              </div>
            </div>
          )}

          <div className="rounded-md shadow-sm -space-y-px">
            <div>
              <label htmlFor="email-address" className="sr-only">
                Email address
              </label>
              <input
                id="email-address"
                name="email"
                type="email"
                autoComplete="email"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                placeholder="Email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <div>
              <label htmlFor="password" className="sr-only">
                Password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                required={!otpMode}
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-black focus:border-black focus:z-10 sm:text-sm dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={loading || otpMode}
                style={{ display: otpMode ? 'none' : 'block' }}
              />
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center justify-center">
              <button
                type="button"
                onClick={handleOtpToggle}
                className="text-sm text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white transition-colors"
              >
                {otpMode ? 'Use password instead' : 'Login with OTP'}
              </button>
            </div>

            <div className="flex items-center justify-between">
              {!otpMode && (
                <div className="flex items-center">
                  <input
                    id="remember-me"
                    name="remember-me"
                    type="checkbox"
                    className="h-4 w-4 text-black focus:ring-black border-gray-300 rounded dark:bg-gray-800 dark:border-gray-700"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                  />
                  <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900 dark:text-gray-300">
                    Remember me
                  </label>
                </div>
              )}

              <div className="text-sm">
                <Link href="/forgot-password" className="font-medium text-black hover:text-gray-700 dark:text-white dark:hover:text-gray-300 transition-colors">
                  Forgot your password?
                </Link>
              </div>
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={loading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? (otpMode ? 'Sending OTP...' : 'Signing in...') : (otpMode ? 'Send OTP' : 'Sign in')}
            </button>
          </div>

          <GoogleSignIn mode="signin" disabled={loading} />
        </form>
      </div>
    </div>
  );
}
