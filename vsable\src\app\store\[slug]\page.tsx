'use client';

import { useState, useEffect, useMemo } from 'react';
import Link from 'next/link';
import axios from 'axios';
import { useParams } from 'next/navigation';
import { Store, Product } from '@/types';

// Import Template Components
import TemplateDefault from '@/components/store-templates/TemplateDefault';
import TemplateOne from '@/components/store-templates/TemplateOne';
import TemplateTwo from '@/components/store-templates/TemplateTwo';
import TemplateThree from '@/components/store-templates/TemplateThree';
import TemplateFour from '@/components/store-templates/TemplateFour';
import TemplateFive from '@/components/store-templates/TemplateFive';
// API base URL
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

export default function StorePage() {
  const params = useParams();
  // Safely extract slug from params, handling possible array or null
  const slug = params && typeof params.slug === 'string'
    ? params.slug
    : Array.isArray(params?.slug)
      ? params.slug[0]
      : undefined;

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [store, setStore] = useState<Store | null>(null);
  const [rawProducts, setRawProducts] = useState<Product[]>([]); // All products from API
  const [categories, setCategories] = useState<string[]>(['All']);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStoreData = async () => {
      if (!slug) {
        setError('Store slug is missing.');
        setLoading(false);
        return;
      }
      setLoading(true);
      setError(null);
      try {
        // This endpoint should return { store: StoreData, products: ProductData[] }
        // And products should ideally have `imageUrl` (absolute) and `image_url` (original)
        const response = await axios.get<{ store: Store; products: Product[] }>(`${API_URL}/store/${slug}`);

        setStore(response.data.store);

        // Ensure products have both imageUrl (for new templates) and image_url (for default)
        // This assumes the backend might send one or the other, or you normalize here if backend doesn't.
        // For now, we assume backend provides `image_url` and `imageUrl` is derived if needed or also provided.
        // The /vendor/menu endpoint in backend already creates `imageUrl`.
        // If /store/:slug doesn't, new templates might need to use `image_url`.
        // Let's assume for now that `imageUrl` will be available or derived.
        const processedProducts = response.data.products.map(p => ({
            ...p,
            imageUrl: p.imageUrl || p.image_url // Fallback for new templates
        }));
        setRawProducts(processedProducts);

        const uniqueCategories = ['All'];
        processedProducts.forEach((product: Product) => {
          if (product.category && !uniqueCategories.includes(product.category)) {
            uniqueCategories.push(product.category);
          }
        });
        setCategories(uniqueCategories);
      } catch (error) {
        console.error("Error fetching store:", error);
        setError(error instanceof Error ? error.message : 'Store not found or an error occurred.');
      } finally {
        setLoading(false);
      }
    };

    fetchStoreData();
  }, [slug]);

  // Memoized filtered products to pass to templates
  const filteredProducts = useMemo(() => {
    return rawProducts.filter((product) => {
      const matchesSearch =
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (product.description && product.description.toLowerCase().includes(searchQuery.toLowerCase()));
      const matchesCategory = selectedCategory === 'All' || product.category === selectedCategory;
      return matchesSearch && matchesCategory;
    });
  }, [rawProducts, searchQuery, selectedCategory]);
  const handleContactClick = (contactLink: string | null) => {
    if (contactLink) {
      window.open(contactLink, '_blank', 'noopener,noreferrer');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error || !store) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center p-4">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Store Not Available</h1>
          <p className="text-gray-600 dark:text-gray-400">{error || "The store you're looking for doesn't exist or has been removed."}</p>
          <Link href="/" className="mt-4 inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
            Go to Homepage
          </Link>
        </div>
      </div>
    );
  }

  // Props for all templates
  const commonTemplateProps = {
    store,
    products: filteredProducts, // Pass filtered products
    categories,
    searchQuery,
    selectedCategory,
    handleContactClick,
  };

  const defaultTemplateProps = {
    ...commonTemplateProps,
    onSearchQueryChange: setSearchQuery,
    onSelectedCategoryChange: setSelectedCategory,
  };

  // Pass the same callbacks to all templates for consistent state management
  const newTemplateProps = {
      ...commonTemplateProps,
      // Pass setters to all templates so they can update the parent state
      onSearchQueryChange: setSearchQuery,
      onSelectedCategoryChange: setSelectedCategory,
  };


  // Dynamic Template Rendering Logic
  // The backend `Store` model defaults `selected_template_id` to 'template1'.
  // We'll use 'default' as a special case if `selected_template_id` is null/undefined or explicitly 'default'.
  const templateIdToRender = store.selected_template_id || 'default';

  switch (templateIdToRender) {
    case 'default': // Explicitly use the original layout
      return <TemplateDefault {...defaultTemplateProps} />;
    case 'template1':
      return <TemplateOne {...newTemplateProps} />;
    case 'template2':
      return <TemplateTwo {...newTemplateProps} />;
    case 'template3':
      return <TemplateThree {...newTemplateProps} />;
    case 'template4':
      return <TemplateFour {...newTemplateProps} />;
    case 'template5':
      return <TemplateFive {...newTemplateProps} />;
    default:
      console.warn(`Unknown template ID: ${store.selected_template_id}. Defaulting to TemplateDefault.`);
      return <TemplateDefault {...defaultTemplateProps} />;
  }
}
