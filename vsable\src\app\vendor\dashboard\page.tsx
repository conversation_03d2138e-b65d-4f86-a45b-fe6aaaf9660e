'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import axios from 'axios';
import {
  FiMenu,
  FiUsers,
  FiEdit,
  FiPlus,
  FiExternalLink,
  FiImage,
  FiShoppingBag,
} from 'react-icons/fi';

import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/ProtectedRoute';

/* ------------------------------------------------------------------ */
/*  Types                                                             */
/* ------------------------------------------------------------------ */
interface MenuItem {
  id: string | number;
  name: string;
  price: number;
  description?: string;
  category?: string;
  /** Publicly reachable URL for a square thumbnail (≈ 150 px). */
  imageUrl?: string;          // <- NEW (rename if your API returns a different key)
}

interface Stats {
  total_views: number;
}

/* ------------------------------------------------------------------ */
/*  Config                                                            */
/* ------------------------------------------------------------------ */
const API_URL = process.env.NEXT_PUBLIC_API_URL ?? 'http://localhost:5000/api';

/* ------------------------------------------------------------------ */
/*  Component                                                         */
/* ------------------------------------------------------------------ */
export default function VendorDashboard() {
  const { user } = useAuth();

  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [stats, setStats] = useState<Stats>({
    total_views: 0,
  });
  const [storeSlug, setStoreSlug] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  /* ------------------------------------------------------------- */
  /*  Data loader                                                  */
  /* ------------------------------------------------------------- */
  useEffect(() => {
    let mounted = true;

    async function loadDashboard() {
      try {
        /* ----- menu items ----- */
        const { data: menuData } = await axios.get<{ menu_items: MenuItem[] }>(
          `${API_URL}/vendor/menu`,
          { withCredentials: true }
        );

        /* ----- store info ----- */
        let slug = '';
        try {
          const { data: storeData } = await axios.get<{ store: { slug: string } }>(
            `${API_URL}/store/my-store`,
            { withCredentials: true }
          );
          slug = storeData?.store?.slug ?? '';
        } catch {
          /* store may not exist yet */
        }

        /* ----- stats ----- */
        let total_views = 0;
        try {
          const { data: statsData } = await axios.get<Stats>(
            `${API_URL}/vendor/stats`,
            { withCredentials: true }
          );
          total_views = statsData?.total_views || 0;
        } catch {
          /* vendor might not have any stats yet */
        }

        if (mounted) {
          setMenuItems(menuData.menu_items ?? []);
          setStoreSlug(slug);
          setStats({ total_views });
          setLoading(false);
        }
      } catch (err) {
        if (mounted) {
          const msg =
            axios.isAxiosError(err) && err.response
              ? `API error: ${err.response.status} ${err.response.statusText}`
              : 'Failed to load dashboard data.';
          setError(msg);
          setLoading(false);
        }
      }
    }

    loadDashboard();
    return () => {
      mounted = false;
    };
  }, []);

  /* ------------------------------------------------------------- */
  /*  Render helpers                                               */
  /* ------------------------------------------------------------- */
  const ExternalStoreLink = () => (
    <div
      className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow cursor-pointer"
      onClick={() => window.open(`/store/${storeSlug}`, '_blank')}
    >
      <div className="px-4 py-5 sm:p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0 bg-purple-500 rounded-md p-3">
            <FiExternalLink className="h-6 w-6 text-white" />
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                View Your Store
              </dt>
              <dd className="mt-1">
                <div className="text-lg font-medium text-gray-900 dark:text-white">
                  See customer view
                </div>
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  );

  /* ------------------------------------------------------------- */
  /*  JSX                                                          */
  /* ------------------------------------------------------------- */
  return (
    <ProtectedRoute allowedRoles={['vendor', 'admin']}>
      <div className="bg-gray-100 dark:bg-gray-900 min-h-screen">
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            {/* header */}
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
              Vendor Dashboard
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Welcome back, {user?.name}
            </p>

            {/* KPIs */}
            <div className="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-2">
              {[
                { icon: FiUsers, label: 'Total Views', value: stats.total_views },
                { icon: FiMenu, label: 'Menu Items', value: menuItems.length },
              ].map(({ icon: Icon, label, value }) => (
                <div
                  key={label}
                  className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"
                >
                  <div className="p-5">
                    <div className="flex items-center">
                      <Icon className="h-6 w-6 text-gray-400 flex-shrink-0" />
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                            {label}
                          </dt>
                          <dd>
                            <div className="text-lg font-medium text-gray-900 dark:text-white">
                              {value}
                            </div>
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* quick actions */}
            <div className="mt-8">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                Quick Actions
              </h2>
              <div className="mt-4 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
                <Link href="/vendor/store" className="block">
                  <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
                    <div className="px-4 py-5 sm:p-6">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 bg-blue-500 rounded-md p-3">
                          <FiEdit className="h-6 w-6 text-white" />
                        </div>
                        <div className="ml-5 w-0 flex-1">
                          <dl>
                            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                              Manage Store Profile
                            </dt>
                            <dd className="mt-1">
                              <div className="text-lg font-medium text-gray-900 dark:text-white">
                                Edit store details
                              </div>
                            </dd>
                          </dl>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>

                <Link href="/vendor/products" className="block">
                  <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow">
                    <div className="px-4 py-5 sm:p-6">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 bg-green-500 rounded-md p-3">
                          <FiShoppingBag className="h-6 w-6 text-white" />
                        </div>
                        <div className="ml-5 w-0 flex-1">
                          <dl>
                            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                              Manage Products
                            </dt>
                            <dd className="mt-1">
                              <div className="text-lg font-medium text-gray-900 dark:text-white">
                                Add or edit products
                              </div>
                            </dd>
                          </dl>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>

                {/* show link only if vendor already has a store */}
                {storeSlug && <ExternalStoreLink />}
              </div>
            </div>

            {/* menu list */}
            <div className="mt-8">
              <div className="flex justify-between items-center">
                <h2 className="text-lg font-medium text-gray-900 dark:text-white">
                  Your Menu Items
                </h2>
                <Link
                  href="/vendor/products"
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <FiPlus className="-ml-1 mr-2 h-4 w-4" />
                  Add Product
                </Link>
              </div>

              {loading ? (
                <div className="mt-4 flex justify-center">
                  <span className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500" />
                </div>
              ) : error ? (
                <div className="mt-4 bg-red-50 dark:bg-red-900/20 p-4 rounded-md">
                  <p className="text-sm text-red-700 dark:text-red-400">{error}</p>
                </div>
              ) : (
                <div className="mt-4 bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-md">
                  <ul className="divide-y divide-gray-200 dark:divide-gray-700">
                    {menuItems.length > 0 ? (
                      menuItems.map((item) => (
                        <li key={item.id} className="px-4 py-4 sm:px-6">
                          <div className="flex items-center">
                            {/* thumbnail */}
                            <div className="flex-shrink-0 w-16 h-16 rounded-md overflow-hidden bg-gray-200 dark:bg-gray-700">
                              {item.imageUrl ? (
                                // eslint-disable-next-line @next/next/no-img-element
                                <img
                                  src={item.imageUrl}
                                  alt={item.name}
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="flex items-center justify-center w-full h-full">
                                  <FiImage className="h-6 w-6 text-gray-400" />
                                </div>
                              )}
                            </div>

                            {/* text content */}
                            <div className="ml-4 min-w-0 flex-1">
                              <div className="flex items-center justify-between">
                                <p className="text-sm font-medium text-blue-600 dark:text-blue-400 truncate">
                                  {item.name}
                                </p>
                                <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                  ${item.price}
                                </span>
                              </div>
                              <div className="mt-1 sm:flex sm:justify-between">
                                {item.description && (
                                  <p className="text-sm text-gray-500 dark:text-gray-400">
                                    {item.description}
                                  </p>
                                )}
                                {item.category && (
                                  <p className="mt-1 sm:mt-0 text-sm text-gray-500 dark:text-gray-400">
                                    Category:&nbsp;{item.category}
                                  </p>
                                )}
                              </div>
                            </div>
                          </div>
                        </li>
                      ))
                    ) : (
                      <li className="px-4 py-4 sm:px-6">
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          No menu items yet. Click “Add Product” to create your first item.
                        </p>
                      </li>
                    )}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}
