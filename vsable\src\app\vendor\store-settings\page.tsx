'use client';

import { useState, useEffect } from 'react';
import axios from 'axios';
import StoreTemplateSelector from '@/components/vendor/StoreTemplateSelector';
import { Store, ApiResponse } from '@/types';
import ProtectedRoute from '@/components/ProtectedRoute'; // Assuming you have this
import Link from 'next/link'; // For linking back or to other sections

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

export default function VendorStoreSettingsPage() {
  const [store, setStore] = useState<Store | null>(null);
  // const [currentUser, setCurrentUser] = useState<User | null>(null); // If needed for other parts
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchVendorData = async () => {
      setLoading(true);
      setError(null);
      try {
        // This endpoint should ideally return the current user's store information
        // including 'selected_template_id'.
        // The /api/vendor/dashboard endpoint was modified earlier to include store data.
        const response = await axios.get<ApiResponse<{ user: Record<string, unknown>, store: Store | null }>>(
          `${API_URL}/vendor/dashboard`,
          { withCredentials: true } // Important for cookie-based auth
        );

        // if (response.data.user) setCurrentUser(response.data.user);

        if (response.data.store) {
          setStore(response.data.store);
        } else {
          // If a vendor has an account but no store yet.
          setError("No store found. Please create a store first to manage its settings.");
        }
      } catch (error) {
        console.error("Error fetching vendor data:", error);
        if (axios.isAxiosError(error) && error.response?.status === 401) {
            setError("Authentication failed. Please log in again.");
            // Optionally redirect to login page
        } else {
            setError(axios.isAxiosError(error) ? error.response?.data?.message || "Failed to load store settings." : "Failed to load store settings.");
        }
      } finally {
        setLoading(false);
      }
    };
    fetchVendorData();
  }, []);

  const handleStoreTemplateUpdate = (updatedStore: Store) => {
    setStore(updatedStore); // Update local state with the new store data from selector
  };

  if (loading) {
    return (
      <ProtectedRoute allowedRoles={['vendor']}>
        <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute allowedRoles={['vendor']}>
      <div className="min-h-screen bg-gray-100 dark:bg-gray-900 py-8">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-6">
            <Link href="/vendor/dashboard" className="text-blue-600 dark:text-blue-400 hover:underline">
              &larr; Back to Dashboard
            </Link>
          </div>

          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Store Appearance Settings
          </h1>
          <p className="text-md text-gray-600 dark:text-gray-400 mb-8">
            Customize the look and feel of your public storefront.
          </p>

          {error && !store && ( // Show error prominently if store couldn't be loaded
            <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-md mb-6">
              <p className="text-sm text-red-700 dark:text-red-400">{error}</p>
              {error.includes("create a store first") && (
                <Link href="/vendor/store/create" className="mt-2 inline-block text-sm text-blue-600 dark:text-blue-400 hover:underline">
                    Create your store now &rarr;
                </Link>
              )}
            </div>
          )}

          {store ? (
            <StoreTemplateSelector
              currentStore={store}
              onTemplateUpdate={handleStoreTemplateUpdate}
            />
          ) : (
            !loading && !error && ( // Case where loading is done, no error, but no store (should be caught by error above)
                <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-md">
                    <p className="text-sm text-yellow-700 dark:text-yellow-400">Store data is not available. If you&apos;ve just created your store, please try refreshing.</p>
                </div>
            )
          )}

          {/* You can add other appearance settings here in the future */}
          {/* For example:
            <div className="mt-10 bg-white dark:bg-gray-800 shadow sm:rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Brand Colors</h3>
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                (Feature coming soon)
              </p>
            </div> */}
         
        </div>
      </div>
    </ProtectedRoute>
  );
}