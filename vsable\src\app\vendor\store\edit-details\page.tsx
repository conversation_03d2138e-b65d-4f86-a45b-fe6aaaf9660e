        'use client'; // If it's an interactive page

import { useState, useEffect } from 'react';
import axios from 'axios';
import ProtectedRoute from '@/components/ProtectedRoute';
import StoreTemplateSelector from '@/components/vendor/StoreTemplateSelector';
import { Store, ApiResponse } from '@/types';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

        export default function EditStoreDetailsPage() {
  const [store, setStore] = useState<Store | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchVendorStore = async () => {
      setLoading(true);
      try {
        const response = await axios.get<ApiResponse<{ store: Store }>>(
          `${API_URL}/vendor/store/details`, // You'll need to create this backend endpoint
          { withCredentials: true }
          );
        if (response.data.store) {
          setStore(response.data.store);
        } else {
          setError("No store found for this vendor.");
        }
      } catch (error) {
        setError(error instanceof Error ? error.message : "Failed to load store details.");
      } finally {
        setLoading(false);
      }
    };
    fetchVendorStore();
  }, []);

  const handleStoreUpdate = (updatedStore: Store) => {
    setStore(updatedStore); // This updates the local state after template change
  };

  if (loading) return <div className="p-4">Loading store details...</div>;
  if (error) return <div className="p-4 text-red-500">Error: {error}</div>;
  if (!store) return <div className="p-4">No store data available. You might need to create a store first.</div>;

  return (
    <ProtectedRoute allowedRoles={['vendor']}>
      <div className="container mx-auto p-4">
        <h1 className="text-2xl font-bold mb-6">Edit Store Details & Appearance</h1>

        {/* Placeholder for other store detail editing form (name, description, logo etc.) */}
        <div className="mb-8 p-6 bg-white dark:bg-gray-800 shadow rounded-lg">
          <h2 className="text-xl font-semibold mb-3">Store Information</h2>
          <p className="text-gray-600 dark:text-gray-400">Form for store name, description, logo will go here.</p>
        </div>

        <StoreTemplateSelector currentStore={store} onTemplateUpdate={handleStoreUpdate} />
      </div>
    </ProtectedRoute>
  );
}
