'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import axios from 'axios';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/ProtectedRoute';
import StoreTemplateSelector from '@/components/vendor/StoreTemplateSelector';
import FileUpload from '@/components/FileUpload';
import { FiSave, FiLink, FiPhone, FiBriefcase } from 'react-icons/fi';
import { FaTelegram, FaWhatsapp, FaInstagram, FaTiktok, FaFacebook } from 'react-icons/fa';

// API base URL
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

interface StoreData {
  id: number;
  name: string;
  slug: string;
  description: string | null;
  logo_url: string | null;
  cover_image_url: string | null;
  telegram: string | null;
  whatsapp: string | null;
  phone: string | null;
  instagram: string | null;
  facebook: string | null;
  tiktok: string | null;
  user_id: number;
  created_at: string;
  updated_at: string;
  selected_template_id?: string | null;
}

export default function StoreProfilePage() {
  const { user } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [store, setStore] = useState<StoreData | null>(null);
  const [hasStore, setHasStore] = useState<boolean | null>(null);

  // Form state
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [logoUrl, setLogoUrl] = useState('');
  const [coverImageUrl, setCoverImageUrl] = useState('');
  const [telegram, setTelegram] = useState('');
  const [whatsapp, setWhatsapp] = useState('');
  const [phone, setPhone] = useState('');
  const [instagram, setInstagram] = useState('');
  const [facebook, setFacebook] = useState('');
  const [tiktok, setTiktok] = useState('');

  useEffect(() => {
    const fetchStore = async () => {
      try {
        const response = await axios.get(`${API_URL}/store/my-store`, {
          withCredentials: true
        });
        setStore(response.data.store);
        setHasStore(true);

        // Set form values
        setName(response.data.store.name || '');
        setDescription(response.data.store.description || '');
        setLogoUrl(response.data.store.logo_url || '');
        setCoverImageUrl(response.data.store.cover_image_url || '');
        setTelegram(response.data.store.telegram || '');
        setWhatsapp(response.data.store.whatsapp || '');
        setPhone(response.data.store.phone || '');
        setInstagram(response.data.store.instagram || '');
        setFacebook(response.data.store.facebook || '');
        setTiktok(response.data.store.tiktok || '');

        setLoading(false);
      } catch (error) {
        if (axios.isAxiosError(error) && error.response?.status === 404) {
          // No store found, but not an error
          setHasStore(false);
          setLoading(false);
        } else {
          setError('Failed to load store data');
          setLoading(false);
        }
      }
    };

    if (user) {
      fetchStore();
    }
  }, [user]);

  const handleCreateStore = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await axios.post(`${API_URL}/store/create`, {
        name,
        description: description || null,
        logo_url: logoUrl || null,
        cover_image_url: coverImageUrl || null,
        telegram: telegram || null,
        whatsapp: whatsapp || null,
        phone: phone || null,
        instagram: instagram || null,
        facebook: facebook || null,
        tiktok: tiktok || null
      }, {
        withCredentials: true
      });

      setStore(response.data.store);
      setHasStore(true);
      setSuccess('Store created successfully!');

      // Redirect to store page after creation
      setTimeout(() => {
        router.replace('/vendor/store');
      }, 1500);
    } catch (error) {
      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'Failed to create store' : 'Failed to create store');
    } finally {
      setSaving(false);
    }
  };

  const handleUpdateStore = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await axios.put(`${API_URL}/store/update`, {
        name,
        description: description || null,
        logo_url: logoUrl || null,
        cover_image_url: coverImageUrl || null,
        telegram: telegram || null,
        whatsapp: whatsapp || null,
        phone: phone || null,
        instagram: instagram || null,
        facebook: facebook || null,
        tiktok: tiktok || null
      }, {
        withCredentials: true
      });

      setStore(response.data.store);
      setSuccess('Store updated successfully!');
    } catch (error) {
      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'Failed to update store' : 'Failed to update store');
    } finally {
      setSaving(false);
    }
  };

  const handleStoreTemplateUpdate = (updatedStore: StoreData) => {
    setStore(updatedStore);
    setSuccess('Store template updated successfully!');
  };

  const handlePreviewStore = () => {
    if (store && store.slug) {
      window.open(`/store/${store.slug}`, '_blank');
    }
  };

  return (
    <ProtectedRoute allowedRoles={['vendor', 'admin']}>
      <div className="bg-gray-100 dark:bg-gray-900 min-h-screen">
        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">
              {hasStore ? 'Store Profile' : 'Create Your Store'}
            </h1>

            {loading ? (
              <div className="mt-6 flex justify-center">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : (
              <div className="mt-6">
                {error && (
                  <div className="mb-4 bg-red-50 dark:bg-red-900/20 p-4 rounded-md">
                    <p className="text-sm text-red-700 dark:text-red-400">{error}</p>
                  </div>
                )}

                {success && (
                  <div className="mb-4 bg-green-50 dark:bg-green-900/20 p-4 rounded-md">
                    <p className="text-sm text-green-700 dark:text-green-400">{success}</p>
                  </div>
                )}

                <form onSubmit={hasStore ? handleUpdateStore : handleCreateStore} className="space-y-6">
                  <div className="bg-white dark:bg-gray-800 shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    <div className="md:grid md:grid-cols-3 md:gap-6">
                      <div className="md:col-span-1">
                        <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-white">Store Information</h3>
                        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                          This information will be displayed publicly on your store page.
                        </p>
                      </div>
                      <div className="mt-5 md:mt-0 md:col-span-2">
                        <div className="grid grid-cols-6 gap-6">
                          <div className="col-span-6 sm:col-span-4">
                            <label htmlFor="store-name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                              Store Name *
                            </label>
                            <div className="mt-1 flex rounded-md shadow-sm">
                              <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm">
                                <FiBriefcase className="h-4 w-4" />
                              </span>
                              <input
                                type="text"
                                name="store-name"
                                id="store-name"
                                required
                                className="focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                                placeholder="Your Store Name"
                                value={name}
                                onChange={(e) => setName(e.target.value)}
                              />
                            </div>
                          </div>
                          <div className="col-span-6">
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                              Store Logo
                            </label>
                            <div className="mt-1">
                              {store ? (
                                <FileUpload
                                  onFileUploaded={(fileUrl) => {
                                    console.log('Logo uploaded:', fileUrl);
                                    // Make sure we're setting the logo URL, not the cover image URL
                                    setLogoUrl(fileUrl);
                                    // Update the store object with the new logo URL
                                    if (store) {
                                      const updatedStore = {
                                        ...store,
                                        logo_url: fileUrl
                                      };
                                      console.log('Updating store with new logo:', updatedStore);
                                      setStore(updatedStore);
                                    }
                                  }}
                                  endpoint="/store/upload-logo"
                                  acceptedFileTypes="image/*,video/*"
                                  maxSizeMB={5}
                                  currentFileUrl={logoUrl}
                                  label="Store Logo"
                                />
                              ) : (
                                <div className="flex flex-col space-y-2">
                                  <FileUpload
                                    onFileUploaded={(fileUrl) => setLogoUrl(fileUrl)}
                                    endpoint="/upload/image"
                                    acceptedFileTypes="image/*,video/*"
                                    maxSizeMB={5}
                                    currentFileUrl={logoUrl}
                                    label="Store Logo"
                                  />
                                  <p className="text-xs text-gray-500 dark:text-gray-400">
                                    You can upload the logo now, or add it after creating the store.
                                  </p>
                                </div>
                              )}
                            </div>
                            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                              Upload a logo for your store. Recommended size: 200x200 pixels.
                            </p>
                          </div>


                          <div className="col-span-6">
                            <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                              Description
                            </label>
                            <div className="mt-1">
                              <textarea
                                id="description"
                                name="description"
                                rows={3}
                                className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                                placeholder="Describe your store and what you offer"
                                value={description}
                                onChange={(e) => setDescription(e.target.value)}
                              />
                            </div>
                          </div>


                          <div className="col-span-6">
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                              Cover Image
                            </label>
                            <div className="mt-1">
                              {store ? (
                                <FileUpload
                                  onFileUploaded={(fileUrl) => {
                                    console.log('Cover image uploaded:', fileUrl);
                                    // Make sure we're setting the cover image URL, not the logo URL
                                    setCoverImageUrl(fileUrl);
                                    // Update the store object with the new cover image URL
                                    if (store) {
                                      const updatedStore = {
                                        ...store,
                                        cover_image_url: fileUrl
                                      };
                                      console.log('Updating store with new cover image:', updatedStore);
                                      setStore(updatedStore);
                                    }
                                  }}
                                  endpoint="/store/upload-cover"
                                  acceptedFileTypes="image/*,video/*"
                                  maxSizeMB={10}
                                  currentFileUrl={coverImageUrl}
                                  label="Cover Image"
                                />
                              ) : (
                                <div className="flex flex-col space-y-2">
                                  <FileUpload
                                    onFileUploaded={(fileUrl) => setCoverImageUrl(fileUrl)}
                                    endpoint="/upload/image"
                                    acceptedFileTypes="image/*,video/*"
                                    maxSizeMB={10}
                                    currentFileUrl={coverImageUrl}
                                    label="Cover Image"
                                  />
                                  <p className="text-xs text-gray-500 dark:text-gray-400">
                                    You can upload the cover image now, or add it after creating the store.
                                  </p>
                                </div>
                              )}
                            </div>
                            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                              Upload a cover image for your store&apos;s landing page. Recommended size: 1920x1080 pixels.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white dark:bg-gray-800 shadow px-4 py-5 sm:rounded-lg sm:p-6">
                    <div className="md:grid md:grid-cols-3 md:gap-6">
                      <div className="md:col-span-1">
                        <h3 className="text-lg font-medium leading-6 text-gray-900 dark:text-white">Contact Information</h3>
                        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                          How customers can reach you to place orders.
                        </p>
                      </div>
                      <div className="mt-5 md:mt-0 md:col-span-2">
                        <div className="grid grid-cols-6 gap-6">
                          <div className="col-span-6 sm:col-span-4">
                            <label htmlFor="telegram" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                              Telegram Username or Link
                            </label>
                            <div className="mt-1 flex rounded-md shadow-sm">
                              <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm">
                                <FaTelegram className="h-4 w-4 text-blue-500" />
                              </span>
                              <input
                                type="text"
                                name="telegram"
                                id="telegram"
                                className="focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                                placeholder="@username or https://t.me/username"
                                value={telegram}
                                onChange={(e) => setTelegram(e.target.value)}
                              />
                            </div>
                          </div>

                          <div className="col-span-6 sm:col-span-4">
                            <label htmlFor="whatsapp" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                              WhatsApp Number (Optional)
                            </label>
                            <div className="mt-1 flex rounded-md shadow-sm">
                              <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm">
                                <FaWhatsapp className="h-4 w-4 text-green-500" />
                              </span>
                              <input
                                type="text"
                                name="whatsapp"
                                id="whatsapp"
                                className="focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                                placeholder="+1234567890"
                                value={whatsapp}
                                onChange={(e) => setWhatsapp(e.target.value)}
                              />
                            </div>
                          </div>

                          <div className="col-span-6 sm:col-span-4">
                            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                              Phone Number (Optional)
                            </label>
                            <div className="mt-1 flex rounded-md shadow-sm">
                              <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm">
                                <FiPhone className="h-4 w-4" />
                              </span>
                              <input
                                type="tel"
                                name="phone"
                                id="phone"
                                className="focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                                placeholder="+1234567890"
                                value={phone}
                                onChange={(e) => setPhone(e.target.value)}
                              />
                            </div>
                          </div>

                          <div className="col-span-6 sm:col-span-4">
                            <label htmlFor="instagram" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                              Instagram Username (Optional)
                            </label>
                            <div className="mt-1 flex rounded-md shadow-sm">
                              <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm">
                                <FaInstagram className="h-4 w-4 text-pink-500" />
                              </span>
                              <input
                                type="text"
                                name="instagram"
                                id="instagram"
                                className="focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                                placeholder="username"
                                value={instagram}
                                onChange={(e) => setInstagram(e.target.value)}
                              />
                            </div>
                          </div>

                          <div className="col-span-6 sm:col-span-4">
                            <label htmlFor="facebook" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                              Facebook Page or Profile (Optional)
                            </label>
                            <div className="mt-1 flex rounded-md shadow-sm">
                              <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm">
                                <FaFacebook className="h-4 w-4 text-blue-600" />
                              </span>
                              <input
                                type="text"
                                name="facebook"
                                id="facebook"
                                className="focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                                placeholder="https://facebook.com/yourpage or username"
                                value={facebook}
                                onChange={(e) => setFacebook(e.target.value)}
                              />
                            </div>
                          </div>

                          <div className="col-span-6 sm:col-span-4">
                            <label htmlFor="tiktok" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                              TikTok Username (Optional)
                            </label>
                            <div className="mt-1 flex rounded-md shadow-sm">
                              <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 text-sm">
                                <FaTiktok className="h-4 w-4 text-black dark:text-white" />
                              </span>
                              <input
                                type="text"
                                name="tiktok"
                                id="tiktok"
                                className="focus:ring-blue-500 focus:border-blue-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                                placeholder="@username"
                                value={tiktok}
                                onChange={(e) => setTiktok(e.target.value)}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-between">
                    <button
                      type="submit"
                      disabled={saving}
                      className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                    >
                      <FiSave className="mr-2 -ml-1 h-5 w-5" />
                      {saving ? 'Saving...' : hasStore ? 'Update Store' : 'Create Store'}
                    </button>

                    {hasStore && store && (
                      <button
                        type="button"
                        onClick={handlePreviewStore}
                        className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:bg-gray-600"
                      >
                        <FiLink className="mr-2 -ml-1 h-5 w-5" />
                        Preview Store
                      </button>
                    )}
                  </div>
                </form>

                {/* Store Template Selector */}
                {hasStore && store && (
                  <div className="mt-8">
                    <StoreTemplateSelector
                      currentStore={store}
                      onTemplateUpdate={handleStoreTemplateUpdate}
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </ProtectedRoute>
  );
}