'use client';

import React, { useState, useEffect, useCallback, Suspense } from 'react';
import Link from 'next/link';
import { useSearchParams, useRouter } from 'next/navigation';
import axios from 'axios';
import { FiMail, FiCheck, FiX, FiRefreshCw } from 'react-icons/fi';
import LoadingSpinner from '@/components/LoadingSpinner';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

function EmailVerificationForm() {
  const [loading, setLoading] = useState(true);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');
  const [resendLoading, setResendLoading] = useState(false);
  const [resendSuccess, setResendSuccess] = useState(false);
  // const [token, setToken] = useState('');
  const [email, setEmail] = useState('');

  const searchParams = useSearchParams();
  const router = useRouter();

  const verifyEmailToken = useCallback(async (verificationToken: string) => {
    try {
      await axios.post(`${API_URL}/auth/verify-email`, {
        token: verificationToken
      });

      setSuccess(true);
      setTimeout(() => {
        router.push('/login?verified=true');
      }, 3000);
    } catch (error: unknown) {
      setError(
        axios.isAxiosError(error)
          ? error.response?.data?.message || 'Invalid or expired verification link'
          : 'Verification failed'
      );
    } finally {
      setLoading(false);
    }
  }, [router]);

  useEffect(() => {
    const tokenParam = searchParams.get('token');
    const emailParam = searchParams.get('email');

    if (tokenParam) {
      verifyEmailToken(tokenParam);
    } else if (emailParam) {
      setEmail(emailParam);
      setLoading(false);
    } else {
      setError('Invalid verification link. Please check your email for the correct link.');
      setLoading(false);
    }
  }, [searchParams, verifyEmailToken]);

  const handleResendVerification = async () => {
    if (!email) {
      setError('Email address is required to resend verification.');
      return;
    }

    setResendLoading(true);
    setError('');
    setResendSuccess(false);

    try {
      await axios.post(`${API_URL}/auth/send-verification-email`, {
        email: email
      });

      setResendSuccess(true);
      setTimeout(() => setResendSuccess(false), 5000);
    } catch (error: unknown) {
      setError(
        axios.isAxiosError(error)
          ? error.response?.data?.message || 'Failed to resend verification email'
          : 'Failed to resend verification email'
      );
    } finally {
      setResendLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <LoadingSpinner size="lg" text="Verifying your email..." />
      </div>
    );
  }

  if (success) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <div className="mx-auto h-16 w-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mb-6">
              <FiCheck className="h-8 w-8 text-green-600 dark:text-green-400" />
            </div>
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
              Email Verified!
            </h2>
            <p className="mt-4 text-gray-600 dark:text-gray-400">
              Your email has been successfully verified. You can now access all features of your account.
            </p>
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-500">
              Redirecting to login page...
            </p>
          </div>

          <div className="text-center">
            <Link
              href="/login"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black transition-colors"
            >
              Continue to Login
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className={`mx-auto h-16 w-16 rounded-full flex items-center justify-center mb-6 ${
            error ? 'bg-red-100 dark:bg-red-900' : 'bg-black dark:bg-white'
          }`}>
            {error ? (
              <FiX className="h-8 w-8 text-red-600 dark:text-red-400" />
            ) : (
              <FiMail className="h-8 w-8 text-white dark:text-black" />
            )}
          </div>

          <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
            {error ? 'Verification Failed' : 'Verify Your Email'}
          </h2>

          {error ? (
            <div className="mt-4">
              <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>

              {email && (
                <div className="space-y-4">
                  {resendSuccess && (
                    <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-4">
                      <p className="text-sm text-green-600 dark:text-green-400">
                        Verification email sent successfully! Please check your inbox.
                      </p>
                    </div>
                  )}

                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    Need a new verification link?
                  </p>

                  <button
                    onClick={handleResendVerification}
                    disabled={resendLoading}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {resendLoading ? (
                      <>
                        <LoadingSpinner size="sm" color="black" />
                        <span className="ml-2">Sending...</span>
                      </>
                    ) : (
                      <>
                        <FiRefreshCw className="mr-2 h-4 w-4" />
                        Resend Verification Email
                      </>
                    )}
                  </button>
                </div>
              )}
            </div>
          ) : (
            <p className="mt-4 text-gray-600 dark:text-gray-400">
              Please check your email for the verification link.
            </p>
          )}
        </div>

        <div className="text-center">
          <Link
            href="/login"
            className="text-sm text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white transition-colors"
          >
            Back to login
          </Link>
        </div>
      </div>
    </div>
  );
}

export default function VerifyEmail() {
  return (
    <Suspense fallback={<LoadingSpinner fullScreen text="Loading..." />}>
      <EmailVerificationForm />
    </Suspense>
  );
}
