'use client';

import { useState, useRef, useId, useEffect, ChangeEvent } from 'react';
import axios from 'axios';
import Image from 'next/image';
import { FiUpload, FiX, FiImage, FiVideo, FiFile, FiCheck } from 'react-icons/fi';
import { getImagePath } from '@/utils/imageUtils';
import SafeImage from '@/components/SafeImage';

/* ------------- CONFIG ------------- */
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

/* ------------- TYPES -------------- */
interface FileUploadProps {
  onFileUploaded: (fileUrl: string) => void;
  endpoint: string;
  acceptedFileTypes?: string;
  maxSizeMB?: number;
  currentFileUrl?: string | null;
  label?: string;
  className?: string;
}

/* ------------- COMPONENT ---------- */
const FileUpload: React.FC<FileUploadProps> = ({
  onFileUploaded,
  endpoint,
  acceptedFileTypes = 'image/*,video/*',
  maxSizeMB = 5,
  currentFileUrl = null,
  label = 'Upload File',
  className = '',
}) => {
  /* state */
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(currentFileUrl);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  /* refs & ids */
  const fileInputRef = useRef<HTMLInputElement>(null);
  const inputId = useId(); // unique for every <FileUpload>

  /* keep preview in sync if parent updates the url */
  useEffect(() => {
    setPreview(currentFileUrl || null);
  }, [currentFileUrl]);

  /* -------- handle file select ------- */
  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    setError(null);
    setSuccess(false);

    const selectedFile = e.target.files?.[0];
    if (!selectedFile) return;

    /* size guard */
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    if (selectedFile.size > maxSizeBytes) {
      setError(`File size exceeds ${maxSizeMB} MB`);
      return;
    }

    setFile(selectedFile);

    /* local preview */
    const reader = new FileReader();
    reader.onloadend = () => setPreview(reader.result as string);
    reader.readAsDataURL(selectedFile);
  };

  /* -------- handle upload ----------- */
  const handleUpload = async () => {
    if (!file) {
      setError('Please select a file first');
      return;
    }

    setUploading(true);
    setError(null);
    setSuccess(false);

    const formData = new FormData();
    formData.append('file', file);

    try {
      const { data } = await axios.post(`${API_URL}${endpoint}`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
        withCredentials: true,
      });

      setSuccess(true);

      /* normalise possible response shapes */
      const url =
        data?.store?.cover_image_url ??
        data?.store?.logo_url ??
        data?.file_url ??
        data?.file_path;

      if (url) {
        console.log('Original URL from server:', url);
        const formattedUrl = getImagePath(url);
        console.log('Formatted URL for Next.js:', formattedUrl);
        onFileUploaded(formattedUrl);
      } else {
        setError('Upload succeeded but no file URL returned');
      }
    } catch (error) {
      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'Error uploading file' : 'Error uploading file');
    } finally {
      setUploading(false);
    }
  };

  /* -------- clear file -------------- */
  const handleClearFile = () => {
    setFile(null);
    setPreview(currentFileUrl || null);
    setError(null);
    setSuccess(false);
    if (fileInputRef.current) fileInputRef.current.value = '';
  };

  /* -------- icon helper ------------- */
  const getFileTypeIcon = () => {
    if (!preview) return <FiFile className="w-12 h-12 text-gray-400" />;
    if (file?.type.startsWith('image/') || preview.startsWith('data:image/'))
      return <FiImage className="w-12 h-12 text-gray-400" />;
    if (file?.type.startsWith('video/') || preview.startsWith('data:video/'))
      return <FiVideo className="w-12 h-12 text-gray-400" />;
    return <FiFile className="w-12 h-12 text-gray-400" />;
  };

  return (
    <div className={`w-full ${className}`}>
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
        {label}
      </label>

      {/* preview */}
      <div className="w-full h-48 bg-gray-100 dark:bg-gray-800 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 flex items-center justify-center overflow-hidden relative">
        {preview ? (
          preview.startsWith('data:image/') ||
          preview.match(/\.(jpe?g|png|gif|webp|avif|svg)$/i) ? (
            <div className="relative w-full h-full">
              {preview.startsWith('data:') ? (
                <Image
                  src={preview}
                  alt="preview"
                  fill
                  style={{ objectFit: 'contain' }}
                  unoptimized={true}
                />
              ) : (
                <SafeImage
                  src={preview}
                  alt="preview"
                  fill
                  style={{ objectFit: 'contain' }}
                  debug={true}
                />
              )}
            </div>
          ) : preview.startsWith('data:video/') ||
            preview.match(/\.(mp4|mov|webm|ogg)$/i) ? (
            <video src={preview} controls className="max-h-full max-w-full" />
          ) : (
            getFileTypeIcon()
          )
        ) : (
          <div className="text-center p-4">
            {getFileTypeIcon()}
            <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
              Drag &amp; drop a file here, or click to select
            </p>
            <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
              {acceptedFileTypes.replace('*', '').replace(/,/g, ', ')} files up
              to {maxSizeMB} MB
            </p>
          </div>
        )}

        {/* clear btn */}
        {preview && (
          <button
            type="button"
            onClick={handleClearFile}
            className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            <FiX className="w-4 h-4" />
          </button>
        )}
      </div>

      {/* controls */}
      <div className="mt-4 flex items-center space-x-3 w-full">
        <input
          id={inputId}
          ref={fileInputRef}
          type="file"
          accept={acceptedFileTypes}
          className="hidden"
          onChange={handleFileChange}
        />
        <label
          htmlFor={inputId}
          className="flex-1 text-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer"
        >
          Select file
        </label>
        <button
          type="button"
          onClick={handleUpload}
          disabled={!file || uploading}
          className={`px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 flex items-center justify-center ${
            !file || uploading ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {uploading ? (
            <span className="flex items-center">
              <svg
                className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Uploading…
            </span>
          ) : success ? (
            <span className="flex items-center">
              <FiCheck className="-ml-1 mr-2 h-4 w-4" />
              Uploaded
            </span>
          ) : (
            <span className="flex items-center">
              <FiUpload className="-ml-1 mr-2 h-4 w-4" />
              Upload
            </span>
          )}
        </button>
      </div>

      {error && <div className="mt-2 text-sm text-red-600 dark:text-red-400">{error}</div>}
    </div>
  );
};

export default FileUpload;
