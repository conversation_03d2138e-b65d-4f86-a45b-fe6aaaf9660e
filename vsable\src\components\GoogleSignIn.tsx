'use client';

import React, { useState } from 'react';
import { FcGoogle } from 'react-icons/fc';
import axios from 'axios';
import { useAuth } from '@/contexts/AuthContext';
import { useFirebaseAuth } from '@/hooks/useFirebaseAuth';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

interface GoogleSignInProps {
  mode: 'signin' | 'signup';
  role?: 'customer' | 'vendor' | 'admin';
  onSuccess?: (credential: string) => void;
  onError?: (error: unknown) => void;
  disabled?: boolean;
}

const GoogleSignIn: React.FC<GoogleSignInProps> = ({
  mode,
  role = 'customer',
  onSuccess,
  onError,
  disabled = false
}) => {
  const [loading, setLoading] = useState(false);
  const { setUser } = useAuth();
  const { signInWithGoogle, getIdToken, error: firebaseError } = useFirebaseAuth();

  const handleGoogleSignIn = async () => {
    setLoading(true);

    try {
      // Sign in with Firebase
      const firebaseUser = await signInWithGoogle();

      if (!firebaseUser) {
        throw new Error(firebaseError || 'Failed to sign in with Google');
      }

      // Get Firebase ID token
      const idToken = await getIdToken();

      if (!idToken) {
        throw new Error('Failed to get authentication token');
      }

      // Send Firebase ID token to backend for verification
      const response = await axios.post(`${API_URL}/auth/google`, {
        idToken: idToken,
        firebaseUid: firebaseUser.uid,
        email: firebaseUser.email,
        name: firebaseUser.displayName,
        photoURL: firebaseUser.photoURL,
        role: role
      });

      // Update user state
      if (setUser) {
        setUser(response.data.user);
      }

      // Call success callback
      if (onSuccess) {
        onSuccess(idToken);
      }

      // Redirect based on user role
      if (response.data.user.role === 'admin') {
        window.location.href = '/admin/dashboard';
      } else if (response.data.user.role === 'vendor') {
        window.location.href = '/vendor/dashboard';
      } else {
        window.location.href = '/';
      }

    } catch (error: unknown) {
      console.error('Google OAuth error:', error);

      const errorMessage = error instanceof Error
        ? error.message
        : 'Google sign-in failed';

      if (onError) {
        onError(error);
      } else {
        alert(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="mt-6">
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-300 dark:border-gray-600" />
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-2 bg-gray-50 dark:bg-gray-900 text-gray-500 dark:text-gray-400">
            Or continue with
          </span>
        </div>
      </div>

      <div className="mt-6">
        <button
          type="button"
          onClick={handleGoogleSignIn}
          disabled={disabled || loading}
          className="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          <FcGoogle className="h-5 w-5 mr-3" />
          {loading
            ? 'Connecting...'
            : mode === 'signin'
              ? 'Sign in with Google'
              : 'Sign up with Google'
          }
        </button>
      </div>

      {/* Instructions for setting up Firebase Google OAuth */}
      <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-900/20 border border-gray-200 dark:border-gray-800 rounded-md">
        <p className="text-xs text-gray-600 dark:text-gray-400">
          <strong>Firebase Setup Required:</strong> Configure your Firebase project for Google OAuth:
        </p>
        <ul className="text-xs text-gray-600 dark:text-gray-400 mt-1 ml-4 list-disc">
          <li>Create Firebase project at console.firebase.google.com</li>
          <li>Enable Google authentication in Firebase Auth</li>
          <li>Update environment variables with Firebase config</li>
        </ul>
      </div>
    </div>
  );
};

export default GoogleSignIn;

/*
GOOGLE OAUTH SETUP INSTRUCTIONS:

1. Go to Google Cloud Console (https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Go to Credentials > Create Credentials > OAuth 2.0 Client IDs
5. Configure OAuth consent screen
6. Add authorized redirect URIs:
   - http://localhost:3000/auth/google/callback (development)
   - https://yourdomain.com/auth/google/callback (production)
7. Copy Client ID and Client Secret
8. Add to environment variables:
   GOOGLE_CLIENT_ID=your_client_id
   GOOGLE_CLIENT_SECRET=your_client_secret

BACKEND IMPLEMENTATION NEEDED:
- Install google-auth library: pip install google-auth google-auth-oauthlib
- Create /auth/google route for OAuth initiation
- Create /auth/google/callback route for OAuth callback
- Handle user creation/login with Google data

FRONTEND IMPLEMENTATION NEEDED:
- Install Google Identity Services
- Replace handleGoogleSignIn with actual OAuth flow
- Handle OAuth response and redirect
*/
