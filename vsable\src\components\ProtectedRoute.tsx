'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles?: string[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  allowedRoles = []
}) => {
  const { user, loading, isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      router.replace('/login');
    } else if (!loading && isAuthenticated && allowedRoles.length > 0) {
      if (user && !allowedRoles.includes(user.role)) {
        // Redirect based on role if not authorized
        if (user.role === 'admin') {
          router.replace('/admin/dashboard');
        } else if (user.role === 'vendor') {
          router.replace('/vendor/dashboard');
        } else {
          router.replace('/');
        }
      }
    }
  }, [loading, isAuthenticated, user, router, allowedRoles]);

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // If not authenticated, don't render children
  if (!isAuthenticated) {
    return null;
  }

  // If roles are specified and user doesn't have permission, don't render children
  if (allowedRoles.length > 0 && user && !allowedRoles.includes(user.role)) {
    return null;
  }

  // Otherwise, render children
  return <>{children}</>;
};

export default ProtectedRoute;
