'use client';

import React from 'react';
import { FiUser, FiShoppingBag, FiSettings } from 'react-icons/fi';

interface RoleSelectorProps {
  selectedRole: 'customer' | 'vendor' | 'admin';
  onRoleChange: (role: 'customer' | 'vendor' | 'admin') => void;
  disabled?: boolean;
}

const RoleSelector: React.FC<RoleSelectorProps> = ({
  selectedRole,
  onRoleChange,
  disabled = false
}) => {
  const roles = [
    {
      id: 'customer' as const,
      name: 'Customer',
      description: 'Browse and purchase products',
      icon: FiUser,
      color: 'blue'
    },
    {
      id: 'vendor' as const,
      name: 'Vendor',
      description: 'Sell products and manage store',
      icon: FiShoppingBag,
      color: 'green'
    },
    {
      id: 'admin' as const,
      name: 'Admin',
      description: 'Manage platform and users',
      icon: FiSettings,
      color: 'purple'
    }
  ];

  return (
    <div className="space-y-3">
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
        Select your role
      </label>
      <div className="grid grid-cols-1 gap-3">
        {roles.map((role) => {
          const Icon = role.icon;
          const isSelected = selectedRole === role.id;
          
          return (
            <button
              key={role.id}
              type="button"
              onClick={() => !disabled && onRoleChange(role.id)}
              disabled={disabled}
              className={`
                relative flex items-center p-4 border rounded-lg text-left transition-all
                ${isSelected
                  ? 'border-black dark:border-white bg-black/5 dark:bg-white/5'
                  : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                }
                ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
              `}
            >
              <div className={`
                flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-4
                ${isSelected
                  ? 'bg-black dark:bg-white text-white dark:text-black'
                  : 'bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400'
                }
              `}>
                <Icon className="w-5 h-5" />
              </div>
              
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h3 className={`
                    text-sm font-medium
                    ${isSelected
                      ? 'text-black dark:text-white'
                      : 'text-gray-900 dark:text-gray-100'
                    }
                  `}>
                    {role.name}
                  </h3>
                  {isSelected && (
                    <div className="w-4 h-4 rounded-full bg-black dark:bg-white flex items-center justify-center">
                      <div className="w-2 h-2 rounded-full bg-white dark:bg-black" />
                    </div>
                  )}
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {role.description}
                </p>
              </div>
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default RoleSelector;
