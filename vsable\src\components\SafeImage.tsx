'use client';

import NextImage, { ImageProps } from 'next/image';
import { getImagePath } from '@/utils/imageUtils';
import { useState, useEffect } from 'react';

interface SafeImageProps extends Omit<ImageProps, 'src'> {
  src: string;
  debug?: boolean;
}

/**
 * A wrapper around Next.js Image component that ensures image paths are properly formatted
 */
const SafeImage = ({ src, debug = false, ...props }: SafeImageProps) => {
  const [error, setError] = useState<string | null>(null);
  const safeSrc = getImagePath(src);
  const isExternal = safeSrc.startsWith('http://') || safeSrc.startsWith('https://');

  useEffect(() => {
    if (debug) {
      console.log(`[SafeImage] Original: "${src}" → Safe: "${safeSrc}"`);
    }
  }, [src, safeSrc, debug]);

  const handleError = () => {
    setError(`Failed to load image: ${safeSrc}`);
    console.error(`[SafeImage] Error loading image: "${safeSrc}" (original: "${src}")`);
    
    // Try fallback to direct MinIO URL if proxy fails
    if (safeSrc.includes('/api/upload/serve/')) {
      const filename = safeSrc.split('/').pop();
      const fallbackUrl = `${process.env.NEXT_PUBLIC_MINIO_URL}/uploads/${filename}`;
      console.log(`[SafeImage] Trying fallback URL: ${fallbackUrl}`);
      // Update the image src
      if (typeof window !== 'undefined') {
        const img = new window.Image();
        img.src = fallbackUrl;
        img.onload = () => {
          setError(null);
        };
      }
    }
  };

  if (error) {
    return (
      <div className="flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-md p-2">
        <span className="text-xs text-gray-500 dark:text-gray-400">Image not available</span>
      </div>
    );
  }
  return (
    <NextImage
      src={safeSrc}
      {...props}
      alt={props.alt || "Image"}
      onError={handleError}
      unoptimized={isExternal}
    />
  )
}

export default SafeImage;

