// This is the original content of frontend/vsable/src/app/store/[slug]/page.tsx
// It will now serve as the 'default' template.
'use client';
// import { useState, useEffect } from 'react'; // Not used in this component
import Image from 'next/image';
import { FiSearch, FiTag, FiImage } from 'react-icons/fi';
import { FaTelegram, FaWhatsapp, FaPhone, FaInstagram, FaFacebook, FaTiktok } from 'react-icons/fa';
import { Store, Product } from '@/types'; // Use shared types
import { getImagePath } from '@/utils/imageUtils';

interface TemplateDefaultProps {
  store: Store;
  products: Product[]; // These are already filtered by the parent StorePage
  categories: string[]; // Passed from parent
  searchQuery: string;
  selectedCategory: string;
  onSearchQueryChange: (query: string) => void;
  onSelectedCategoryChange: (category: string) => void;
  handleContactClick: (contactLink: string | null) => void;
}

const TemplateDefault: React.FC<TemplateDefaultProps> = ({
  store,
  products, // These are the filteredProducts from the parent
  categories,
  searchQuery,
  selectedCategory,
  onSearchQueryChange,
  onSelectedCategoryChange,
  handleContactClick,
}) => {
  // The filtering logic is now handled by the parent StorePage.
  // This component receives the already filtered `products`.

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Store Banner */}
      <div className="relative h-64 md:h-80 w-full bg-blue-600 dark:bg-blue-800">
        <div className="absolute inset-0 flex items-center">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
            <div className="flex items-center">
              <div className="h-24 w-24 relative rounded-full overflow-hidden border-4 border-white bg-white">
                {store.logo_url ? (
                  <Image
                    src={getImagePath(store.logo_url)}
                    alt={`${store.name} logo`}
                    fill
                    style={{ objectFit: 'cover' }}
                  />
                ) : (
                  <div className="h-full w-full flex items-center justify-center bg-gray-200">
                    <FiImage className="h-12 w-12 text-gray-400" />
                  </div>
                )}
              </div>
              <div className="ml-6">
                <h1 className="text-3xl font-bold text-white">{store.name}</h1>
                <p className="text-sm text-white opacity-80">{store.view_count || 0} views</p>
                <div className="flex items-center mt-2 space-x-4">
                  {store.telegram && (
                    <a
                      href={store.telegram.startsWith('https://') ? store.telegram : `https://t.me/${store.telegram.replace('@', '')}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-white hover:text-blue-200 flex items-center"
                    >
                      <FaTelegram className="h-5 w-5 mr-1" />
                      <span className="text-sm">Telegram</span>
                    </a>
                  )}
                  {store.whatsapp && (
                    <a
                      href={`https://wa.me/${store.whatsapp.replace(/[^0-9]/g, '')}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-white hover:text-blue-200 flex items-center"
                    >
                      <FaWhatsapp className="h-5 w-5 mr-1" />
                      <span className="text-sm">WhatsApp</span>
                    </a>
                  )}
                  {store.phone && (
                    <a
                      href={`tel:${store.phone}`}
                      className="text-white hover:text-blue-200 flex items-center"
                    >
                      <FaPhone className="h-5 w-5 mr-1" />
                      <span className="text-sm">Call</span>
                    </a>
                  )}
                  {store.instagram && (
                    <a
                      href={`https://instagram.com/${store.instagram.replace('@', '')}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-white hover:text-blue-200 flex items-center"
                    >
                      <FaInstagram className="h-5 w-5 mr-1" />
                      <span className="text-sm">Instagram</span>
                    </a>
                  )}
                  {store.facebook && (
                    <a
                      href={store.facebook.startsWith('https://') ? store.facebook : `https://facebook.com/${store.facebook.replace('@', '')}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-white hover:text-blue-200 flex items-center"
                    >
                      <FaFacebook className="h-5 w-5 mr-1" />
                      <span className="text-sm">Facebook</span>
                    </a>
                  )}
                  {store.tiktok && (
                    <a
                      href={`https://tiktok.com/@${store.tiktok.replace('@', '')}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-white hover:text-blue-200 flex items-center"
                    >
                      <FaTiktok className="h-5 w-5 mr-1" />
                      <span className="text-sm">TikTok</span>
                    </a>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Store Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {store.description && (
          <p className="text-gray-600 dark:text-gray-400 mb-8">{store.description}</p>
        )}

        {/* Search and Filter */}
        <div className="flex flex-col md:flex-row justify-between mb-8">
          <div className="relative mb-4 md:mb-0 md:w-1/2">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FiSearch className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Search products..."
              value={searchQuery}
              onChange={(e) => onSearchQueryChange(e.target.value)}
            />
          </div>
          <div className="flex overflow-x-auto space-x-2 pb-2">
            {categories.map((category) => (
              <button
                key={category}
                className={`px-4 py-2 rounded-full text-sm font-medium ${
                  selectedCategory === category
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                }`}
                onClick={() => onSelectedCategoryChange(category)}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Products Grid */}
        {products.length === 0 ? (
          <div className="text-center py-12">
            <FiSearch className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-lg font-medium text-gray-900 dark:text-white">No products found</h3>
            <p className="mt-1 text-gray-500 dark:text-gray-400">Try adjusting your search or filter to find what you&apos;re looking for.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {products.map((product) => (
              <div key={product.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div className="h-48 w-full relative">
                  {product.image_url ? ( // Using image_url as per original template
                    <Image
                      src={getImagePath(product.image_url)}
                      alt={product.name}
                      fill
                      sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, (max-width: 1280px) 33vw, 25vw"
                      style={{ objectFit: 'cover' }}
                    />
                  ) : (
                    <div className="h-full w-full flex items-center justify-center bg-gray-200 dark:bg-gray-700">
                      <FiImage className="h-12 w-12 text-gray-400" />
                    </div>
                  )}
                </div>
                <div className="p-4">
                  <div className="flex justify-between">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">{product.name}</h3>
                    <span className="text-blue-600 dark:text-blue-400 font-bold">${product.price.toFixed(2)}</span>
                  </div>
                  <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">{product.description || 'No description'}</p>
                  {product.category && (
                    <div className="mt-4">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        {product.category}
                      </span>
                    </div>
                  )}
                  {product.tags && product.tags.length > 0 && (
                    <div className="mt-2 flex flex-wrap gap-1">
                      {product.tags.map((tag, index) => (
                        <span key={index} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                          <FiTag className="mr-1 h-3 w-3" />
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}
                  <button
                    onClick={() => handleContactClick(product.contact_link)}
                    disabled={!product.contact_link}
                    className="mt-4 w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <FaTelegram className="mr-2 h-4 w-4" /> {/* Assuming Telegram is the primary contact */}
                    Contact Vendor
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default TemplateDefault;