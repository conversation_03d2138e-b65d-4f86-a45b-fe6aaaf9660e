// Interactive Showcase: Hero product section, unique navigation elements (conceptual)
import Image from 'next/image';
import { FiImage, FiShoppingCart } from 'react-icons/fi';
import { Store, Product } from '@/types';
import { useState } from 'react'; // For interactive elements
import { getImagePath } from '@/utils/imageUtils';

interface TemplateProps {
  store: Store;
  products: Product[];
  searchQuery: string;
  selectedCategory: string;
  handleContactClick: (contactLink: string | null) => void;
}

const TemplateFive: React.FC<TemplateProps> = ({ store, products, handleContactClick }) => {
  const [showMenu, setShowMenu] = useState(true); // Default to showing menu, not cover
  const filteredProducts = products;
  const [activeProduct, setActiveProduct] = useState<Product | null>(products.length > 0 ? products[0] : null);

  const CoverPage = () => (
    <div className="h-screen relative">
      <div
        className="absolute inset-0 bg-cover bg-center"
        style={{
          backgroundImage: store.cover_image_url
            ? `url('${getImagePath(store.cover_image_url)}')`
            : undefined
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-b from-blue-900/70 to-slate-900/90"></div>
      </div>
      <div className="relative h-full flex flex-col items-center justify-center text-white">
        <h1 className="text-6xl font-extrabold mb-4 text-center">{store.name}</h1>
        <p className="text-xl mb-10 max-w-xl text-center text-blue-100">{store.description || 'Discover our interactive showcase'}</p>
        <button
          onClick={() => setShowMenu(true)}
          className="px-10 py-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all text-lg font-semibold"
        >
          Explore Products
        </button>
      </div>
    </div>
  );

  const MainContent = () => (
    <div className="bg-slate-50 dark:bg-slate-900 text-slate-800 dark:text-slate-200 min-h-screen">
      {/* Split Layout Header */}
      <header className="container mx-auto px-4 py-8 md:flex md:items-center md:justify-between">
        <div>
          {store.logo_url && (
            <Image
              src={store.logo_url
                ? getImagePath(store.logo_url)
                : 'https://via.placeholder.com/300'
              }
              alt={`${store.name} logo`}
              width={50}
              height={50}
              className="mb-2 md:mb-0 rounded-md object-contain"
            />
          )}
          <h1 className="text-3xl font-bold text-slate-900 dark:text-white">{store.name}</h1>
          <p className="text-slate-600 dark:text-slate-400">{store.description}</p>
        </div>
        <nav className="mt-4 md:mt-0">
          {/* Conceptual: Could be category links or scroll-to sections */}
          <a href="#products" className="text-slate-700 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2">Products</a>
          <a href="#contact" className="text-slate-700 dark:text-slate-300 hover:text-blue-600 dark:hover:text-blue-400 px-3 py-2">Contact</a>
        </nav>
      </header>

      {/* Hero Product Section (if products exist) */}
      {activeProduct && (
        <section className="bg-slate-100 dark:bg-slate-800 py-12 md:py-20">
          <div className="container mx-auto px-4 md:flex md:items-center gap-8">
            <div className="md:w-1/2 h-80 md:h-96 relative rounded-lg overflow-hidden shadow-lg bg-slate-200 dark:bg-slate-700">
              {activeProduct.imageUrl ? (
                <Image
                  src={activeProduct.imageUrl
                    ? getImagePath(activeProduct.imageUrl)
                    : 'https://via.placeholder.com/300'
                  }
                  alt={activeProduct.name}
                  fill
                  style={{ objectFit: 'cover' }}
                />
              ) : (
                <FiImage className="w-24 h-24 text-slate-400 m-auto" />
              )}
            </div>
            <div className="md:w-1/2 mt-6 md:mt-0">
              <span className="text-sm font-semibold text-blue-600 dark:text-blue-400 uppercase">Featured Product</span>
              <h2 className="text-3xl md:text-4xl font-extrabold text-slate-900 dark:text-white mt-2">{activeProduct.name}</h2>
              <p className="text-slate-600 dark:text-slate-300 mt-4 text-lg">{activeProduct.description}</p>
              <p className="text-4xl font-bold text-slate-800 dark:text-slate-100 mt-6">${activeProduct.price.toFixed(2)}</p>
              {activeProduct.contact_link && (
                <button
                    onClick={() => handleContactClick(activeProduct.contact_link)}
                    className="mt-8 px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-lg font-semibold flex items-center"
                >
                    <FiShoppingCart className="mr-2" /> Get This Deal
                </button>
              )}
            </div>
          </div>
        </section>
      )}

      {/* Product Grid / List */}
      <main id="products" className="container mx-auto px-4 py-12">
        <h2 className="text-2xl font-semibold text-slate-700 dark:text-slate-200 mb-8 text-center">More From Us</h2>
        {filteredProducts.length === 0 ? (
          <p className="text-slate-500 dark:text-slate-400 text-center">No other products to show.</p>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredProducts.map((product) => (
              <div
                key={product.id}
                className={`bg-white dark:bg-slate-800 rounded-lg shadow-md overflow-hidden cursor-pointer transition-all hover:shadow-xl ${activeProduct?.id === product.id ? 'ring-2 ring-blue-500' : ''}`}
                onClick={() => setActiveProduct(product)} // Click to feature
              >
                <div className="w-full h-40 relative bg-slate-200 dark:bg-slate-700">
                  {product.imageUrl ? (
                    <Image
                      src={product.imageUrl
                        ? getImagePath(product.imageUrl)
                        : 'https://via.placeholder.com/300'
                      }
                      alt={product.name}
                      fill
                      sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, (max-width: 1280px) 33vw, 25vw"
                      style={{ objectFit: 'cover' }}
                    />
                  ) : (
                    <FiImage className="w-12 h-12 text-slate-400 m-auto" />
                  )}
                </div>
                <div className="p-4">
                  <h3 className="text-md font-semibold text-slate-900 dark:text-white truncate">{product.name}</h3>
                  <p className="text-sm text-slate-500 dark:text-slate-400">${product.price.toFixed(2)}</p>
                </div>
              </div>
            ))}
          </div>
        )}
      </main>
      <footer id="contact" className="bg-slate-200 dark:bg-slate-800 py-10 mt-12 text-center">
        <p className="text-slate-600 dark:text-slate-400">Questions? Contact us at {store.phone || 'our main line'}.</p>
        <p className="text-xs text-slate-500 dark:text-slate-500 mt-2">&copy; {new Date().getFullYear()} {store.name}.</p>
      </footer>
    </div>
  );

  return (
    <div className="font-sans">
      {!showMenu ? <CoverPage /> : <MainContent />}
    </div>
  );
};
export default TemplateFive;