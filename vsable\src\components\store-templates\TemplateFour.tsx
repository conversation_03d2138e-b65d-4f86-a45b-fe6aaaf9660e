import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON>he<PERSON>ronUp, FiSearch, FiShoppingCart } from 'react-icons/fi';
import { FaTelegram, FaWhatsapp, FaPhone, FaInstagram, FaFacebook, FaTiktok } from 'react-icons/fa';
import { LazyLoadImage } from 'react-lazy-load-image-component';
import { Store, Product } from '@/types';
import { getImagePath } from '@/utils/imageUtils';

interface TemplateProps {
  store: Store;
  products: Product[];
  searchQuery: string;
  selectedCategory: string;
  handleContactClick: (contactLink: string | null) => void;
  onSearchQueryChange?: (query: string) => void;
  onSelectedCategoryChange?: (category: string) => void;
}

const TemplateFour: React.FC<TemplateProps> = ({
  store,
  products,
  searchQuery,
  selectedCategory,
  handleContactClick,
  onSearchQueryChange,
  onSelectedCategoryChange
}) => {
  const [showBackToTop, setShowBackToTop] = useState(false);
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [activeCategory, setActiveCategory] = useState(selectedCategory || 'All');

  useEffect(() => {
    setLocalSearchQuery(searchQuery);
  }, [searchQuery]);

  useEffect(() => {
    setActiveCategory(selectedCategory || 'All');
  }, [selectedCategory]);

  useEffect(() => {
    const handleScroll = () => {
      setShowBackToTop(window.scrollY > 300);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const categories = ['All', ...Array.from(new Set(products.map(p => p.category).filter((c): c is string => typeof c === 'string' && c.trim() !== '')))]

  const handleSearchChange = (query: string) => {
    setLocalSearchQuery(query);
    if (onSearchQueryChange) onSearchQueryChange(query);
  };

  const handleCategoryChange = (category: string) => {
    setActiveCategory(category);
    if (onSelectedCategoryChange) onSelectedCategoryChange(category);
  };

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(localSearchQuery.toLowerCase()) ||
      (product.description && product.description.toLowerCase().includes(localSearchQuery.toLowerCase()));
    const matchesCategory = activeCategory === 'All' || product.category === activeCategory;
    return matchesSearch && matchesCategory;
  });

  const scrollToTop = () => window.scrollTo({ top: 0, behavior: 'smooth' });

  return (
    <div className="font-sans bg-gray-50">
      {/* Hero Section */}
      <div className="relative h-screen bg-cover bg-center" style={{
        backgroundImage: store.cover_image_url
          ? `url('${getImagePath(store.cover_image_url)}')`
          : `url('https://images.unsplash.com/photo-1576426863848-c21f53c60b19')`
      }}>
        <div className="absolute inset-0 flex items-center justify-center text-center text-white">
          <div>
            <h1 className="text-5xl md:text-7xl font-serif mb-4">{store.name}</h1>
            <p className="text-xl md:text-2xl font-light">{store.description || 'Discover our exclusive collection'}</p>
          </div>
        </div>
      </div>

      {/* About Section */}
      <section className="py-20 bg-white text-center">
        <div className="max-w-3xl mx-auto px-4">
          <h2 className="text-4xl font-serif mb-6">About Us</h2>
          <p className="text-lg text-gray-700">
            We offer premium products that blend style and substance. Crafted with passion and precision, each piece reflects our commitment to excellence.
          </p>
        </div>
      </section>

      {/* Search and Filters */}
      <section className="py-10 bg-gray-100">
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-6">
            <div className="relative w-full md:w-1/2">
              <FiSearch className="absolute left-3 top-3 text-gray-400" />
              <input
                type="text"
                value={localSearchQuery}
                onChange={(e) => handleSearchChange(e.target.value)}
                placeholder="Search products..."
                className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-2 focus:ring-gray-300"
              />
            </div>
            <div className="flex flex-wrap gap-2 justify-center md:justify-start">
              {categories.map(category => (
                <button
                  key={category}
                  onClick={() => handleCategoryChange(category)}
                  className={`px-5 py-2 rounded-full text-sm capitalize ${activeCategory === category ? 'bg-black text-white' : 'bg-white text-gray-700 hover:bg-gray-200'}`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>

          {/* Product Grid */}
          <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {filteredProducts.map(product => (
              <motion.div
                key={product.id}
                layout
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="bg-white rounded-lg shadow hover:shadow-lg overflow-hidden"
              >
                <div className="relative pb-[100%]">
                  <LazyLoadImage
                    src={product.image_url
                      ? getImagePath(product.image_url)
                      : 'https://via.placeholder.com/300'
                    }
                    alt={product.name}
                    // effect="blur"
                    className="absolute inset-0 w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                  />
                </div>
                <div className="p-4">
                  <h3 className="text-lg font-semibold mb-1">{product.name}</h3>
                  <p className="text-sm text-gray-600 mb-2">{product.description || 'No description available.'}</p>
                  <div className="flex justify-between items-center">
                    <span className="text-black font-bold">${product.price.toFixed(2)}</span>
                    <button
                      onClick={() => handleContactClick(product.contact_link)}
                      className="p-2 bg-gray-100 rounded-full hover:bg-gray-200"
                      disabled={!product.contact_link}
                    >
                      <FiShoppingCart className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Back to Top Button */}
      <AnimatePresence>
        {showBackToTop && (
          <motion.button
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            onClick={scrollToTop}
            className="fixed bottom-8 right-8 bg-black text-white p-3 rounded-full shadow-lg z-50"
          >
            <FiChevronUp size={24} />
          </motion.button>
        )}
      </AnimatePresence>

      {/* Footer */}
      <footer className="bg-black text-white py-10">
        <div className="text-center space-y-2">
          <h3 className="text-xl font-semibold">{store.name}</h3>
          <div className="flex justify-center space-x-4">
            {store.phone && <a href={`tel:${store.phone}`}><FaPhone /></a>}
            {store.whatsapp && <a href={`https://wa.me/${store.whatsapp.replace(/[^0-9]/g, '')}`}><FaWhatsapp /></a>}
            {store.telegram && <a href={`https://t.me/${store.telegram.replace('@', '')}`}><FaTelegram /></a>}
            {store.instagram && <a href={`https://instagram.com/${store.instagram.replace('@', '')}`}><FaInstagram /></a>}
            {store.facebook && <a href={store.facebook.startsWith('https://') ? store.facebook : `https://facebook.com/${store.facebook.replace('@', '')}`}><FaFacebook /></a>}
            {store.tiktok && <a href={`https://tiktok.com/@${store.tiktok.replace('@', '')}`}><FaTiktok /></a>}
          </div>
          <p className="text-sm">&copy; {new Date().getFullYear()} {store.name}. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default TemplateFour;