import Image from 'next/image';
import { FiImage, FiShoppingCart, FiTag, FiSearch } from 'react-icons/fi';
import { FaT<PERSON>gram, FaWhatsapp, FaPhone, FaInstagram, FaFacebook, FaTiktok } from 'react-icons/fa';
import { Store, Product } from '@/types';
import { useState, useEffect } from 'react';
import { getImagePath } from '@/utils/imageUtils';

interface TemplateProps {
  store: Store;
  products: Product[];
  searchQuery: string;
  selectedCategory: string;
  handleContactClick: (contactLink: string | null) => void;
  onSearchQueryChange?: (query: string) => void;
  onSelectedCategoryChange?: (category: string) => void;
}

const TemplateOne: React.FC<TemplateProps> = ({
  store,
  products,
  searchQuery,
  selectedCategory,
  handleContactClick,
  onSearchQueryChange,
  onSelectedCategoryChange
}) => {
  const [showMenu, setShowMenu] = useState(true); // Default to showing menu, not cover
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [activeCategory, setActiveCategory] = useState(selectedCategory || 'All');

  useEffect(() => {
    setLocalSearchQuery(searchQuery);
  }, [searchQuery]);

  useEffect(() => {
    setActiveCategory(selectedCategory || 'All');
  }, [selectedCategory]);

  const categories = ['All', ...Array.from(new Set(products.map(product => product.category).filter((c): c is string => typeof c === 'string' && c.trim() !== '')))];

  const handleSearchChange = (query: string) => {
    setLocalSearchQuery(query);
    if (onSearchQueryChange) {
      onSearchQueryChange(query);
    }
  };

  const handleCategoryChange = (category: string) => {
    setActiveCategory(category);
    if (onSelectedCategoryChange) {
      onSelectedCategoryChange(category);
    }
  };

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(localSearchQuery.toLowerCase()) ||
                         (product.description && product.description.toLowerCase().includes(localSearchQuery.toLowerCase()));
    const matchesCategory = activeCategory === 'All' || product.category === activeCategory;
    return matchesSearch && matchesCategory;
  });

  const CoverPage = () => (
    <div className="h-screen relative">
      <div
        className="absolute inset-0 bg-cover bg-center"
        style={{
          backgroundImage: store.cover_image_url
            ? `url('${getImagePath(store.cover_image_url)}')`
            : `url('https://images.unsplash.com/photo-1441986300917-64674bd600d8')`
        }}
      >
        <div className="absolute inset-0 bg-black bg-opacity-40"></div>
      </div>
      <div className="relative h-full flex flex-col items-center justify-center text-white">
        <h1 className="text-6xl font-light mb-2">{store.name}</h1>
        <p className="text-lg mb-2">{store.view_count || 0} views</p>
        <p className="text-xl mb-8">{store.description || 'Welcome to our store'}</p>
        <button
          onClick={() => setShowMenu(true)}
          className="px-8 py-3 bg-white text-gray-900 rounded-full hover:bg-opacity-90 transition-all"
        >
          View Products
        </button>
      </div>
    </div>
  );

  const MainContent = () => (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Hero Banner */}
      <div className="relative h-64 md:h-80 w-full bg-black dark:bg-gray-800">
        <div className="absolute inset-0 flex items-center">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
            <div className="flex items-center">
              <div className="h-24 w-24 relative rounded-full overflow-hidden border-4 border-white bg-white">
                {store.logo_url ? (
                  <Image
                    src={store.logo_url
                      ? getImagePath(store.logo_url)
                      : 'https://via.placeholder.com/300'
                    }
                    alt={`${store.name} logo`}
                    fill
                    sizes="96px"
                    style={{ objectFit: 'cover' }}
                  />
                ) : (
                  <div className="h-full w-full flex items-center justify-center bg-gray-200">
                    <FiImage className="h-12 w-12 text-gray-400" />
                  </div>
                )}
              </div>
              <div className="ml-6">
                <h1 className="text-3xl md:text-4xl font-bold text-white">{store.name}</h1>
                {/* <p className="text-sm text-white opacity-80">{store.view_count || 0} views</p> */}
                <div className="flex items-center mt-3 space-x-4">
                  {store.telegram && (
                    <a
                      href={store.telegram.startsWith('https://') ? store.telegram : `https://t.me/${store.telegram.replace('@', '')}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-white hover:text-gray-200 flex items-center"
                    >
                      <FaTelegram className="h-5 w-5 mr-1" />
                      <span className="text-sm">Telegram</span>
                    </a>
                  )}
                  {store.whatsapp && (
                    <a
                      href={`https://wa.me/${store.whatsapp.replace(/[^0-9]/g, '')}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-white hover:text-gray-200 flex items-center"
                    >
                      <FaWhatsapp className="h-5 w-5 mr-1" />
                      <span className="text-sm">WhatsApp</span>
                    </a>
                  )}
                  {store.phone && (
                    <a
                      href={`tel:${store.phone}`}
                      className="text-white hover:text-gray-200 flex items-center"
                    >
                      <FaPhone className="h-5 w-5 mr-1" />
                      <span className="text-sm">Call</span>
                    </a>
                  )}
                  {store.instagram && (
                    <a
                      href={`https://instagram.com/${store.instagram.replace('@', '')}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-white hover:text-gray-200 flex items-center"
                    >
                      <FaInstagram className="h-5 w-5 mr-1" />
                      <span className="text-sm">Instagram</span>
                    </a>
                  )}
                  {store.facebook && (
                    <a
                      href={store.facebook.startsWith('https://') ? store.facebook : `https://facebook.com/${store.facebook.replace('@', '')}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-white hover:text-gray-200 flex items-center"
                    >
                      <FaFacebook className="h-5 w-5 mr-1" />
                      <span className="text-sm">Facebook</span>
                    </a>
                  )}
                  {store.tiktok && (
                    <a
                      href={`https://tiktok.com/@${store.tiktok.replace('@', '')}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-white hover:text-gray-200 flex items-center"
                    >
                      <FaTiktok className="h-5 w-5 mr-1" />
                      <span className="text-sm">TikTok</span>
                    </a>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {store.description && (
          <p className="text-gray-600 dark:text-gray-400 mb-8 text-center max-w-2xl mx-auto">{store.description}</p>
        )}

        {/* Search and Filter */}
        <div className="flex flex-col md:flex-row justify-between items-center mb-8 gap-4">
          <div className="relative w-full md:w-1/2">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FiSearch className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-black focus:border-black"
              placeholder="Search menu items..."
              value={localSearchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
            />
          </div>
          <div className="flex overflow-x-auto space-x-2 pb-2">
            {categories.map((category) => (
              <button
                key={category}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                  activeCategory === category
                    ? 'bg-black text-white'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                }`}
                onClick={() => handleCategoryChange(category)}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Products Grid */}
        {filteredProducts.length === 0 ? (
          <div className="text-center py-12">
            <FiSearch className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-lg font-medium text-gray-900 dark:text-white">No items found</h3>
            <p className="mt-1 text-gray-500 dark:text-gray-400">Try adjusting your search or filter to find what you&apos;re looking for.</p>
          </div>
        ) : (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {filteredProducts.map((product) => (
              <div
                key={product.id}
                className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden transform hover:scale-105 transition-transform duration-300"
              >
                <div className="relative pb-[100%] overflow-hidden">
                  {product.image_url ? (
                    <Image
                      src={product.image_url
                        ? getImagePath(product.image_url)
                        : 'https://via.placeholder.com/300'
                      }
                      alt={product.name}
                      fill
                      sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                      style={{ objectFit: 'cover' }}
                      className="transform transition-transform duration-300 hover:scale-110"
                    />
                  ) : (
                    <div className="absolute inset-0 flex items-center justify-center bg-gray-200 dark:bg-gray-700">
                      <FiImage className="h-12 w-12 text-gray-400" />
                    </div>
                  )}
                </div>
                <div className="p-4">
                  <div className="flex justify-between items-start">
                    <h3 className="text-sm sm:text-lg font-semibold text-gray-900 dark:text-white">{product.name}</h3>
                    <span className="text-sm sm:text-base text-black dark:text-white font-bold">${product.price.toFixed(2)}</span>
                  </div>
                  <p className="mt-2 text-xs sm:text-sm text-gray-600 dark:text-gray-400 line-clamp-2">{product.description || 'No description'}</p>
                  {product.category && (
                    <div className="mt-3">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                        {product.category}
                      </span>
                    </div>
                  )}
                  {product.tags && product.tags.length > 0 && (
                    <div className="mt-2 flex flex-wrap gap-1">
                      {product.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                        >
                          <FiTag className="mr-1 h-3 w-3" />
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}
                  <button
                    onClick={() => handleContactClick(product.contact_link)}
                    disabled={!product.contact_link}
                    className="mt-4 w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <FiShoppingCart className="mr-2 h-4 w-4" />
                    Order Now
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <footer className="bg-gray-800 dark:bg-gray-900 py-8 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-gray-300">
          <h3 className="text-xl font-semibold">{store.name}</h3>
          <div className="mt-4 flex flex-wrap justify-center gap-4">
            {store.phone && (
              <p>
                Phone: <a href={`tel:${store.phone}`} className="hover:text-gray-400">{store.phone}</a>
              </p>
            )}
            {store.whatsapp && (
              <p>
                WhatsApp: <a href={`https://wa.me/${store.whatsapp.replace(/[^0-9]/g, '')}`} className="hover:no-underline hover:text-gray-400">Message Us</a>
              </p>
            )}
            {store.instagram && (
              <p>
                Instagram: <a href={`https://instagram.com/${store.instagram.replace('@', '')}`} className="hover:no-underline hover:text-gray-400">@{store.instagram.replace('@', '')}</a>
              </p>
            )}
            {store.telegram && (
              <p>
                Telegram: <a href={store.telegram.startsWith('https://') ? store.telegram : `https://t.me/${store.telegram.replace('@', '')}`} className="hover:no-underline hover:text-gray-400">Contact Us</a>
              </p>
            )}
            {store.facebook && (
              <p>
                Facebook: <a href={store.facebook.startsWith('https://') ? store.facebook : `https://facebook.com/${store.facebook.replace('@', '')}`} className="hover:no-underline hover:text-gray-400">Follow Us</a>
              </p>
            )}
            {store.tiktok && (
              <p>
                TikTok: <a href={`https://tiktok.com/@${store.tiktok.replace('@', '')}`} className="hover:no-underline hover:text-gray-400">@{store.tiktok.replace('@', '')}</a>
              </p>
            )}
          </div>
          <p className="mt-4">© {new Date().getFullYear()} {store.name}. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );

  return (
    <div className="font-sans">
      {!showMenu ? <CoverPage /> : <MainContent />}
    </div>
  );
};

export default TemplateOne;