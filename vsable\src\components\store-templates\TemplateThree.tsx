import Image from 'next/image';
import { FiSearch, FiShoppingCart } from 'react-icons/fi';
import { FaT<PERSON>gram, FaWhatsapp, FaPhone, FaInstagram } from 'react-icons/fa';
import { Store, Product } from '@/types';
import { useState, useEffect } from 'react';
import { getImagePath } from '@/utils/imageUtils';

interface TemplateProps {
  store: Store;
  products: Product[];
  searchQuery: string;
  selectedCategory: string;
  handleContactClick: (contactLink: string | null) => void;
  onSearchQueryChange?: (query: string) => void;
  onSelectedCategoryChange?: (category: string) => void;
}

const TemplateThree: React.FC<TemplateProps> = ({
  store,
  products,
  searchQuery,
  selectedCategory,
  handleContactClick,
  onSearchQueryChange,
  onSelectedCategoryChange
}) => {
  const [showMenu, setShowMenu] = useState(false);
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [activeCategory, setActiveCategory] = useState(selectedCategory || 'All');

  useEffect(() => {
    setLocalSearchQuery(searchQuery);
  }, [searchQuery]);

  useEffect(() => {
    setActiveCategory(selectedCategory || 'All');
  }, [selectedCategory]);

  const categories = ['All', ...Array.from(new Set(products.map(product => product.category).filter((c): c is string => typeof c === 'string' && c.trim() !== '')))];

  const handleSearchChange = (query: string) => {
    setLocalSearchQuery(query);
    if (onSearchQueryChange) {
      onSearchQueryChange(query);
    }
  };

  const handleCategoryChange = (category: string) => {
    setActiveCategory(category);
    if (onSelectedCategoryChange) {
      onSelectedCategoryChange(category);
    }
  };

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(localSearchQuery.toLowerCase()) ||
                         (product.description && product.description.toLowerCase().includes(localSearchQuery.toLowerCase()));
    const matchesCategory = activeCategory === 'All' || product.category === activeCategory;
    return matchesSearch && matchesCategory;
  });

  const CoverPage = () => (
    <div className="h-screen relative">
      <div
        className="absolute inset-0 bg-cover bg-center"
        style={{
          backgroundImage: store.cover_image_url
            ? `url('${getImagePath(store.cover_image_url)}')`
            : `url('https://images.unsplash.com/photo-1576426863848-c21f53c60b19')`
        }}
      >
        {/* <div className="absolute inset-0 bg-black bg-opacity-40"></div> */}
      </div>
      <div className="relative h-full flex flex-col items-center justify-center text-white">
        <h1 className="text-6xl font-light mb-4">{store.name}</h1>
        <p className="text-xl mb-8">{store.description || 'Discover our collection'}</p>
        <button
          onClick={() => setShowMenu(true)}
          className="px-8 py-3 bg-white text-gray-900 rounded-full hover:bg-opacity-90 transition-all"
        >
          View Collection
        </button>
      </div>
    </div>
  );

  const ProductCard = ({ product }: { product: Product }) => (
    <div className="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
      <div className="relative pb-[100%]">
        <Image
          src={
            product.image_url
              ? getImagePath(product.image_url)
              : 'https://via.placeholder.com/400x400?text=No+Image'
          }
          alt={product.name}
          className="absolute inset-0 w-full h-full object-cover"
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
      </div>
      <div className="p-4">
        <h3 className="text-lg font-medium text-gray-900 mb-2">{product.name}</h3>
        <p className="text-gray-600 text-sm mb-4">{product.description || 'No description'}</p>
        <div className="flex justify-between items-center">
          <span className="text-gray-900 font-medium">${product.price.toFixed(2)}</span>
          <button
            onClick={() => handleContactClick(product.contact_link)}
            disabled={!product.contact_link}
            className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors disabled:opacity-50"
          >
            <FiShoppingCart className="w-5 h-5" />
          </button>
        </div>
        {product.category && (
          <span className="mt-2 inline-block px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-600">
            {product.category}
          </span>
        )}
      </div>
    </div>
  );

  const MenuPage = () => (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-light text-gray-900">{store.name}</h1>
            <div className="flex space-x-2">
              {store.telegram && (
                <a href={store.telegram} target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:text-gray-900">
                  <FaTelegram className="h-5 w-5" />
                </a>
              )}
              {store.whatsapp && (
                <a href={`https://wa.me/${store.whatsapp.replace(/[^0-9]/g, '')}`} target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:text-gray-900">
                  <FaWhatsapp className="h-5 w-5" />
                </a>
              )}
              {store.phone && (
                <a href={`tel:${store.phone}`} className="text-gray-600 hover:text-gray-900">
                  <FaPhone className="h-5 w-5" />
                </a>
              )}
              {store.instagram && (
                <a href={`https://instagram.com/${store.instagram.replace('@', '')}`} target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:text-gray-900">
                  <FaInstagram className="h-5 w-5" />
                </a>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Search and Filter */}
        <div className="flex flex-col md:flex-row justify-between items-center mb-10 gap-4">
          <div className="relative w-full md:w-1/2">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FiSearch className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white text-gray-900 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-200 focus:border-gray-200"
              placeholder="Search skincare products..."
              value={localSearchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
            />
          </div>
          <div className="flex overflow-x-auto space-x-2 pb-2">
            {categories.map((category) => (
              <button
                key={category}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                  activeCategory === category
                    ? 'bg-gray-900 text-white'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
                onClick={() => handleCategoryChange(category)}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Product Grid */}
        <h2 className="text-3xl font-semibold mb-8 text-center text-gray-900">Our Products</h2>
        {filteredProducts.length === 0 ? (
          <div className="text-center py-12">
            <FiSearch className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-lg font-medium text-gray-900">No products found</h3>
            <p className="mt-1 text-gray-400">Try adjusting your search or filter to find what you&apos;re looking for.</p>
          </div>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {filteredProducts.map((product) => (
              <ProductCard key={product.id} product={product} />
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <footer className="bg-gray-800 py-8 mt-12 border-t border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-gray-300">
          <h3 className="text-xl font-semibold">{store.name}</h3>
          <div className="mt-4 flex flex-wrap justify-center gap-4">
            {store.phone && (
              <p>
                Phone: <a href={`tel:${store.phone}`} className="hover:text-cyan-400">{store.phone}</a>
              </p>
            )}
            {store.whatsapp && (
              <p>
                WhatsApp: <a href={`https://wa.me/${store.whatsapp.replace(/[^0-9]/g, '')}`} className="hover:no-underline hover:text-cyan-400">Message Us</a>
              </p>
            )}
            {store.instagram && (
              <p>
                Instagram: <a href={`https://instagram.com/${store.instagram.replace('@', '')}`} className="hover:no-underline hover:text-cyan-400">@{store.instagram.replace('@', '')}</a>
              </p>
            )}
            {store.telegram && (
              <p>
                Telegram: <a href={store.telegram.startsWith('https://') ? store.telegram : `https://t.me/${store.telegram.replace('@', '')}`} className="hover:no-underline hover:text-cyan-400">Contact Us</a>
              </p>
            )}
            {store.facebook && (
              <p>
                Facebook: <a href={store.facebook.startsWith('https://') ? store.facebook : `https://facebook.com/${store.facebook.replace('@', '')}`} className="hover:no-underline hover:text-cyan-400">Follow Us</a>
              </p>
            )}
            {store.tiktok && (
              <p>
                TikTok: <a href={`https://tiktok.com/@${store.tiktok.replace('@', '')}`} className="hover:no-underline hover:text-cyan-400">@{store.tiktok.replace('@', '')}</a>
              </p>
            )}
          </div>
          <p className="mt-4">© {new Date().getFullYear()} {store.name}. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );

  return (
    <div className="font-sans">
      {!showMenu ? <CoverPage /> : <MenuPage />}
    </div>
  );
};

export default TemplateThree;