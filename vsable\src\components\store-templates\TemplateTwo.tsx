import Image from 'next/image';
import { FiImage, FiShoppingCart, FiTag, FiSearch } from 'react-icons/fi';
import { FaTelegram, FaWhatsapp, FaPhone, FaInstagram, FaFacebook, FaTiktok } from 'react-icons/fa';
import { Store, Product } from '@/types';
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { getImagePath } from '@/utils/imageUtils';

interface TemplateProps {
  store: Store;
  products: Product[];
  searchQuery: string;
  selectedCategory: string;
  handleContactClick: (contactLink: string | null) => void;
  onSearchQueryChange?: (query: string) => void;
  onSelectedCategoryChange?: (category: string) => void;
}

const TemplateTwo: React.FC<TemplateProps> = ({
  store,
  products,
  searchQuery,
  selectedCategory,
  handleContactClick,
  onSearchQueryChange,
  onSelectedCategoryChange
}) => {
  const [showMenu, setShowMenu] = useState(true); // Default to showing menu, not cover
  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery);
  const [activeCategory, setActiveCategory] = useState(selectedCategory || 'All');

  useEffect(() => {
    setLocalSearchQuery(searchQuery);
  }, [searchQuery]);

  useEffect(() => {
    setActiveCategory(selectedCategory || 'All');
  }, [selectedCategory]);

  const categories = ['All', ...Array.from(new Set(products.map(product => product.category).filter((c): c is string => typeof c === 'string' && c.trim() !== '')))];

  const handleSearchChange = (query: string) => {
    setLocalSearchQuery(query);
    if (onSearchQueryChange) {
      onSearchQueryChange(query);
    }
  };

  const handleCategoryChange = (category: string) => {
    setActiveCategory(category);
    if (onSelectedCategoryChange) {
      onSelectedCategoryChange(category);
    }
  };

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(localSearchQuery.toLowerCase()) ||
                         (product.description && product.description.toLowerCase().includes(localSearchQuery.toLowerCase()));
    const matchesCategory = activeCategory === 'All' || product.category === activeCategory;
    return matchesSearch && matchesCategory;
  });

  const CoverPage = () => (
    <div className="h-screen relative">
      <div
        className="absolute inset-0 bg-cover bg-center"
        style={{
          backgroundImage: store.cover_image_url
            ? `url('${getImagePath(store.cover_image_url)}')`:undefined
            // : `url('https://images.unsplash.com/photo-1441986300917-64674bd600d8')`
        }}
      >
        <div className="absolute inset-0 bg-black bg-opacity-60"></div>
      </div>
      <div className="relative h-full flex flex-col items-center justify-center text-white">
        <h1 className="text-6xl font-bold mb-2">{store.name}</h1>
        <p className="text-lg mb-2">{store.view_count || 0} views</p>
        <p className="text-xl mb-8 max-w-xl text-center">{store.description || 'Discover our exclusive collection'}</p>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => setShowMenu(true)}
          className="px-8 py-3 bg-cyan-500 text-white rounded-full hover:bg-cyan-600 transition-all"
        >
          Explore Collection
        </motion.button>
      </div>
    </div>
  );

  const MainContent = () => (
    <div className="min-h-screen bg-gray-900 text-white font-sans">
      {/* Hero Banner */}
      <div
        className="relative h-80 md:h-96 w-full bg-cover bg-center"
        style={{ backgroundImage: store.cover_image_url? `url('${getImagePath(store.cover_image_url)}')`:undefined }}
      >
        <div className="absolute inset-0 bg-black/40 flex items-center">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
            <div className="flex flex-col md:flex-row items-center justify-between">
              <div className="flex items-center">
                <div className="h-20 w-20 relative rounded-full overflow-hidden border-4 border-white bg-white">
                  {store.logo_url ? (
                    <Image
                      src={store.logo_url
                        ? getImagePath(store.logo_url)
                        : 'https://via.placeholder.com/300'
                      }
                      alt={`${store.name} logo`}
                      fill
                      sizes="80px"
                      style={{ objectFit: 'cover' }}
                    />
                  ) : (
                    <div className="h-full w-full flex items-center justify-center bg-gray-700">
                      <FiImage className="h-10 w-10 text-gray-400" />
                    </div>
                  )}
                </div>
                <div className="ml-4">
                  <h1 className="text-4xl md:text-5xl font-light">{store.name}</h1>
                  <p className="mt-1 text-lg text-gray-300">{store.description || 'Discover the latest fashion trends'}</p>
                </div>
              </div>
              <div className="flex items-center mt-4 md:mt-0 space-x-4">
                {store.telegram && (
                  <a
                    href={store.telegram.startsWith('https://') ? store.telegram : `https://t.me/${store.telegram.replace('@', '')}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-300 hover:text-white flex items-center"
                  >
                    <FaTelegram className="h-5 w-5 mr-1" />
                    <span className="text-sm">Telegram</span>
                  </a>
                )}
                {store.whatsapp && (
                  <a
                    href={`https://wa.me/${store.whatsapp.replace(/[^0-9]/g, '')}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-300 hover:text-white flex items-center"
                  >
                    <FaWhatsapp className="h-5 w-5 mr-1" />
                    <span className="text-sm">WhatsApp</span>
                  </a>
                )}
                {store.phone && (
                  <a
                    href={`tel:${store.phone}`}
                    className="text-gray-300 hover:text-white flex items-center"
                  >
                    <FaPhone className="h-5 w-5 mr-1" />
                    <span className="text-sm">Call</span>
                  </a>
                )}
                {store.instagram && (
                  <a
                    href={`https://instagram.com/${store.instagram.replace('@', '')}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-300 hover:text-white flex items-center"
                  >
                    <FaInstagram className="h-5 w-5 mr-1" />
                    <span className="text-sm">Instagram</span>
                  </a>
                )}
                {store.facebook && (
                  <a
                    href={store.facebook.startsWith('https://') ? store.facebook : `https://facebook.com/${store.facebook.replace('@', '')}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-300 hover:text-white flex items-center"
                  >
                    <FaFacebook className="h-5 w-5 mr-1" />
                    <span className="text-sm">Facebook</span>
                  </a>
                )}
                {store.tiktok && (
                  <a
                    href={`https://tiktok.com/@${store.tiktok.replace('@', '')}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-300 hover:text-white flex items-center"
                  >
                    <FaTiktok className="h-5 w-5 mr-1" />
                    <span className="text-sm">TikTok</span>
                  </a>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Search and Filter */}
        <div className="flex flex-col md:flex-row justify-between items-center mb-12 gap-4">
          <h2 className="text-3xl font-light text-white mb-4 md:mb-0">Our Collection</h2>
          <div className="relative w-full md:w-1/2">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              className="w-full pl-10 pr-4 py-2 border border-gray-700 rounded-full bg-gray-800 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-cyan-500"
              placeholder="Search clothing items..."
              value={localSearchQuery}
              onChange={(e) => handleSearchChange(e.target.value)}
            />
          </div>
        </div>

        <div className="flex flex-wrap gap-4 mb-8">
          {categories.map((category) => (
            <button
              key={category}
              className={`px-6 py-2 rounded-full text-sm font-medium transition-colors capitalize ${
                activeCategory === category
                  ? 'bg-cyan-500 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
              onClick={() => handleCategoryChange(category)}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Product Grid */}
        {filteredProducts.length === 0 ? (
          <div className="text-center py-12">
            <FiSearch className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-lg font-medium text-white">No items found</h3>
            <p className="mt-1 text-gray-400">Try adjusting your search or filter to find what you&apos;re looking for.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredProducts.map((product) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="bg-gray-800 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow"
              >
                <div className="relative pb-[100%]">
                  {product.image_url ? (
                    <Image
                      src={product.image_url
                        ? getImagePath(product.image_url)
                        : 'https://via.placeholder.com/300'
                      }
                      alt={product.name}
                      fill
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                      style={{ objectFit: 'cover' }}
                    />
                  ) : (
                    <div className="absolute inset-0 flex items-center justify-center bg-gray-700">
                      <FiImage className="h-16 w-16 text-gray-500" />
                    </div>
                  )}
                </div>
                <div className="p-6">
                  <h3 className="text-lg font-medium text-white mb-2 truncate">{product.name}</h3>
                  <p className="text-gray-400 text-sm mb-4 line-clamp-2">{product.description || 'No description'}</p>
                  <div className="flex justify-between items-center">
                    <span className="text-white font-medium">${product.price.toFixed(2)}</span>
                    <button
                      onClick={() => handleContactClick(product.contact_link)}
                      disabled={!product.contact_link}
                      className="p-2 rounded-full bg-gray-700 hover:bg-gray-600 text-white transition-colors disabled:opacity-50"
                    >
                      <FiShoppingCart className="h-5 w-5" />
                    </button>
                  </div>
                  {product.category && (
                    <span className="mt-3 inline-flex px-2 py-1 text-xs rounded-full bg-cyan-700 text-cyan-200">
                      <FiTag className="mr-1 h-3 w-3" /> {product.category}
                    </span>
                  )}
                  {product.tags && product.tags.length > 0 && (
                    <div className="mt-3 flex flex-wrap gap-1">
                      {product.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-700 text-gray-300"
                        >
                          <FiTag className="mr-1 h-3 w-3" />
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <footer className="bg-gray-800 py-8 mt-12 border-t border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-gray-300">
          <h3 className="text-xl font-semibold">{store.name}</h3>
          <div className="mt-4 flex flex-wrap justify-center gap-4">
            {store.phone && (
              <p>
                Phone: <a href={`tel:${store.phone}`} className="hover:text-cyan-400">{store.phone}</a>
              </p>
            )}
            {store.whatsapp && (
              <p>
                WhatsApp: <a href={`https://wa.me/${store.whatsapp.replace(/[^0-9]/g, '')}`} className="hover:text-cyan-400">Message Us</a>
              </p>
            )}
            {store.instagram && (
              <p>
                Instagram: <a href={`https://instagram.com/${store.instagram.replace('@', '')}`} className="hover:text-cyan-400">@{store.instagram.replace('@', '')}</a>
              </p>
            )}
            {store.telegram && (
              <p>
                Telegram: <a href={store.telegram.startsWith('https://') ? store.telegram : `https://t.me/${store.telegram.replace('@', '')}`} className="hover:text-cyan-400">Contact Us</a>
              </p>
            )}
            {store.facebook && (
              <p>
                Facebook: <a href={store.facebook.startsWith('https://') ? store.facebook : `https://facebook.com/${store.facebook.replace('@', '')}`} className="hover:text-cyan-400">Follow Us</a>
              </p>
            )}
            {store.tiktok && (
              <p>
                TikTok: <a href={`https://tiktok.com/@${store.tiktok.replace('@', '')}`} className="hover:text-cyan-400">@{store.tiktok.replace('@', '')}</a>
              </p>
            )}
          </div>
          <p className="mt-4">© {new Date().getFullYear()} {store.name}. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );

  return (
    <div className="font-sans">
      {!showMenu ? <CoverPage /> : <MainContent />}
    </div>
  );
};

export default TemplateTwo;