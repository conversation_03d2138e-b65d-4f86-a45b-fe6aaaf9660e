'use client';

import { useState, useEffect } from 'react';
import axios from 'axios';
import Image from 'next/image';
import { FiEye } from 'react-icons/fi';
import { Store, ApiResponse } from '@/types';
import TemplatePreview from './TemplatePreview';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

interface StoreTemplateSelectorProps {
  currentStore: Store | null; // Pass the vendor's current store data
  onTemplateUpdate: (updatedStore: Store) => void; // Callback to update parent state
}

interface TemplateOption {
  id: string;
  name: string;
  description: string;
  previewImageUrl?: string; // Optional: for a visual preview
}

const AVAILABLE_TEMPLATES: TemplateOption[] = [
  { id: 'template1', name: 'Classic Clean', description: 'A simple and elegant layout.' },
  { id: 'template2', name: 'Modern Dark', description: 'A sleek, contemporary dark theme.' },
  { id: 'template3', name: 'Vibrant Grid', description: 'A colorful, grid-based product display.' },
  { id: 'template4', name: 'Minimalist Focus', description: 'Emphasizes product imagery with minimal distractions.' },
  { id: 'template5', name: 'Interactive Showcase', description: 'Engaging animations and interactive elements.' },
];

const StoreTemplateSelector: React.FC<StoreTemplateSelectorProps> = ({ currentStore, onTemplateUpdate }) => {
  const [selectedTemplateId, setSelectedTemplateId] = useState<string | null | undefined>(currentStore?.selected_template_id);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [previewTemplate, setPreviewTemplate] = useState<string | null>(null);

  useEffect(() => {
    setSelectedTemplateId(currentStore?.selected_template_id || 'template1');
  }, [currentStore]);

  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplateId(templateId);
  };

  const handleSubmit = async () => {
    if (!selectedTemplateId) {
      setError('Please select a template.');
      return;
    }
    if (!currentStore) {
        setError('Store data is not available.');
        return;
    }

    setIsLoading(true);
    setError(null);
    setSuccessMessage(null);

    try {
      // Assuming you have a way to get the auth token (e.g., from context, cookies)
      // For simplicity, this example doesn't implement token handling here.
      // In a real app, you'd pass the token in the Authorization header.
      const response = await axios.put<ApiResponse<Store>>(
        `${API_URL}/vendor/store/template`,
        { selected_template_id: selectedTemplateId },
        {
          withCredentials: true, // If using cookies for auth
          // headers: { 'Authorization': `Bearer YOUR_AUTH_TOKEN` } // If using Bearer token
        }
      );

      if (response.data && response.data.store) {
        onTemplateUpdate(response.data.store);
        setSuccessMessage(response.data.message || 'Template updated successfully!');
      } else {
        setError(response.data.message || 'Failed to update template. Invalid response.');
      }
    } catch (error) {
      console.error('Error updating template:', error);
      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'An unknown error occurred.' : 'An unknown error occurred.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 shadow sm:rounded-lg p-6">
      <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-white">
        Store Appearance
      </h3>
      <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
        Choose a template that best represents your brand.
      </p>

      {error && <p className="mt-2 text-sm text-red-600 dark:text-red-400">{error}</p>}
      {successMessage && <p className="mt-2 text-sm text-green-600 dark:text-green-400">{successMessage}</p>}

      <div className="mt-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {AVAILABLE_TEMPLATES.map((template) => (
          <div
            key={template.id}
            className={`rounded-lg border p-4 transition-all
                        ${selectedTemplateId === template.id
                            ? 'border-black dark:border-white ring-2 ring-black dark:ring-white bg-gray-50 dark:bg-gray-900'
                            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                        }`}
          >
            {template.previewImageUrl && (
              <div className="w-full h-32 bg-gray-200 dark:bg-gray-700 rounded-md mb-3 overflow-hidden">
                <Image src={template.previewImageUrl || ''} alt={template.name} width={300} height={150} className="w-full h-full object-cover" />
              </div>
            )}
            <h4 className="text-md font-semibold text-gray-800 dark:text-gray-100">{template.name}</h4>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">{template.description}</p>

            <div className="flex space-x-2">
              <button
                onClick={() => setPreviewTemplate(template.id)}
                className="flex-1 flex items-center justify-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <FiEye className="mr-2 h-4 w-4" />
                Preview
              </button>
              <button
                onClick={() => handleTemplateSelect(template.id)}
                className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  selectedTemplateId === template.id
                    ? 'bg-black dark:bg-white text-white dark:text-black'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
              >
                {selectedTemplateId === template.id ? 'Selected' : 'Select'}
              </button>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 flex justify-end">
        <button
          type="button"
          onClick={handleSubmit}
          disabled={isLoading || !selectedTemplateId || selectedTemplateId === currentStore?.selected_template_id}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {isLoading ? 'Saving...' : 'Save Template'}
        </button>
      </div>

      {/* Template Preview Modal */}
      {previewTemplate && currentStore && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setPreviewTemplate(null)}></div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

            <div className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <TemplatePreview
                templateId={previewTemplate}
                store={currentStore}
                onClose={() => setPreviewTemplate(null)}
                onSelect={(templateId) => {
                  handleTemplateSelect(templateId);
                  setPreviewTemplate(null);
                }}
                isSelected={selectedTemplateId === previewTemplate}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StoreTemplateSelector;
