'use client';

import React, { useState } from 'react';
import { FiX, FiExternalLink, FiEye } from 'react-icons/fi';
import { Store, Product } from '@/types';

// Import Template Components
import TemplateDefault from '@/components/store-templates/TemplateDefault';
import TemplateOne from '@/components/store-templates/TemplateOne';
import TemplateTwo from '@/components/store-templates/TemplateTwo';
import TemplateThree from '@/components/store-templates/TemplateThree';
import TemplateFour from '@/components/store-templates/TemplateFour';
import TemplateFive from '@/components/store-templates/TemplateFive';

interface TemplatePreviewProps {
  templateId: string;
  store: Store;
  products?: Product[];
  onClose: () => void;
  onSelect: (templateId: string) => void;
  isSelected?: boolean;
}

// Demo data for preview
const demoProducts: Product[] = [
  {
    id: 1,
    name: "Premium Coffee Beans",
    description: "Freshly roasted coffee beans from the mountains",
    price: 24.99,
    image_url: "https://images.unsplash.com/photo-1559056199-641a0ac8b55e",
    imageUrl: "https://images.unsplash.com/photo-1559056199-641a0ac8b55e",
    category: "Beverages",
    tags: ["coffee", "premium", "organic"],
    is_active: true,
    store_id: 1,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    contact_link: null
  },
  {
    id: 2,
    name: "Artisan Chocolate",
    description: "Handcrafted chocolate made with finest ingredients",
    price: 18.50,
    image_url: "https://images.unsplash.com/photo-1549007994-cb92caebd54b",
    imageUrl: "https://images.unsplash.com/photo-1549007994-cb92caebd54b",
    category: "Sweets",
    tags: ["chocolate", "artisan", "handmade"],
    is_active: true,
    store_id: 1,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    contact_link: null
  },
  {
    id: 3,
    name: "Organic Honey",
    description: "Pure organic honey from local beekeepers",
    price: 12.99,
    image_url: "https://images.unsplash.com/photo-1587049352846-4a222e784d38",
    imageUrl: "https://images.unsplash.com/photo-1587049352846-4a222e784d38",
    category: "Natural",
    tags: ["honey", "organic", "natural"],
    is_active: true,
    store_id: 1,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    contact_link: null
  }
];

const demoStore: Partial<Store> = {
  name: "Demo Store",
  description: "This is how your store will look with this template",
  logo_url: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43",
  cover_image_url: "https://images.unsplash.com/photo-1441986300917-64674bd600d8",
  telegram: "@demostore",
  whatsapp: "+1234567890",
  phone: "+****************",
  instagram: "@demostore",
  view_count: 1234
};

const TemplatePreview: React.FC<TemplatePreviewProps> = ({
  templateId,
  store,
  products = demoProducts,
  onClose,
  onSelect,
  isSelected = false
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Merge store data with demo data for preview
  const previewStore: Store = {
    ...demoStore,
    ...store,
    name: store.name || demoStore.name!,
    description: store.description || demoStore.description!,
    logo_url: store.logo_url || demoStore.logo_url!,
    cover_image_url: store.cover_image_url || demoStore.cover_image_url!
  } as Store;

  const renderTemplate = () => {
    // Fix the categories array to ensure it only contains strings (no null values)
    const categories = ['All', ...Array.from(new Set(
      products.map(p => p.category).filter((category): category is string => 
        typeof category === 'string' && category.trim() !== ''
      )
    ))];

    const templateProps = {
      store: previewStore,
      products: products,
      searchQuery: '',
      selectedCategory: 'All',
      handleContactClick: () => {},
      onSearchQueryChange: () => {},
      onSelectedCategoryChange: () => {},
      categories: categories, // Use the properly typed categories array
      isPreview: true
    };

    switch (templateId) {
      case 'default':
        return <TemplateDefault {...templateProps} />;
      case 'template1':
        return <TemplateOne {...templateProps} />;
      case 'template2':
        return <TemplateTwo {...templateProps} />;
      case 'template3':
        return <TemplateThree {...templateProps} />;
      case 'template4':
        return <TemplateFour {...templateProps} />;
      case 'template5':
        return <TemplateFive {...templateProps} />;
      default:
        return <TemplateDefault {...templateProps} />;
    }
  };

  const getTemplateName = (id: string) => {
    const names: Record<string, string> = {
      'default': 'Classic Template',
      'template1': 'Modern Blue',
      'template2': 'Elegant Cyan',
      'template3': 'Professional Teal',
      'template4': 'Vibrant Purple',
      'template5': 'Minimal Slate'
    };
    return names[id] || 'Unknown Template';
  };

  if (isFullscreen) {
    return (
      <div className="fixed inset-0 z-50 bg-white dark:bg-gray-900">
        <div className="absolute top-4 right-4 z-10 flex space-x-2">
          <button
            onClick={() => setIsFullscreen(false)}
            className="p-2 bg-black dark:bg-white text-white dark:text-black rounded-full hover:bg-gray-800 dark:hover:bg-gray-200 transition-colors"
          >
            <FiX className="h-5 w-5" />
          </button>
        </div>
        <div className="h-full overflow-auto">
          {renderTemplate()}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {getTemplateName(templateId)}
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Preview how your store will look
          </p>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => setIsFullscreen(true)}
            className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors"
            title="Fullscreen preview"
          >
            <FiExternalLink className="h-5 w-5" />
          </button>
          <button
            onClick={onClose}
            className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors"
          >
            <FiX className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Preview */}
      <div className="relative">
        <div className="h-96 overflow-hidden">
          <div className="transform scale-50 origin-top-left w-[200%] h-[200%]">
            {renderTemplate()}
          </div>
        </div>
        
        {/* Overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 transition-all flex items-center justify-center opacity-0 hover:opacity-100">
          <button
            onClick={() => setIsFullscreen(true)}
            className="bg-white dark:bg-gray-800 text-gray-900 dark:text-white px-4 py-2 rounded-lg shadow-lg flex items-center space-x-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <FiEye className="h-4 w-4" />
            <span>Full Preview</span>
          </button>
        </div>
      </div>

      {/* Actions */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={() => onSelect(templateId)}
          className={`w-full py-2 px-4 rounded-md font-medium transition-colors ${
            isSelected
              ? 'bg-black dark:bg-white text-white dark:text-black'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-600'
          }`}
        >
          {isSelected ? 'Selected' : 'Select Template'}
        </button>
      </div>
    </div>
  );
};

export default TemplatePreview;


