'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import axios from 'axios';

// Define types
export interface User {
  id: number;
  email: string;
  name: string;
  role: 'customer' | 'vendor' | 'admin';
  created_at: string;
  last_login: string | null;
  email_verified: boolean;
  profile_picture: string | null;
  google_id: boolean;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string, rememberMe: boolean) => Promise<void>;
  logout: () => Promise<void>;
  register: (name: string, email: string, password: string, role?: string) => Promise<void>;
  sendOTP: (email: string) => Promise<void>;
  verifyOTP: (email: string, otp: string) => Promise<User>;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (token: string, password: string) => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
  sendVerificationEmail: (email: string) => Promise<void>;
  verifyEmail: (token: string) => Promise<User>;
  setUser: (user: User | null) => void;
  isAuthenticated: boolean;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// API base URL
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

// Provider component
export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  // Configure axios to include credentials
  axios.defaults.withCredentials = true;

  // Check if user is logged in
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const response = await axios.get(`${API_URL}/auth/me`);
        setUser(response.data.user);
      } catch {
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Login function
  const login = async (email: string, password: string, rememberMe: boolean) => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.post(`${API_URL}/auth/login`, {
        email,
        password,
        remember_me: rememberMe
      });

      setUser(response.data.user);

      // Redirect based on user role
      if (response.data.user.role === 'admin') {
        router.replace('/admin/dashboard');
      } else if (response.data.user.role === 'vendor') {
        router.replace('/vendor/dashboard');
      } else {
        router.replace('/');
      }
    } catch (error) {
      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'Login failed' : 'Login failed');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    setLoading(true);

    try {
      await axios.post(`${API_URL}/auth/logout`);
      setUser(null);
      router.replace('/login');
    } catch (error) {
      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'Logout failed' : 'Logout failed');
    } finally {
      setLoading(false);
    }
  };

  // Register function
  const register = async (name: string, email: string, password: string, role: string = 'customer') => {
    setLoading(true);
    setError(null);

    try {
      await axios.post(`${API_URL}/auth/register`, {
        name,
        email,
        password,
        role
      });

      // Redirect to login page after successful registration
      router.replace('/login');
    } catch (error) {
      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'Registration failed' : 'Registration failed');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Send OTP function
  const sendOTP = async (email: string) => {
    setLoading(true);
    setError(null);

    try {
      await axios.post(`${API_URL}/auth/send-otp`, { email });
    } catch (error) {
      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'Failed to send OTP' : 'Failed to send OTP');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Verify OTP function
  const verifyOTP = async (email: string, otp: string): Promise<User> => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.post(`${API_URL}/auth/verify-otp`, { email, otp });
      return response.data.user;
    } catch (error) {
      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'Invalid OTP' : 'Invalid OTP');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Forgot password function
  const forgotPassword = async (email: string) => {
    setLoading(true);
    setError(null);

    try {
      await axios.post(`${API_URL}/auth/forgot-password`, { email });
    } catch (error) {
      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'Failed to send reset email' : 'Failed to send reset email');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Reset password function
  const resetPassword = async (token: string, password: string) => {
    setLoading(true);
    setError(null);

    try {
      await axios.post(`${API_URL}/auth/reset-password`, { token, password });
    } catch (error) {
      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'Failed to reset password' : 'Failed to reset password');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Update profile function
  const updateProfile = async (data: Partial<User>) => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.put(`${API_URL}/auth/profile`, data);
      setUser(response.data.user);
    } catch (error) {
      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'Failed to update profile' : 'Failed to update profile');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Send verification email function
  const sendVerificationEmail = async (email: string) => {
    setLoading(true);
    setError(null);

    try {
      await axios.post(`${API_URL}/auth/send-verification-email`, { email });
    } catch (error) {
      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'Failed to send verification email' : 'Failed to send verification email');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Verify email function
  const verifyEmail = async (token: string): Promise<User> => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.post(`${API_URL}/auth/verify-email`, { token });
      const verifiedUser = response.data.user;
      setUser(verifiedUser);
      return verifiedUser;
    } catch (error) {
      setError(axios.isAxiosError(error) ? error.response?.data?.message || 'Email verification failed' : 'Email verification failed');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const value = {
    user,
    loading,
    error,
    login,
    logout,
    register,
    sendOTP,
    verifyOTP,
    forgotPassword,
    resetPassword,
    updateProfile,
    sendVerificationEmail,
    verifyEmail,
    setUser,
    isAuthenticated: !!user
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);

  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }

  return context;
};
