import { useState, useEffect } from 'react';
import {
  signInWithPopup,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  User as FirebaseUser
} from 'firebase/auth';
import { auth, googleProvider } from '@/lib/firebase';

interface UseFirebaseAuthReturn {
  user: FirebaseUser | null;
  loading: boolean;
  error: string | null;
  signInWithGoogle: () => Promise<FirebaseUser | null>;
  signOut: () => Promise<void>;
  getIdToken: () => Promise<string | null>;
}

export const useFirebaseAuth = (): UseFirebaseAuthReturn => {
  const [user, setUser] = useState<FirebaseUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!auth) {
      setLoading(false);
      return;
    }

    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setUser(user);
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const signInWithGoogle = async (): Promise<FirebaseUser | null> => {
    if (!auth || !googleProvider) {
      setError('Firebase is not properly configured');
      return null;
    }

    try {
      setLoading(true);
      setError(null);

      const result = await signInWithPopup(auth, googleProvider);
      return result.user;
    } catch (error: unknown) {
      console.error('Google sign-in error:', error);
      setError(error instanceof Error ? error.message : 'Failed to sign in with Google');
      return null;
    } finally {
      setLoading(false);
    }
  };

  const signOut = async (): Promise<void> => {
    if (!auth) {
      setError('Firebase is not properly configured');
      return;
    }

    try {
      await firebaseSignOut(auth);
      setUser(null);
    } catch (error: unknown) {
      console.error('Sign out error:', error);
      setError(error instanceof Error ? error.message : 'Failed to sign out');
    }
  };

  const getIdToken = async (): Promise<string | null> => {
    if (!user) return null;

    try {
      return await user.getIdToken();
    } catch (error: unknown) {
      console.error('Get ID token error:', error);
      setError(error instanceof Error ? error.message : 'Failed to get ID token');
      return null;
    }
  };

  return {
    user,
    loading,
    error,
    signInWithGoogle,
    signOut,
    getIdToken,
  };
};
