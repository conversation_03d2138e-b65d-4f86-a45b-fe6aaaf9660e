export interface User {
  id: number;
  email: string;
  name: string;
  role: 'customer' | 'vendor' | 'admin';
  // Add other user-specific fields
}

export interface Store {
  id: number;
  name: string;
  slug: string;
  description: string | null;
  logo_url: string | null;
  cover_image_url: string | null; // Added for cover page functionality
  telegram: string | null;
  whatsapp: string | null;
  phone: string | null;
  instagram: string | null;
  facebook: string | null;
  tiktok: string | null;
  user_id: number;
  created_at: string;
  updated_at: string;
  selected_template_id?: string | null; // Ensure this is present
  view_count?: number; // Added for view tracking
}

export interface Product {
  id: number;
  name: string;
  description: string | null;
  price: number;
  image_url: string | null; // Original field from backend model, used by TemplateDefault
  imageUrl?: string | null;  // Normalized field for TemplateOne-Five (populated by backend /menu or similar)
  category: string | null;
  tags: string[];
  is_active: boolean;
  store_id: number;
  created_at: string;
  updated_at: string;
  contact_link: string | null;
}

// API Response types (can be expanded)
export interface ApiResponse<T = unknown> {
  message?: string;
  error?: string;
  // Specific data structures based on endpoint
  user?: User;
  store?: Store;
  stores?: Store[];
  products?: Product[]; // Used by /store/:slug if it returns products directly
  menu_items?: Product[]; // Used by /vendor/menu which normalizes imageUrl
  // Add other common API response fields
  data?: T; // Generic data field
}
