/**
 * Utility functions for handling image paths
 */

// API base URL without the /api suffix
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL
  ? process.env.NEXT_PUBLIC_API_URL.replace(/\/api$/, '')
  : 'http://localhost:5000';

// MinIO URL - direct access (force HTTPS)
const MINIO_URL = process.env.NEXT_PUBLIC_MINIO_URL
  ? process.env.NEXT_PUBLIC_MINIO_URL.replace(/^http:/, 'https:')
  : 'https://**************:9000';

// Use this for proxied access through the backend
const PROXY_URL = `${API_BASE_URL}/api/upload/serve`;

/**
 * Ensures that image paths are properly formatted for Next.js Image component
 * Next.js requires image paths to start with a leading slash or be absolute URLs
 */
export function getImagePath(src: string): string {
  if (!src) return '';

  let result = '';

  // If it's already a MinIO URL, convert it to use the proxy
  if (src.includes('**************:9000')) {
    const filename = src.split('/').pop();
    if (filename) {
      result = `${PROXY_URL}/${filename}`;
      logImagePathTransformation(src, result, 'MinIO direct URL converted to proxy');
      return result;
    }
  }

  // If it's already an absolute URL and not a MinIO URL, return as is
  if ((src.startsWith('http://') || src.startsWith('https://'))
      && !src.includes('**************:9000')) {
    result = src;
    logImagePathTransformation(src, result, 'External absolute URL');
    return result;
  }

  // Handle protocol-relative URLs
  if (src.startsWith('//')) {
    result = `https:${src}`;
    logImagePathTransformation(src, result, 'Protocol-relative URL fixed');
    return result;
  }

  // If it starts with 'uploads/' or 'images/', add a leading slash
  if (src.startsWith('uploads/') || src.startsWith('images/')) {
    // Extract the filename if it has a UUID pattern
    const filename = src.split('/').pop();
    if (filename && filename.match(/[0-9a-f]{32}_/)) {
      result = `${PROXY_URL}/${filename}`;
      logImagePathTransformation(src, result, 'Path with UUID pattern');
      return result;
    }

    // Otherwise, add a leading slash for Next.js
    result = `/${src}`;
    logImagePathTransformation(src, result, 'Added leading slash for Next.js');
    return result;
  }

  // Clean the path (but don't remove leading slashes for Next.js)
  const cleanPath = src.startsWith('/') ? src : src.replace(/^\/+/, '');

  // If it contains a UUID pattern, it's likely a MinIO file
  if (cleanPath.match(/[0-9a-f]{32}_/)) {
    const filename = cleanPath.split('/').pop();
    result = `${PROXY_URL}/${filename}`;
    logImagePathTransformation(src, result, 'UUID-based filename');
    return result;
  }

  // Handle uploads directory
  if (cleanPath.includes('uploads/')) {
    const filename = cleanPath.split('/').pop();
    result = `${PROXY_URL}/${filename}`;
    logImagePathTransformation(src, result, 'Uploads directory');
    return result;
  }

  // For all other cases, ensure it has a leading slash or use the proxy
  if (cleanPath.startsWith('/')) {
    result = cleanPath;
  } else {
    result = `/${cleanPath}`;
  }

  logImagePathTransformation(src, result, 'Ensured leading slash for Next.js');
  return result;
}

/**
 * Gets the correct URL for uploaded files
 * Ensures paths are compatible with Next.js Image component
 */
export function getUploadUrl(path: string): string {
  if (!path) return '';

  // Handle protocol-relative URLs
  if (path.startsWith('//')) {
    return `https:${path}`;
  }

  // If it's already an absolute URL and not a MinIO URL, return as is
  if ((path.startsWith('http://') || path.startsWith('https://'))
      && !path.includes('**************:9000')) {
    return path;
  }

  // For MinIO URLs, use the proxy
  if (path.includes('**************:9000')) {
    const filename = path.split('/').pop();
    return `${PROXY_URL}/${filename}`;
  }

  // If it starts with 'uploads/' or 'images/', add a leading slash for Next.js
  if (path.startsWith('uploads/') || path.startsWith('images/')) {
    // Extract the filename if it has a UUID pattern
    const filename = path.split('/').pop();
    if (filename && filename.match(/[0-9a-f]{32}_/)) {
      return `${PROXY_URL}/${filename}`;
    }

    // Otherwise, add a leading slash for Next.js
    return `/${path}`;
  }

  // Clean the path (but preserve leading slashes for Next.js)
  const cleanPath = path.startsWith('/') ? path : path.replace(/^\/+/, '');

  // If it contains a UUID pattern, it's likely a MinIO file
  if (cleanPath.match(/[0-9a-f]{32}_/)) {
    const filename = cleanPath.split('/').pop();
    return `${PROXY_URL}/${filename}`;
  }

  // Handle uploads directory
  if (cleanPath.includes('uploads/')) {
    const filename = cleanPath.split('/').pop();
    return `${PROXY_URL}/${filename}`;
  }

  // For all other cases, ensure it has a leading slash
  if (cleanPath.startsWith('/')) {
    return cleanPath;
  } else {
    return `/${cleanPath}`;
  }
}

/**
 * Gets a direct MinIO URL for a file
 */
export function getMinioUrl(path: string): string {
  if (!path) return '';

  // If it's already a MinIO URL, return as is
  if (path.includes('**************:9000')) {
    return path;
  }

  // Extract the filename
  const filename = path.split('/').pop();
  if (!filename) return '';

  // Return the MinIO URL
  return `${MINIO_URL}/uploads/${filename}`;
}

/**
 * Debug utility to log image path transformations
 */
export function logImagePathTransformation(
  originalPath: string,
  transformedPath: string,
  context: string = ''
): void {
  if (process.env.NODE_ENV === 'development') {
    console.log(`Image path transformation (${context}):`, {
      from: originalPath,
      to: transformedPath
    });
  }
}

